#include "totvs.ch"

CLASS FluigDocument FROM FWoAuth1Fluig
    DATA oClientFluig    AS OBJECT
    DATA cConsumerKey    AS Character 
    DATA cConsumerSecret AS Character 
    DATA cAccessToken    AS Character 
    DATA cTokenSecret    AS Character 
    DATA cHost           AS Character 
	
    PUBLIC METHOD New(cConsumerKey,cConsumerSecret,cAccessToken,cTokenSecret,cHost) CONSTRUCTOR
    PUBLIC METHOD UpLoad(cFolderId,cDescription,cName,cBase64Content,cDocumentExtension,cEndPoint) 
    PUBLIC METHOD CreateFolder(cParentId,cDescription,cEndPoint) 
    PUBLIC METHOD GetFolderIdAno(cParentId,cDescription,cEndPoint)
    PUBLIC METHOD GetFolderIdAnoMes(cParentId,cDescription,cEndPoint)
    PUBLIC METHOD GetFolderIdFilial(cParentId,cDescription,cEndPoint)
    PUBLIC METHOD UpdateParam()

ENDCLASS

METHOD New(cCsKey,cCsSecret,cAcToken,cTkSecret,cHost) CLASS FluigDocument
Default cCsKey    := Alltrim(GetMv("TI_ECMCSKY",,"fluigApi"))
Default cCsSecret := Alltrim(GetMv("TI_ECMCSAC",,"fluigApi"))
Default cAcToken  := Alltrim(GetMv("TI_ECMACTK",,"a6dc3984-7063-44d2-b424-22cf871ef2bf"))
Default cTkSecret := Alltrim(GetMv("TI_ECMTKSE",,"4bbcd53c-4803-4c97-9c0e-3bf1d2e9ebf63b0de469-bf8c-4000-87ef-b0399d416943"))
Default cHost     := Alltrim(GetMv("TI_ECMWSRT",,"https://prefluig18.totvs.com.br"))

    ::cConsumerKey     := cCsKey
    ::cConsumerSecret  := cCsSecret
    ::cAccessToken     := cAcToken
    ::cTokenSecret     := cTkSecret
    ::cHost            := cHost
    _Super:New(::cConsumerKey,::cConsumerSecret,::cHost,"")
    //-------------------------------------------------------
    //Define o Token e o SecretToken.
    //-------------------------------------------------------
    ::SetToken(::cAccessToken)
    ::SetSecretToken(::cTokenSecret)

Return

METHOD UpdateParam() CLASS FluigDocument

    _Super:UpdateParam()
    ::cNonce := SubStr(Sha1(FWUUIDV4()),1,32)
    ::aParametros[3] := {"oauth_nonce",self:cNonce}

Return



METHOD UpLoad(cFolderId,cDescription,cName,cBase64Content,cDocumentExtension,cEndPoint) CLASS FluigDocument
Local oJson := JsonObject():New()
Local cBody
Local cRet
Local cHeadOut := ""

Default cEndPoint := Alltrim(GetMv("TI_ECMURLD",,"/api/public/social/community/document"))

    oJson['folderId']    := cFolderId
    oJson['description'] := cDescription
    oJson['name']        := cName
    oJson['base64media'] := cBase64Content
    oJson['documentExtension'] := cDocumentExtension

    cBody := oJson:ToJson()
    FreeObj(oJson)

    cRet := ::POST( ::cHost  + cEndPoint,, cBody,,@cHeadOut )
    VarInfo("cHeadOut UpLoad => ",cHeadOut)

Return cRet

METHOD CreateFolder(cParentId,cDescription,cEndPoint) CLASS FluigDocument
Local oJson := JsonObject():New()
Local cBody
Local cRet
Local cHeadOut := ""

Default cEndPoint := Alltrim(GetMv("TI_ECMURLF",,"/api/public/ecm/document/createFolder"))

    oJson['parentId']    := cParentId
    oJson['description'] := cDescription
    
    cBody := oJson:ToJson()
    FreeObj(oJson)

    cRet := ::POST( ::cHost  + cEndPoint,, cBody )
    VarInfo("cHeadOut CreateFolder => ",cHeadOut)

Return cRet

METHOD GetFolderIdAno(cParentId,cDescription,cEndPoint) CLASS FluigDocument
Local cRet      := ""
Local cAliasTrb := GetNextAlias()
Local cTabPastas:= "FLDFLG"
Local oResponse	:= JsonObject():New()
Local cResponse

    BeginSql Alias cAliasTrb
		SELECT ZX5_CHAVE2
		  FROM %table:ZX5%
		 WHERE ZX5_FILIAL = %exp:Left(cDescription,4)%
		 	   AND ZX5_TABELA=%exp:cTabPastas%
			   AND ZX5_CHAVE=' '
			   AND %notDel%
	EndSql

    // Se não achou a pasta da Filial/Cria no cParentId
	If (cAliasTrb)->(Eof())
		
		cResponse := ::CreateFolder(cParentId,cDescription,cEndPoint)
        cRet := cResponse

		If !Empty(cResponse) .And. oResponse:FromJson( cResponse ) == Nil

            If oResponse:HasProperty("content") .And. oResponse['content']:HasProperty("id")

                RecLock("ZX5",.T.)
                    ZX5->ZX5_FILIAL := Left(cDescription,4)
                    ZX5->ZX5_TABELA := cTabPastas
                    ZX5->ZX5_CHAVE2 := cValToChar(oResponse['content']['id'])
                    ZX5->ZX5_DESCRI := "ParentId: " + cParentId
                MsUnlock()

                cRet := ZX5->ZX5_CHAVE2
            
            EndIf

		EndIf

	Else
        cRet := (cAliasTrb)->ZX5_CHAVE2
    EndIf

    (cAliasTrb)->(DBCloseArea())
    FreeObj(oResponse)

Return Alltrim(cRet)

METHOD GetFolderIdAnoMes(cParentId,cDescription,cEndPoint) CLASS FluigDocument
Local cRet      := ""
Local cAliasTrb := GetNextAlias()
Local cTabPastas:= "FLDFLG"
Local oResponse	:= JsonObject():New()
Local cResponse
Local cFolderName

   BeginSql Alias cAliasTrb
        SELECT ZX5_CHAVE2
        FROM %table:ZX5%
        WHERE ZX5_FILIAL = %exp:cDescription%
            AND ZX5_TABELA=%exp:cTabPastas%
            AND ZX5_CHAVE=' '
            AND %notDel%
    EndSql

    // Se não achou a pasta da Filial/Cria no cParentId
	If (cAliasTrb)->(Eof())
		cFolderName := MesExtenso(Month(Stod(cDescription+'01')))

		cResponse := ::CreateFolder(cParentId,cFolderName,cEndPoint)
        cRet := cResponse

		If !Empty(cResponse) .And. oResponse:FromJson( cResponse ) == Nil
            
            If oResponse:HasProperty("content") .And. oResponse['content']:HasProperty("id")

                RecLock("ZX5",.T.)
                    ZX5->ZX5_FILIAL := cDescription
                    ZX5->ZX5_TABELA := cTabPastas
                    ZX5->ZX5_CHAVE  := ' '
                    ZX5->ZX5_CHAVE2 := cValToChar(oResponse['content']['id'])
                    ZX5->ZX5_DESCRI := "ParentId: " + cParentId
                MsUnlock()

                cRet := ZX5->ZX5_CHAVE2
            
            EndIf

		EndIf

	Else
        cRet := (cAliasTrb)->ZX5_CHAVE2
    EndIf

    (cAliasTrb)->(DBCloseArea())
    FreeObj(oResponse)

Return Alltrim(cRet)

METHOD GetFolderIdFilial(cParentId,cAnoMes,cDescription,cEndPoint) CLASS FluigDocument
Local cRet      := ""
Local cAliasTrb := GetNextAlias()
Local cTabPastas:= "FLDFLG"
Local oResponse	:= JsonObject():New()
Local cResponse
Local cFolderName

    BeginSql Alias cAliasTrb
		SELECT ZX5_CHAVE2
		  FROM %table:ZX5%
		 WHERE ZX5_FILIAL = %exp:cAnoMes%
		 	   AND ZX5_TABELA=%exp:cTabPastas%
			   AND ZX5_CHAVE=%exp:cDescription%
			   AND %notDel%
	EndSql

    // Se não achou a pasta da Filial/Cria no cParentId
	If (cAliasTrb)->(Eof())
		cFolderName := "Filial_"+cDescription
        
        cResponse := ::CreateFolder(cParentId,cFolderName,cEndPoint)
        cRet := cResponse

		If !Empty(cResponse) .And. oResponse:FromJson( cResponse ) == Nil

            If oResponse:HasProperty("content") .And. oResponse['content']:HasProperty("id")
                RecLock("ZX5",.T.)
                    ZX5->ZX5_FILIAL := cAnoMes
                    ZX5->ZX5_TABELA := cTabPastas
                    ZX5->ZX5_CHAVE  := cDescription
                    ZX5->ZX5_CHAVE2 := cValToChar(oResponse['content']['id'])
                    ZX5->ZX5_DESCRI := "ParentId: " + cParentId
                MsUnlock()

                cRet := ZX5->ZX5_CHAVE2

            EndIf

		EndIf

	Else
        cRet := (cAliasTrb)->ZX5_CHAVE2
    EndIf

    (cAliasTrb)->(DBCloseArea())
    FreeObj(oResponse)

Return Alltrim(cRet)