#include "totvs.ch"

//TIADMVIN-3138
User Function Tgcvxc05
Return

Class Tgcvxc05

	Data cContrato
	Data cRevisa
	Data cPlanilha
	Data cItem
	Data cProduto
	Data cDescPro
    Data cTipRec
    Data cTipCnt
    Data dVencto

	Data cCondPG
	Data cCodISS
	Data cNotaSe
	Data cMoeda
	Data cMascCC
	Data cStatRM
	Data cGrupo
	Data cUniNeg
	Data cGU

	Data cFreFat
	Data cTpPago
	Data nDiaFat 

	Data cPropos
	Data cRevPro
	Data cItmPro
	Data cFoldPr


	Data cCliente
	Data cLoja
	Data cDescCli

	Data dDtIni
	Data dConIni
	Data dConFin

	Data cAnoMesI
	Data cAnoMesF
	Data cAnoMesC
	Data cAnoMesTE
	Data cAnoMesTS
	Data cAnoMesO

    Data cCtrOrig
    Data cCtrDest

	Data nQtdMes
	Data nPeriodico
	Data cPeriodico
	Data cRatHist

	Data cAMAtu
	Data nMesAtu

	Data nQuant
	Data nQtdRev
	Data nVlrUniCtr
	Data nVlrTotCtr

	Data nQtdTraE
	Data nQtdTraS


	Data nImpostC
	Data cModImpC

	Data cCmpFIni       //Competencia de inicio de faturamento do contrato
	Data cLinRec
	Data nPercAudi


	Data aoCroCmp
	Data aSituac
	Data cSitIni 
	Data cSituac
	Data cDescSitu
	Data cSitAtiAnt

	Data nCarenVnd
	Data cCarIniVnd
	Data cCarFinVnd

	
	Data aCliente
	Data aCancela
	Data aCarencia
	Data aBonifica
	Data aReajuste
	Data aAjustFin
	Data aP68Intera
	Data aFinanceiro
	Data aTransf
	Data aCtrlPer
	DAta aBilling
	Data aRoyalties
	Data aReajExtra
	Data aTroca

	Data aEstruCSV
	Data cArqCSV
	Data lSetorPub
	Data lCorpora
	Data lBilling
	Data lRoyalties

	Data lDetPh5
    Data aAMAtu
    Data aMsgErro
	Data aExcecao
	Data aHistoric
	Data aPHG
	Data aCNCMain
	Data oModelo
	Data lModelo
	
	Data lNovTrat
	

	Method New()
	Method Destroy() 
    Method SetContrato(cContrato, cRevisa, cPlanilha, cItem, cProduto, cDescPro, cTipRec, cTipCnt, dVencto)
	Method SetCliente(cCliente, cLoja, cDescCli, lSetorPub)
	Method SetCarenVnd(nCarenVnd, cCarIniVnd, cCarFinVnd)
	Method SetCNB(cCondPG, cCodISS, cNotaSe, cMoeda, cMascCC, cStatRM, cGrupo, cUniNeg, cGU)
	Method SetModelo(oModelo) 
	Method SetProposta(cPropos, cRevPro, cItmPro, cFoldPr)
	Method AnoMes(nMes) 
	Method Cria()
	Method Calc()
	Method ReCalc()
	Method SetCalcFin()
	Method SetSituac()
	Method SetReajuste()
	Method SetAjustFin()
	Method SetVlIntera()
	
	Method SetCancela()
    Method SetTroca()
    Method SetAberto()
	Method OpenBilling(cAnoMes, nx)
    Method AtuSituac()
    Method RetPH5(nLinha, cNameCpo)
	Method DescCondic(nPeriodico)
	Method DescSitu(cSituação)
	Method Salva(aAMSave)
	Method RetPerRat() 
	Method CountVlr(cAtrib)
	Method IsIgual(cAtrib)
	Method RetObjCmp(cAMProc)
	Method Html()
	Method HtmlParIni()
	Method HtmlCabec()
	Method HtmlTitulo(cTitulo)
	Method HtmlLinha(cTitulo, cAtrib, lVerCalc, cCor)
	Method HtmlDet(cTitulo, cAtrib, lVerCalcF, cCor)
	Method GravaCSV(cArquivo)
	Method EstruCSV()
	Method CabecCSV()
	Method LinhaCSV()
	Method SelCmp(aAMSave)
	Method FreeChild()
	Method CarregaGetMV()
	
EndClass

Method New() Class Tgcvxc05
	::dDtIni     := Date()
	::dConIni    := ctod("")
	::dConFin    := ctod("")

	::cAnoMesI   := ""
	::cAnoMesF   := ""
	::cAnoMesC   := ""
	::cAnoMesTE  := ""
	::cAnoMesTS  := ""
	::cAnoMesO   := ""

    ::cCtrOrig   := ""
    ::cCtrDest   := ""
	::cCmpFIni   := ""
	::cLinRec    := ""

	::cFreFat    := ""
	::cTpPago    := ""
	::nDiaFat    := 0

	::nPercAudi  := 0

	::nQtdMes    := 1
	::nPeriodico := 1
	::cPeriodico := ""
	::cRatHist   := ""
	::aoCroCmp   := {}

	::aSituac    := {}
	::aCliente	 := {}
	::aCancela   := {}
	::aCarencia  := {}
	::aBonifica  := {}
	::aReajuste  := {}
	::aAjustFin  := {}
	::aP68Intera := {}
	::aFinanceiro:= {}
	::aTransf    := {}
	::aCtrlPer   := {}
	::aBilling   := {}
	::aRoyalties := {}
	::aReajExtra := {}
	::aTroca     := {}

	::cAMAtu     := ""
	::nQuant     := 0
	::nQtdRev    := 0
	::nVlrUniCtr := 0
	::nVlrTotCtr := 0

	::nQtdTraE   := 0
	::nQtdTraS   := 0
	::nImpostC   := 1
	::cModImpC   := ""

	::aEstruCSV  := Self:EstruCSV()
	::cSitIni    := ""
	::cSituac    := ""
	::cDescSitu  := ""
	::cSitAtiAnt := ""
	::lSetorPub  := .F.
	::lCorpora   := .F.
    ::lBilling   := .F.
    ::lRoyalties := .F.
	::lDetPh5    := .F.
	::aAMAtu     := {}
    ::nMesAtu    := 0
    ::aMsgErro   := {}
	::aExcecao   := {}    
	::aHistoric  := {}
	::aPHG       := {} 
	::aCNCMain   := {}
	Self:CarregaGetMV()	
Return

Method Destroy() Class Tgcvxc05
	
	aSize(::aoCroCmp   , 0)
	aSize(::aSituac    , 0)
	aSize(::aCliente   , 0)	
	aSize(::aCancela   , 0)
	aSize(::aCarencia  , 0)
	aSize(::aBonifica  , 0)
	aSize(::aReajuste  , 0)
	aSize(::aAjustFin  , 0)
	aSize(::aP68Intera , 0)
	aSize(::aFinanceiro, 0)
	aSize(::aTransf    , 0)
	aSize(::aCtrlPer   , 0)
	aSize(::aBilling   , 0)
	aSize(::aRoyalties , 0)
	aSize(::aReajExtra , 0)
	aSize(::aTroca     , 0)
	aSize(::aEstruCSV  , 0)
	aSize(::aAMAtu     , 0)
    aSize(::aMsgErro   , 0)
	aSize(::aExcecao   , 0)    
	aSize(::aHistoric  , 0)
	aSize(::aPHG       , 0) 
	aSize(::aCNCMain   , 0)

	::aoCroCmp    := Nil
	::aSituac     := Nil
	::aCliente    := Nil
	::aCancela    := Nil
	::aCarencia   := Nil
	::aBonifica   := Nil
	::aReajuste   := Nil
	::aAjustFin   := Nil
	::aP68Intera  := Nil
	::aFinanceiro := Nil
	::aTransf     := Nil
	::aCtrlPer    := Nil
	::aBilling    := Nil
	::aRoyalties  := Nil
	::aReajExtra  := Nil
	::aTroca      := Nil
	::aEstruCSV   := Nil
	::aAMAtu      := Nil
	::aMsgErro    := Nil
	::aExcecao    := Nil
	::aHistoric   := Nil
	::aPHG     := Nil
	::aCNCMain    := Nil


	::oModelo    := Nil
	::lModelo    := .F.
Return

Method SetContrato(cContrato, cRevisa, cPlanilha, cItem, cProduto, cDescPro, cTipRec, cTipCnt, dVencto) Class Tgcvxc05
	::cContrato := cContrato
	::cRevisa   := cRevisa
	::cPlanilha := cPlanilha
	::cItem     := cItem
	::cProduto  := cProduto
	::cDescPro  := cDescPro
    ::cTipRec   := cTipRec
    ::cTipCnt   := cTipCnt
    ::dVencto   := dVencto
Return

Method SetCliente(cCliente, cLoja, cDescCli, lSetorPub) Class Tgcvxc05
	::cCliente  := cCliente
	::cLoja     := cLoja
	::cDescCli  := cDescCli
	::lSetorPub := lSetorPub
Return


Method SetCarenVnd(nCarenVnd, cCarIniVnd, cCarFinVnd) Class Tgcvxc05
	::nCarenVnd  := nCarenVnd
	::cCarIniVnd := cCarIniVnd
	::cCarFinVnd := cCarFinVnd

Return

Method SetCNB(cCondPG, cCodISS, cNotaSe, cMoeda, cMascCC, cStatRM, cGrupo, cUniNeg, cGU, cFreFat, cTpPago, nDiafat) Class Tgcvxc05
	::cCondPG := cCondPG
	::cCodISS := cCodISS
	::cNotaSe := cNotaSe
	::cMoeda  := cMoeda
	::cMascCC := cMascCC
	::cStatRM := cStatRM
	::cGrupo  := cGrupo
	::cUniNeg := cUniNeg
	::cGU     := cGU

	Default cFreFat := "1"
	Default cTpPago := "1"
	Default nDiafat := 0

	If Empty(cFreFat)
		::cFreFat := "1" //Default Janela
	Else
		::cFreFat := cFreFat
	EndIf 

	If Empty(cTpPago)
		::cTpPago := "1" //Default Pos Pagamento
	Else
		::cTpPago := cTpPago
	EndIf

	::nDiaFat := nDiafat

Return

Method SetModelo(oModelo) Class Tgcvxc05
	::oModelo    := oModelo
	::lModelo    := .T.
Return

Method SetProposta(cPropos, cRevPro, cItmPro, cFoldPr) Class Tgcvxc05
	::cPropos := cPropos
	::cRevPro := cRevPro
	::cItmPro := cItmPro
	::cFoldPr := cFoldPr


Return

Method AnoMes(nMes) Class Tgcvxc05 
	Local cMesAno := ''
	Local cAnoMes := ''

	cMesAno    := StrZero(Month(::dDtIni), 2) + "/" + Strzero(Year(::dDtIni), 4)
	If nMes > 1
		cMesAno  := AdicMes(cMesAno, nMes - 1)
	EndIf
	cAnoMes    := Right(cMesAno, 4) + Left(cMesAno, 2)

return cAnoMes

Method Cria() Class Tgcvxc05
	Local nx       :=0
	Local nPerRat  := 0
	Local lExcecao := .f.
	::aoCroCmp := Array(::nQtdMes)

	::cPeriodico  := Self:DescCondic(::nPeriodico)
	::cDescSitu   := Self:DescSitu(::cSituac)
	For nx:= 1 to ::nQtdMes
		If ::cTipRec == '2' 
			If CmpToAM(::cCmpFIni) > self:AnoMes(nX)
				::nQtdMes := nX - 1
				Exit
			EndIf
		EndIf 

		::aoCroCmp[nX] := Tgcvxc06():New(Self, nX)
		::aoCroCmp[nX]:nPeriodico  := ::nPeriodico
		::aoCroCmp[nX]:cPeriodico  := ::Self:DescCondic(::nPeriodico)
		::aoCroCmp[nX]:cSituac     := ::cSituac
        ::aoCroCmp[nX]:cDescSitu   := Self:DescSitu(::cSituac)
        ::aoCroCmp[nX]:lFinalizado := .F.

		lExcecao := .f.
		nPerRat := Self:RetPerRat(::aoCroCmp[nX]:cAnoMes, @lExcecao) 
		::aoCroCmp[nX]:nPerRat     := nPerRat
		::aoCroCmp[nX]:cRatHist    := ::cRatHist
		::aoCroCmp[nX]:lExcecao    := lExcecao

	Next

Return


Method Calc() Class Tgcvxc05
	Local nx:=0

	Self:SetCalcFin()
	Self:SetSituac()
	Self:SetReajuste()
	Self:SetAjustFin()
	Self:SetVlIntera()
	
    Self:SetCancela()
    Self:SetTroca()
    Self:SetAberto()
    Self:AtuSituac()
	
	For nx:= 1 to ::nQtdMes 

		If Empty(::aoCroCmp[nX]:nPerRat)
			::aoCroCmp[nX]:cAcao := "Excluir Cronograma"
			Loop
		EndIf
		::aoCroCmp[nX]:lRecalc := .F.
		::aoCroCmp[nX]:LoadFinCmp() 
		::aoCroCmp[nX]:Calc()
	Next

Return

Method ReCalc() Class Tgcvxc05
	Local nx      := 1
	Local nPerRat := 0
	

    Self:SetAberto()

	For nx:= 1 to ::nQtdMes 
		::aoCroCmp[nX]:ZeraRecalc()
		nPerRat := Self:RetPerRat(::aoCroCmp[nX]:cAnoMes) 
		::aoCroCmp[nX]:nPerRat     := nPerRat
		::aoCroCmp[nX]:cRatHist    := ::cRatHist
		
		If Empty(nPerRat)
			::aoCroCmp[nX]:cAcao := "Excluir Cronograma"
			Loop
		EndIf
		::aoCroCmp[nX]:LoadFinCmp() 
		::aoCroCmp[nX]:Calc()
		::aoCroCmp[nX]:lRecalc := .T.
	Next
	
Return


Method SetCalcFin() Class Tgcvxc05
	Local cAMFIni  := CmptoAM(::cCmpFIni)
	Local cAnoMes  := ""
	Local nx       := 0
	Local ny       := 0
	Local nCount    := ::nPeriodico
	Local nPer      := ::nPeriodico
	Local nPerAnt   := ::nPeriodico
	Local nMesCiclo := 1
	Local aCiclo    := {}


	aadd(aCiclo, {cAMFIni, 1})
	// controle de faturamento de periodico com base na aCtrlPer carregada da tabela PHD
	For nx:= 1 to len(::aCtrlPer)
		cAMAux := ::aCtrlPer[nx, 1]
		nDif   := AMCalcDif(cAMFIni, cAMAux) + 1

		If Mod(nDif, nPer) == 0
			nMesCiclo := nPer
		Else
			nMesCiclo := Mod(nDif, nPer)
		EndIf
		cAMIniCiclo := CmpToAM(AdicMes(AMtoCmp(cAMAux), (nMesCiclo - 1) * -1 ))
		aadd(aCiclo, {cAMIniCiclo, nMesCiclo})
	Next

	// definição do meses de faturamento
	For ny := 1 to len (aCiclo)
		cAMIniCiclo := aCiclo[ny, 1]
		nMesCiclo   := aCiclo[ny, 2]
		nCount      := ::nPeriodico

		For nx:= 1 to ::nQtdMes
			nPer    := ::aoCroCmp[nX]:nPeriodico
			cAnoMes := ::aoCroCmp[nX]:cAnoMes
			If cAnoMes < cAMFIni
				::aoCroCmp[nX]:lPeriodo := .T. // dentro da carencia
				::aoCroCmp[nX]:lIniPer  := .F.
			Else
				If cAnoMes < cAMIniCiclo
					Loop
				EndIf
				::aoCroCmp[nX]:lPeriodo := .F.
				::aoCroCmp[nX]:lIniPer := .F.
				If nCount == nPer - (nMesCiclo - 1)
					::aoCroCmp[nX]:lPeriodo := .T.
				EndIf
				If nCount == nPer
					::aoCroCmp[nX]:lIniPer := .T.
				EndIf
				nCount--
				If nCount == 0 .or. nPer <> nPerAnt
					nCount  := nPer
					nPerAnt := nPer
				EndIf
			EndIf
		Next
	Next

Return

Method SetSituac() Class Tgcvxc05
	Local nx         := 0
	Local ny         := 0
	Local cAMRev     := ""
	Local cSituac    := ""
	Local nQuant     := 0
	Local nVlrUni    := 0
	Local cCondic    := ""
	Local nPeriodico := 0
	Local cRevisa    := ""

    //identificar a situação iniciai
	::cSitIni  := ::aSituac[1, 2]
	nQuant     := ::aSituac[1, 3]
	nVlrUni    := ::aSituac[1, 4]
	cCondic    := ::aSituac[1, 5]
	nPeriodico := Val(::aSituac[1, 5]) + 1

    For nx:= 1 to len(::aSituac)
        If Empty(::aSituac[nx, 1])
            ::cSitIni := ::aSituac[nx, 2]   
            Loop
        else
            Exit
        EndIf 
    Next

    For ny := 1 to ::nQtdMes
        ::aoCroCmp[ny]:cSituac    := ::cSitIni
		::aoCroCmp[ny]:cDescSitu  := Self:DescSitu(::cSitIni)
		::aoCroCmp[ny]:nQtdRev    := nQuant
		::aoCroCmp[ny]:nVlrRev    := nVlrUni
		::aoCroCmp[ny]:nPeriodico := nPeriodico
		::aoCroCmp[ny]:cPeriodico := Self:DescCondic(nPeriodico)
	Next

    //Identifica a Situação, Quant e
    For nx:= 1 to len(::aSituac)
		cAMRev     := ::aSituac[nx, 1]
		cSituac    := ::aSituac[nx, 2]
		nQuant     := ::aSituac[nx, 3]
		nVlrUni    := ::aSituac[nx, 4]
		cCondic    := ::aSituac[nx, 5]
		nPeriodico := Val(::aSituac[nx, 5]) + 1
        cRevisa    := ::aSituac[nx, 8]

		For ny := 1 to ::nQtdMes
            If ::aoCroCmp[ny]:cAnoMes >= cAMRev
                ::aoCroCmp[ny]:cSituac    := cSituac
                If cSituac $ "CO" 
                    ::aoCroCmp[ny]:nQtdRev    := 0
                Else
                    ::aoCroCmp[ny]:nQtdRev    := nQuant
                EndIf
				::aoCroCmp[ny]:nVlrRev    := nVlrUni
				::aoCroCmp[ny]:nPeriodico := nPeriodico
				::aoCroCmp[ny]:cRevisa    := cRevisa
				::aoCroCmp[ny]:cDescSitu  := Self:DescSitu(cSituac)
				::aoCroCmp[ny]:cPeriodico := Self:DescCondic(nPeriodico)
			EndIf
		Next
	Next
	
Return

Method SetReajuste() Class Tgcvxc05
	Local nx := 0
	Local ny := 0
	Local cAMAux   := ""
	Local nVlrVelho:= 0
	Local nVlrNovo := 0
	Local cRevisa  := ""
	Local cAMAnt   := ""

	//aSort(::aReajuste,,, {|x,y| x[1] + x[6] + x[4] + Inverte(x[5]) < y[1] + y[6] + Y[4] + Inverte(y[5]) })
	aSort(::aReajuste,,, {|x,y| x[1] + x[6] + x[4] + x[7] < y[1] + y[6] + Y[4] + y[7] })

	For nx:= 1 to len(::aReajuste)
		If ::aReajuste[nx, 5] $ "COI;COD" // Se for coorporativo não ajusta aqui o valor unitario
			Loop
		EndIf
		cAMAux    := ::aReajuste[nx, 1]
		nVlrVelho := ::aReajuste[nx, 2]
		nVlrNovo  := ::aReajuste[nx, 3]
		cRevisa   := ::aReajuste[nx, 4]
		For ny := 1 to ::nQtdMes
			If ! ::aoCroCmp[ny]:cRevisa  >= cRevisa
				Loop
			EndIf
			If ::aoCroCmp[ny]:cAnoMes < cAMAnt
				Loop
			EndIf
			If ::aoCroCmp[ny]:cAnoMes == cAMAux
				::aoCroCmp[ny]:nVlrAju   += nVlrNovo - nVlrVelho
			EndIf
		Next
		cAMAnt := cAMAux
	Next
Return

Method SetAjustFin() Class Tgcvxc05
	Local nx := 0
	Local ny := 0
	Local cAMAux   := ""
	Local nVlrVelho:= 0
	Local nVlrNovo := 0
	Local cRevisa  := ""
	Local nIndComis:= 0

	//aSort(::aAjustFin,,, {|x,y| x[1] + x[6] + x[4] + Inverte(x[5]) < y[1] + y[6] + Y[4] + Inverte(y[5]) })

	aSort(::aAjustFin,,, {|x,y| x[1] + x[6] + x[4] + x[7] < y[1] + y[6] + Y[4] + y[7] })

	For nx:= 1 to len(::aAjustFin)
		cAMAux    := ::aAjustFin[nx, 1]
		nVlrVelho := ::aAjustFin[nx, 2]
		nVlrNovo  := ::aAjustFin[nx, 3]
		cRevisa   := ::aAjustFin[nx, 4]
		nIndComis := ::aAjustFin[nx, 8]
		For ny := 1 to ::nQtdMes
			If ! ::aoCroCmp[ny]:cRevisa  >= cRevisa
				Loop
			EndIf
			If ::aoCroCmp[ny]:cAnoMes < cAMAux
				Loop
			EndIf

			::aoCroCmp[ny]:nPDesComis := nIndComis

			If ::aoCroCmp[ny]:cAnoMes == cAMAux
				::aoCroCmp[ny]:nVlAjuFin   += nVlrNovo - nVlrVelho
			EndIf
		Next
	Next
Return

Method SetVlIntera() Class Tgcvxc05
	Local nx        := 0
	Local ny        := 0
	Local cAMAux    := ""
	Local nVlrVelho := 0
	Local nVlrNovo  := 0
	Local cTipo     := ""

	aSort(::aP68Intera,,, {|x,y| x[1] + x[3] < y[1] + y[3] })

	For nx:= 1 to len(::aP68Intera)
		cAMAux    := ::aP68Intera[nx, 1]
		cTipo     := ::aP68Intera[nx, 2]
		nVlrVelho := ::aP68Intera[nx, 4]
		nVlrNovo  := ::aP68Intera[nx, 5]

		For ny := 1 to ::nQtdMes
			If ::aoCroCmp[ny]:cAnoMes < cAMAux
				Loop
			EndIf

			If ::aoCroCmp[ny]:cAnoMes == cAMAux
				::aoCroCmp[ny]:nVlIntera += nVlrNovo - nVlrVelho
	
			EndIf
		Next
	Next
Return


Method SetCancela() Class Tgcvxc05
	Local nx       := 0
	Local ny       := 0
	Local cAMPH3   := ""
	Local cOper    := ""
	Local nQtdOpe  := 0
	Local cSitAnt  := ""
	Local cAMCob   := ""
	Local cMotCan  := ""
	Local lCanRea  := .F.
    
	If Empty(::aCancela)
		Return
    EndIf

    //aadd(aCancela, {cAMPH3, cOper, cTipo, dDtOper, nQtdOpe, cCodMot, nMulta, cAMCob, cSitAnt })
    //Destarcar a Qtd de Programada e a Qtde da competencia
    For nx:= 1 to len(::aCancela)
        cAMPH3  := ::aCancela[nx, 1]    
		cOper   := ::aCancela[nx, 2]
		nQtdOpe := ::aCancela[nx, 5]
		cMotCan := ::aCancela[nx, 6]
        cSitAnt := ::aCancela[nx, 9]
        If ! cOper == "P"
            Loop
        EndIf
        If ! Empty(cSitAnt) .and.  ! cSitAnt $ "CO"
            ::cSitAtiAnt := cSitAnt
        EndIf
        For ny := 1 to ::nQtdMes
            If ::aoCroCmp[ny]:cAnoMes < cAMPH3
                Loop
            EndIf
            ::aoCroCmp[ny]:nQtdProgC += nQtdOpe
			If ::aoCroCmp[ny]:cAnoMes == cAMPH3
				::aoCroCmp[ny]:cMotCan := cMotCan
                ::aoCroCmp[ny]:nQtdProg += nQtdOpe
            EndIf
        Next
    Next

    //Destarcar a Qtd cancelada e reativação
    For nx:= 1 to len(::aCancela)
        cAMPH3  := ::aCancela[nx, 1]    
        cOper   := ::aCancela[nx, 2]
		nQtdOpe := ::aCancela[nx, 5]
		cMotCan := ::aCancela[nx, 6]
		cAMCob  := ::aCancela[nx, 8]
		cSitAnt := ::aCancela[nx, 9]
		
        If cOper == "P"
            Loop
		EndIf
		If cOper == "R"
       
			If ! Empty(cSitAnt) .and.  ! cSitAnt $ "CO"
				::cSitAtiAnt := cSitAnt
			EndIf

			If Empty(cAMCob) .and. Empty(cAMPH3)  
				Loop   // registro alterado manualmente pela manutenção para registrar a reativação, nesse caso sera ignorado
			EndIf
			If Empty(cAMCob)
				cAMCob := cAMPH3
			EndIf
			If Empty(cAMPH3)
				cAMPH3 := cAMCob
			EndIf
		EndIf
		
		For ny := 1 to ::nQtdMes
			If cOper == "C"
				lCanRea  := .F.
				If ::aoCroCmp[ny]:cAnoMes == cAMPH3
					::aoCroCmp[ny]:cMotCan := cMotCan
					::aoCroCmp[ny]:nQtdCan += nQtdOpe
					lCanRea := .T.
				EndIf
				If ::aoCroCmp[ny]:cAnoMes >= cAMPH3
					::aoCroCmp[ny]:nQtdRetCA += nQtdOpe
					lCanRea := .T.
				EndIf
				If lCanRea
					/*If ::aoCroCmp[ny]:nQtdReat > 0 
						::aoCroCmp[ny]:nQtdReat  -= nQtdOpe
					EndIf*/
					If ::aoCroCmp[ny]:nQtdRetro > 0 
						::aoCroCmp[ny]:nQtdRetro  -= nQtdOpe
					EndIf
				EndIf
			Else	
				If ::aoCroCmp[ny]:cAnoMes < cAMCob
					Loop
				EndIf
				
				If ::aoCroCmp[ny]:cAnoMes == cAMCob
					::aoCroCmp[ny]:nQtdReat += nQtdOpe
				EndIf
				If ::aoCroCmp[ny]:cAnoMes < cAMPH3 .aND. ::aoCroCmp[ny]:cAnoMes >= cAMCob
					::aoCroCmp[ny]:nQtdRetCA -= nQtdOpe
					::aoCroCmp[ny]:nQtdRetro += nQtdOpe	
				EndIf


			EndIf
			
        Next
    Next
    
Return

Method SetTroca() Class Tgcvxc05
    Local cAMCro   := ""
    Local cAMCriado := ""
    Local cSitAnt  := ""
    Local nDelta   := 0
    Local cId      := ""
    Local cTipo    := ""
    Local nValor   := 0
    Local nQtde    := 0
    Local nx       := 0
    Local ny       := 0

    If Empty(::aTroca)
		Return
    EndIf

    //Destarcar a Qtd de Troca 
    For nx:= 1 to len(::aTroca)
        cAMCro   := ::aTroca[nx, 1]
        nDelta   := ::aTroca[nx, 2]
        cId      := ::aTroca[nx, 3]
        cTipo    := ::aTroca[nx, 4]   // 1=Saida;2=Entrada;3=Entrada Nova
        nValor   := ::aTroca[nx, 5]
        nQtde    := ::aTroca[nx, 6]
        cSitAnt  := ::aTroca[nx, 7]
        cAMCriado:= ::aTroca[nx, 8]

        If ! Empty(cSitAnt)
            ::cSitAtiAnt := cSitAnt
        EndIf

        For ny := 1 to ::nQtdMes
            If ! cTipo  == "1"  //Saida
                Loop 
            EndIf

            If ::aoCroCmp[ny]:cAnoMes == cAMCro
                ::aoCroCmp[ny]:nDeltaTrc   += nDelta
                ::aoCroCmp[ny]:cIdDltTrc   := cId
                ::aoCroCmp[ny]:cTpTroca    := cTipo
                ::aoCroCmp[ny]:nVlrTrc     += nValor 
                ::aoCroCmp[ny]:nQtdTroca   += nQtde
            EndIf
            If cAMCro == cAMCriado
                If ::aoCroCmp[ny]:cAnoMes == cAMCro
                    ::aoCroCmp[ny]:nQtdTrCalc += nQtde
                    //::aoCroCmp[ny]:cSituac    := cSitAnt
                EndIf
            ElseIf cAMCriado < cAMCro 
                If ::aoCroCmp[ny]:cAnoMes < cAMCriado
                    Loop
                EndIf
                If ::aoCroCmp[ny]:cAnoMes <= cAMCro
                    ::aoCroCmp[ny]:nQtdTrCalc += nQtde
                EndIf
                If ::aoCroCmp[ny]:cAnoMes < cAMCro
                    ::aoCroCmp[ny]:cSituac    := cSitAnt
                EndIf
            ElseIf cAMCro < cAMCriado
                If ::aoCroCmp[ny]:cAnoMes < cAMCro
                    Loop
                EndIf
                If ::aoCroCmp[ny]:cAnoMes == cAMCro
                    ::aoCroCmp[ny]:cSituac    := "O"
                EndIf
                If ::aoCroCmp[ny]:cAnoMes > cAMCro .and. ::aoCroCmp[ny]:cAnoMes < cAMCriado
                    ::aoCroCmp[ny]:cSituac    := "O"
                    ::aoCroCmp[ny]:nQtdTrCalc -= nQtde
                EndIf
            EndIf
        Next
    Next
Return


Method SetAberto() Class Tgcvxc05
    Local nx         := 1
    Local cAnoMes    := ""
    Local cCliente   := ""
    Local cLoja      := ""
    Local nPerRat    := 0
	Local cPedido    := ""
    Local cStatusPH6 := ""
    Local aClientes  := {}
    Local np         := 0
    Local ny         := 0
    Local nPTotal    := 0
    Local oObj   
    Local nPerRatF   := 0
	Local nLastReaj	:= 0


    While nx <= len(::aFinanceiro)
        cAnoMes    := Self:RetPH5(nx, "PH5_ANOMES")
        cCliente   := Self:RetPH5(nx, "PH5_CLIENT") 
        cLoja      := Self:RetPH5(nx, "PH5_LOJA")
        nPerRat    := Self:RetPH5(nx, "PH5_PERRAT")
		cPedido    := Self:RetPH5(nx, "PH5_PEDVEN")

        If Empty(nPerRat)
            nPerRat := 100
        EndIf
        cStatusPH6 := Self:RetPH5(nx, "PH6_STATUS")
        If cStatusPH6 $ "01/02/04/05/06"  
            np := Ascan(aClientes, {|x| x[1] + x[2] == cCliente + cLoja})
            If Empty(nP)
                aadd(aClientes, {cCliente, cLoja, nPerRat})
            EndIf
        EndIf
        If nx == len(::aFinanceiro) .or. ! cAnoMes == Self:RetPH5(nx + 1, "PH5_ANOMES")
            nPerRatF := 0
            For ny := 1 to len(aClientes)
                nPTotal  += aClientes[ny, 3]  
                If aClientes[ny, 1] + aClientes[ny, 2]  == ::cCliente + ::cLoja
                    nPerRatF := aClientes[ny, 3]
                EndIf
            Next
            oObj := Self:RetObjCmp(cAnoMes)
            If oObj <> NIL
                oObj:lFinalizado := nPTotal == 100 .And. !Empty(cPedido)
				If Self:lBilling
					If Self:OpenBilling(cAnoMes, nx , oObj:nPerRat) 
						oObj:lFinalizado := .F.
					EndIF					
				Endif

				//Tratamento Corporativo, não marcar como finalizado
				If ASCAN(SELF:aReajuste, { |X| Left(X[5], 2) == 'CO'}) > 0
					nLastReaj := Len(SELF:aReajuste)
					If Left(cAnoMes,4) == Left(SELF:aReajuste[nLastReaj,1],4)	
						oObj:lFinalizado := .F.
					EndIf
				EndIf
				

                If Empty(nPerRatF) .and. cStatusPH6 $ "01/02/04/05/06"  
                    nPerRatF := 101
                EndIf
                oObj:nPerRatF := nPerRatF
            EndIf
            aClientes := {}
            nPTotal := 0
        EndIf
        nx++
    End 

Return 



Method OpenBilling(cAnoMes, nx , nPerRat) Class Tgcvxc05
	Local nValbil := 0
	Local nQtdBil := 0
	Local nPosbil := 0
	Local lOpen   := .F.
	Local nQtdfat := 0

	nPosbil := PosBilling(Self:aBilling, cAnoMes)

	If nPosbil > 0
		nValbil := Self:aBilling[nPosbil, 3]
		nQtdBil := Self:aBilling[nPosbil, 4]
		IF nQtdBil == 0
			nQtdBil := 1
		Endif
		nQtdfat := nQtdBil * nPerRat	

		If Self:RetPH5(nx, "PH5_QUANT") <> nQtdBil
			lOpen := .T.
		EndIf
		If Self:RetPH5(nx, "PH5_VLUNIT") <> nValbil
			lOpen := .T.
		EndIf
		If Self:RetPH5(nx, "PH5_QTDFAT") <> nQtdfat
			lOpen := .T.
		EndIf

	EndIf	

Return lOpen

Static Function PosBilling(aBilling, cAnoMes)
	Local nx   := 0
	Local nPos := 0

	For nx:= 1 to len(aBilling)
		If ! aBilling[nx, 1] == cAnoMes
			Loop
		EndIf
		nPos := nx
	Next 

Return nPos

Method AtuSituac() Class Tgcvxc05
	Local ny         := 0

    For ny := 1 to ::nQtdMes
        If ::aoCroCmp[ny]:cSituac $ "CO"
            Loop
        EndIf
        If ::aoCroCmp[ny]:lFinalizado
            Loop
        EndIf
		If ::cSituac <> 'C'//ajuste da OPR-828 perdida na migracao e recuperada
        	::aoCroCmp[ny]:cSituac := ::cSituac
        EndIf
        
		
    Next
	
Return


Method RetPH5(nLinha, cNameCpo) Class Tgcvxc05
    Local uRet := NIL    
    Local np   := 0
    Local aFinanceiro := ::aFinanceiro

    np:= Ascan(aFinanceiro[nLinha], {|x| x[1] == cNameCpo})

    uRet := aFinanceiro[nLinha, np, 2]

Return uRet



Method DescCondic(nPeriodico) Class Tgcvxc05
	Local cPeriodico
	If nPeriodico == 1
		cPeriodico := "Mensal"
	ElseIf nPeriodico == 2
		cPeriodico := "Bimestral"
	ElseIf nPeriodico == 3
		cPeriodico := "Trimestral"
	ElseIf nPeriodico == 6
		cPeriodico := "Semestral"
	ElseIf nPeriodico == 12
		cPeriodico := "Anual"
	ElseIf nPeriodico == 24
		cPeriodico := "BiAnual"
	ElseIf nPeriodico == 36
		cPeriodico := "Trienal"
	ElseIf nPeriodico == 60
		cPeriodico := "Quienal"
	Else
		cPeriodico := "Não definido"
	EndIf

Return cPeriodico

Method DescSitu(cSituac) Class Tgcvxc05
	Local cDescSitu := "Não definido"
	Local aDescSitu := {"A-Ativo", "C-Cancelado", "G-Gratuito", "M-Manual", "S-Suspenso", "P-Pendente","T-Transferido","O-Troca"}
	Local nP        := 0

	nP := ascan(aDescSitu, {|x| Left(x, 1) == cSituac })
	If ! Empty(nP)
		cDescSitu := Subs(aDescSitu[nP], 3)
	EndIf

Return cDescSitu

Method Salva(aAMSave) Class Tgcvxc05
	Local nx
	Local ny

	aSort(aAMSave)

	For nx := 1 to len(aAMSave)
		For ny := 1 to ::nQtdMes
			If  aAMSave[nx] == ::aoCroCmp[ny]:cAnoMes
				::aoCroCmp[ny]:Salva()
				Exit
			EndIf
		Next
	Next

Return

Method RetPerRat(cAmProc, lExcecao) Class Tgcvxc05
	Local ni       := 0
	Local cAmDe    := ""
	Local cAmAte   := ""
	Local nPerRat  := 0
	Local cStatus  := ""
	Local cRevHist := Space(FwGetSX3Cache("P70_REVHIS", "X3_TAMANHO"))
	Default lExcecao := .f.

	Begin Sequence
		lExcecao := .f.
		For ni:= 1 to Len(::aExcecao)
			cAmDe  := ::aExcecao[ni, 3]
			cAmAte := ::aExcecao[ni, 4]

			If Empty(cAmAte)
				cAmAte := "ZZZZZZ"
			EndIf

			If cAmProc >= cAmDe .and. cAmProc <= cAmAte
				nPerRat  := RetPerRat(::cCliente, ::cLoja, cAmProc, ::aExcecao, @cRevHist)
				lExcecao := .t.
				Break
			EndIf
		Next


		For ni:= 1 to Len(::aHistoric)
			cAmDe   := ::aHistoric[ni, 3]
			cAmAte  := ::aHistoric[ni, 4]
			cStatus := ::aHistoric[ni, 6]

			If Empty(cAmAte)
				cAmAte := "ZZZZZZ"
			EndIf

			If cStatus <> "A"
				Loop
			EndIf

			If cAmProc >= cAmDe .and. cAmProc <= cAmAte
				nPerRat := RetPerRat(::cCliente, ::cLoja, cAmProc, ::aHistoric, @cRevHist)
				Break
			EndIf
		Next

		For ni:= 1 to Len(::aPHG)
			nPerRat := RetPerRat(::cCliente, ::cLoja, cAmProc, ::aPHG, @cRevHist)
			Break
		Next

		For ni:= 1 to Len(::aCNCMain)
			nPerRat := RetPerRat(::cCliente, ::cLoja, cAmProc, ::aCNCMain, @cRevHist)
		Next

	End Sequence

	::cRatHist := cRevHist

Return nPerRat

Static Function RetPerRat(cCliente, cLoja, cAmProc, aRateio, cRevHist)
	Local ni      := 0
	Local cCliAux := ""
	Local cLojAux := ""
	Local cAmDe   := ""
	Local cAmAte  := ""
	Local nRateio := 0
	Local cStatus := ""

	For ni:=1 to Len(aRateio)
		cCliAux  := aRateio[ni, 1]
		cLojAux  := aRateio[ni, 2]
		cAmDe    := aRateio[ni, 3]
		cAmAte 	 := aRateio[ni, 4]
		cStatus  := aRateio[ni, 6]
		cRevHist := aRateio[ni, 7]

		If Empty(cAmAte)
			cAmAte := "ZZZZZZ"
		EndIf

		If cStatus <> "A"
			Loop
		EndIf

		If !(cCliAux == cCliente .And. cLojAux == cLoja)
			Loop
		EndIf

		If cAmProc >= cAmDe .and. cAmProc <= cAmAte 
			nRateio := aRateio[ni, 5]
			Exit
		EndIf
	Next
Return nRateio

Method CountVlr(cAtrib) Class Tgcvxc05
	Local nCount := 0
	Local oObj
	Local nValor
	Local nx

	For nx:= 1 to len(::aoCroCmp)
		oObj := ::aoCroCmp[nx]
		nValor := &("oObj:" + cAtrib)
		If ! Empty(nValor)
			nCount++
		EndIf
	Next

Return nCount

Method IsIgual(cAtrib) Class Tgcvxc05
	Local oObj
	Local uValor
	Local uValorAnt
	Local nx

	For nx:= 1 to len(::aoCroCmp)
		oObj := ::aoCroCmp[nx]
		uValor := &("oObj:" + cAtrib)
		If nX > 1 .and. uValor <> uValorAnt
			Return .F.
		EndIf
		uValorAnt := uValor
	Next

Return .t.

Method RetObjCmp(cAMProc) Class Tgcvxc05
	Local ny
	Local oRet := Nil

	For ny := 1 to ::nQtdMes
		If  ::aoCroCmp[ny]:cAnoMes == cAMProc
			oRet := ::aoCroCmp[ny]
			Exit
		EndIf
	Next

Return oRet

Method FreeChild() Class Tgcvxc05
	Local nx

	For nx:= 1 to len(::aoCroCmp)
		If ::aoCroCmp[nX] <> Nil
			::aoCroCmp[nx]:Free()
		EndIf
	Next

	Self:Destroy()

Return


Method Html(lDetPh5)  Class Tgcvxc05
	Local cHtml := ""
	Default lDetPh5 := .F.
	::lDetPh5 := lDetPh5

	cHtml += Self:HtmlParIni()
	//cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633' bgcolor=#009ABD>" + CRLF
	cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
	cHtml += Self:HtmlCabec()
	cHtml += Self:HtmlTitulo("Calculo Cronograma")
	cHtml += Self:HtmlLinha("Revisão"                    , "cRevisa"   )
	cHtml += Self:HtmlLinha("Periodicidade"              , "cPeriodico")
	cHtml += Self:HtmlLinha("Situação"                   , "cDescSitu" )
	cHtml += Self:HtmlLinha("Quantidade Revisão"         , "nQtdRev"   )
	cHtml += Self:HtmlLinha("% Rateio"                   , "nPerRat"   )
    cHtml += Self:HtmlLinha("Quantidade"                 , "nQuant"    )
    cHtml += Self:HtmlLinha("Qtd Calc. Programado"       , "nQtdProgC" )
    cHtml += Self:HtmlLinha("Qtd Calc. Reativação Retro.", "nQtdRetro" )
    cHtml += Self:HtmlLinha("Qtd Calc. Troca"            , "nQtdTrCalc")

    cHtml += Self:HtmlLinha("Quantidade Faturada"        , "nQtdFat"   )
    cHtml += Self:HtmlLinha("Quantidade Programada"      , "nQtdProg"  )
    cHtml += Self:HtmlLinha("Quantidade Cancelada"       , "nQtdCan"   )
    cHtml += Self:HtmlLinha("Quantidade Reativada"       , "nQtdReat"  )
    cHtml += Self:HtmlLinha("Quantidade Troca"           , "nQtdTroca" )
	cHtml += Self:HtmlLinha("Qtd. Transf. Entrada"       , "nQtdTraE"  )
	cHtml += Self:HtmlLinha("Qtd. Transf. Saida"         , "nQtdTraS"  )
	cHtml += Self:HtmlLinha("Qtd. Billing"               , "nBilQtde"  )
	cHtml += Self:HtmlLinha("% Reaj. Extra."             , "nPerREX"   )
	cHtml += Self:HtmlLinha("Reajuste Unitario"          , "nVlrAju"   )
	cHtml += Self:HtmlLinha("Valor Total Intera"         , "nVlTIntera" )
	cHtml += Self:HtmlLinha("Valor Cancelado Intera"     , "nVlTIntCan" )
	cHtml += Self:HtmlLinha("Valor Revisão"              , "nVlrRev"   )
	cHtml += Self:HtmlLinha("Valor Unitario"             , "nVlrUni"   )
	cHtml += Self:HtmlLinha("Valor Total "               , "nVlrTot"   )
	cHtml += Self:HtmlLinha("Valor Faturar Ref."         , "nVlrFatRef")
	cHtml += Self:HtmlLinha("Valor Faturar"              , "nVlrFat"   )
	cHtml += Self:HtmlLinha("Valor Faturar s/ Imp."      , "nVlrFatS"  )
	cHtml += Self:HtmlLinha("Valor Imposto"              , "nVlrImpT"  )
	cHtml += Self:HtmlLinha("Valor Carencia"             , "nVlrCare"  )
	cHtml += Self:HtmlLinha("Valor Bonificação"          , "nVlrBoni"  )
	cHtml += Self:HtmlLinha("Valor Cancelado"            , "nVlrCanc"  )
	cHtml += Self:HtmlLinha("Valor Reativado"            , "nVlrReat"  )
	cHtml += Self:HtmlLinha("Valor Canc. Prog."          , "nVlrProg"  )
	cHtml += Self:HtmlLinha("Multa Cancelamento"         , "nMultCan"  )
	cHtml += Self:HtmlLinha("Valor Reajuste"             , "nVlrTAju"  )
	cHtml += Self:HtmlLinha("Valor Nova Venda"           , "nVlrVNova" )
	cHtml += Self:HtmlLinha("Valor Billing Up"           , "nBilUp"    )
	cHtml += Self:HtmlLinha("Valor Billing Down"         , "nBilDown"  )
	cHtml += Self:HtmlLinha("Valor Royalties Var"        , "nRoyVar"   )
	cHtml += Self:HtmlLinha("Valor Troca"                , "nVlrTrc"   )
	cHtml += Self:HtmlLinha("Valor Transf. Entr."        , "nVlrTraE"  )
	cHtml += Self:HtmlLinha("Valor Transf. Saida"        , "nVlrTraS"  )
	cHtml += Self:HtmlLinha("Valor Reaj. Extra. Total "  , "nVlrREXT"  )


	cHtml += Self:HtmlLinha("Valor Unitario Incr."       ,"nCorUni"    )
	cHtml += Self:HtmlLinha("Valor Bonificação Incr."    ,"nCorBoni"   )
	cHtml += Self:HtmlLinha("Valor Fat Ref Incr."        ,"nCorFatRef" )
	cHtml += Self:HtmlLinha("Valor Imposto Incr."        ,"nCorImpT"   )
	cHtml += Self:HtmlLinha("Valor Fat Incr."            ,"nCorFat"    )


	cHtml += Self:HtmlTitulo("Banco Cronograma")

	cHtml += Self:HtmlDet("PH5 Total"              , "nPH5VlrTot"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 A Faturar"          , "nPH5VlAFat"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 A Faturar s/ Imp."  , "nPH5VlAFaS"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Faturado"           , "nPH5VlFat"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Faturado s/ Imp."   , "nPH5VlFatS"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Imposto"            , "nPH5VlTrib"   , .F., "BLUE")
    cHtml += Self:HtmlDet("PH5 Valor Saldo NCC"    , "nPH5VlSld"    , .F., "BLUE")
    cHtml += Self:HtmlDet("PH5 Quantidade Fat"     , "nPH5Quant"    , .F., "BLUE")
    cHtml += Self:HtmlDet("PH5 Quantidade Can"     , "nPH5QtdCan"   , .F., "BLUE")
    cHtml += Self:HtmlDet("PH5 Quantidade Reat"    , "nPH5QtdRea"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Carencia"           , "nPH5VlCar"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Bonificação Fat"    , "nPH5VlBonF"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Bonificação"        , "nPH5VlBon"    , .F., "BLUE")

	cHtml += Self:HtmlDet("PH5 Cancelado"          , "nPH5VlCan"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 MultaCanc."         , "nPH5Multa"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Reativado"          , "nPH5VlReat"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Reajuste"           , "nPH5VlReaj"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Incremento Fat"     , "nPH5VlIncF"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Incremento"         , "nPH5VlInc"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Troca"              , "nPH5VlTroca"  , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Venda Nova"         , "nPH5VlNova"   , .F., "BLUE")

	cHtml += Self:HtmlDet("PH5 Billing Up"         , "nPH5BilUp"    , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Billing Down"       , "nPH5BilDown"  , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Billing Qtde"       , "nPH5BilQtd"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Billing Valor"      , "nPH5BilVlr"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Billing Variação"   , "nPH5BilVar"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Royalties Variação" , "nPH5RoyVar"   , .F., "BLUE")
	cHtml += Self:HtmlDet("PH5 Reajuste Extra."    , "nPH5VlREX"    , .F., "BLUE")


	cHtml += Self:HtmlLinha("Status"          , "cStatus"       , .F., "BLUE")

	cHtml += Self:HtmlTitulo("Analise de Integridade")

	cHtml += Self:HtmlLinha("Base Integra"    , "lIntegro"     , .F.)
	cHtml += Self:HtmlLinha("Necessario"      , "cAcao"        , .F.)
	cHtml += Self:HtmlLinha("Erro"            , "aErro"        , .F.)
	cHtml += Self:HtmlTitulo("Atualizar Cronograma")

    cHtml += Self:HtmlLinha("Quantidade "          , "nAtuQuant"   , .F., "BROWN")
    cHtml += Self:HtmlLinha("Quantidade Fat"       , "nAtuQtFat"   , .F., "BROWN")
	cHtml += Self:HtmlLinha("Qtd Carencia"         , "nAtuQtCare"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Qtd Cancelada"        , "nAtuQtCan"   , .F., "BROWN")
	cHtml += Self:HtmlLinha("Qtd Reativada"        , "nAtuQtReat"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Qtd Tranferida"       , "nAtuQtTra"   , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Total"          , "nAtuVlrTot"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Faturar"        , "nAtuVlrFat"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Faturar S/ Imp.", "nAtuVlrFaS"  , .F., "BROWN")

	cHtml += Self:HtmlLinha("Valor Imposto"        , "nAtuVlrImp"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Carencia"       , "nAtuVlrCar"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Bonificação"    , "nAtuVlrBon"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Cancelado"      , "nAtuVlrCan"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Multa Canc."    , "nAtuMulta"   , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Reativado"      , "nAtuVlrReat" , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Incremento"     , "nAtuVlrIncr" , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Billing"        , "nAtuBilVlr"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Billing Up"     , "nAtuBilUp"   , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Billing Down"   , "nAtuBilDown" , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Billing Var."   , "nAtuBilVar"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Royalties Var." , "nAtuRoyVar"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Troca"          , "nAtuVlrTrc"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Valor Reaj. Extra."   , "nAtuVlrREX"  , .F., "BROWN")
	cHtml += Self:HtmlLinha("Flg SMS Corporativo"  , "cAtuFlCorp"  , .F., "BROWN")


	cHtml += "   </table>" + CRLF
	cHtml += "<hr>" + CRLF
	cHtml += "<br>" + CRLF

Return cHtml


Method HtmlParIni() Class Tgcvxc05
	Local cHtml   := ""

	cHtml += "<font size=5 face=calibri>  " + CRLF
	cHtml += "   <table width=100% border=0> " + CRLF

	If ::cTipRec == "1"
		cHtml += "      <tr><td>Contrato<b> " + ::cContrato + "</b> Revisão <b> " + ::cRevisa +" </b>Planilha<b> " + ::cPlanilha + "</b> Item<b> "+ ::cItem + "</b></td></tr>" + CRLF
	Else
		cHtml += "      <tr><td>Contrato<b> " + ::cContrato + "</b> Revisão <b> " + ::cRevisa +" </b>Planilha<b> " + ::cPlanilha + "</b> Item<b> "+ ::cItem + "</b> Tipo<b> PONTUAL</b></td></tr>" + CRLF
	EndIf
	If ::lSetorPub
		cHtml += "     <tr><td>Cliente<b> " + ::cCliente + "</b> Loja <b> " + ::cLoja + " - " + ::cDescCli + " </b> <u>SETOR BUBLICO</u></td></tr>" + CRLF
	Else
		cHtml += "     <tr><td>Cliente<b> " + ::cCliente + "</b> Loja <b> " + ::cLoja + " - " + ::cDescCli + " </b> </b></td></tr>" + CRLF
	EndIf
    cHtml += "      <tr><td>Proposta<b> "             + ::cPropos +"</b></td></tr>" + CRLF
    cHtml += "      <tr><td>Produto<b> "              + ::cProduto  + " - " + ::cDescPro +"</b></td></tr>" + CRLF
	cHtml += "      <tr><td>Situação <b> "            + ::cDescSitu + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Periodicidade<b> "        + ::cPeriodico                   + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Quantidade CTR <b> "      + Str(::nQuant    , 15, 2)      + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Valor Unitario CTR<b> "   + Str(::nVlrUniCtr, 15, 2)      + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Valor Total CTR<b> "      + Str(::nVlrTotCtr, 15, 2)      + " </td></tr>" + CRLF
	If ! Empty(::nCarenVnd)
		cHtml += "      <tr><td>Carencia na Venda<b> "    + Str(::nCarenVnd)               + " </td></tr>" + CRLF
		cHtml += "      <tr><td>Inicio Carencia<b> "      + ::cCarIniVnd + " </td></tr>" + CRLF
		cHtml += "      <tr><td>Termino Carencia<b> "     + ::cCarFinVnd + " </td></tr>" + CRLF
	EndIf
	cHtml += "      <tr><td>Inicio Contrato<b> "      + Dtoc(::dConIni) + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Termino Contrato<b> "     + Dtoc(::dConFin) + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Inicio Faturamento<b> "      + ::cCmpFIni + " </td></tr>" + CRLF
	If ! empty(::cAnoMesC)
		cHtml += "      <tr><td>Item Cancelado<b> "               + Right(::cAnoMesC, 2) + "/" + Left(::cAnoMesC, 4) + " </td></tr>" + CRLF
	EndIf
	If ! empty(::cAnoMesO)
		cHtml += "      <tr><td>Item Trocado<b> "               + Right(::cAnoMesO, 2) + "/" + Left(::cAnoMesO, 4) + " </td></tr>" + CRLF
	EndIf
	If ! empty(::cAnoMesTE)
		cHtml += "      <tr><td>Transferencia Entrada<b> "               + Right(::cAnoMesTE, 2) + "/" + Left(::cAnoMesTE, 4) + " </b>Contrato de Origem: <b>" + ::cCtrOrig + "</b> </td></tr>" + CRLF
    EndIf
    If ! empty(::cAnoMesTS)
		cHtml += "      <tr><td>Transferencia Saida<b> "               + Right(::cAnoMesTS, 2) + "/" + Left(::cAnoMesTS, 4) + " </b>Contrato de Destino: <b>" + ::cCtrDest + "</b> </td></tr>" + CRLF
	EndIf
	cHtml += "      <tr><td>Indice p/ imposto<b> "      + Str(::nImpostC, 15, 4) + " </td></tr>" + CRLF
	cHtml += "   </table>" + CRLF

Return cHtml

Method HtmlCabec() Class Tgcvxc05
	Local cHtml       := ""
	Local nx          := 0
	Local cCorMes     := "bgcolor='YELLOW'"
	Local cTamCol     := "100"
	Local cSublinhado := ""
	Local cRisco      := ""
	Local lCorSim     := .F.
	Local nc          := 0

	If ::lDetPh5
		cTamCol := "150"
	EndIf

	cHtml += "      <tr>" + CRLF
	cHtml += "         <td width='200' align='RIGHT'><b>  </td>" + CRLF
	For nx:= 1 to ::nQtdMes
		If ::aoCroCmp[nX]:cMesAno == StrZero(Month(dDataBase), 2) + "/" + Strzero(Year(dDataBase), 4)
			cCorMes:= "bgcolor='YELLOW'"
		Else
			cCorMes:= ""
		EndIf
		If ! ::aoCroCmp[nX]:lPeriodo
			cRisco := "<s>"
		Else
			cRisco := ""
		EndIf
		If ::aoCroCmp[nX]:lIniPer .and. ::nPeriodico > 1
			lCorSim  := ! lCorSim
		EndIf

		If lCorSim
			cHtml += "         <td width=" +cTamCol + " align='CENTER' " + cCorMes + "> <font COLOR='GREEN'><b>" + cRisco + cSublinhado +  " Seq:(" +  Alltrim(Str(nx, 5)) + ") " + ::aoCroCmp[nX]:cMesAno  + "</font> </td>" + CRLF
		Else
			cHtml += "         <td width=" +cTamCol + " align='CENTER' " + cCorMes + "><b>" + cRisco + cSublinhado +  " Seq:(" +  Alltrim(Str(nx, 5)) + ") " + ::aoCroCmp[nX]:cMesAno  + "</td>" + CRLF
		EndIf
		If ++nc == 12
			nc := 0
			cHtml += "         <td width='200' align='RIGHT'><b>  </td>" + CRLF
		EndIf
	Next
	cHtml += "      </tr>" + CRLF

Return cHtml

Method HtmlTitulo(cTitulo) Class Tgcvxc05
	Local cHtml := ""
	Local nx
	Local nc    := 0

	cHtml += "      <tr style='background-color:#CDC9C9'>" + CRLF
	cHtml += "         <td><b><u><font COLOR='RED'>" + cTitulo + "</font></u></b></td>" + CRLF
	For nx:= 1 to len(::aoCroCmp)
		cHtml += "         <td></td>" + CRLF
		If ++nc == 12
			nc := 0
			cHtml += "         <td><b><u><font COLOR='RED'>" + cTitulo + "</font></u></b></td>" + CRLF
		EndIf
	Next
	cHtml += "      </tr>" + CRLF

Return cHtml



Static _lSim := .T. 

Method HtmlLinha(cTitulo, cAtrib, lVerCalcF, cCor) Class Tgcvxc05
	Local cHtml := ""
	Local nx
	Local oObj
	Local uValor      := NIL
	Local cListAtrib  := "nVlrAju/nVlIntera/nVlTIntera/nVlTIntCan"
	Local nc          := 0
	Local cCorAnt     := ''
	Default lVerCalcF := .T.
	Default cCor      := ''

	cCorAnt := cCor 

	If cAtrib $ "cPeriodico;nVlrUni;nVlrTot" .and. Self:IsIgual(cAtrib)
		Return cHtml
	EndIf

	If ! cAtrib $ "??" .and. Empty(Self:CountVlr(cAtrib))
		Return cHtml
	EndIf

	//cHtml += "      <tr>" + CRLF
	If _lSim 
		cHtml += "      <tr style='background-color:#F0FFFF'>" + CRLF
		_lSim := .F.
	Else 
		cHtml += "      <tr style='background-color:#E0EEEE'>" + CRLF
		_lSim := .t.
	EndIf 
	
	cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF
	For nx:= 1 to len(::aoCroCmp)
		oObj := ::aoCroCmp[nx]
		uValor := &("oObj:" + cAtrib)
		If lVerCalcF .AND. ! ::aoCroCmp[nX]:lPeriodo .AND. ! cAtrib $ cListAtrib
			cHtml += "         <td> </td>" + CRLF
			If ++nc == 12
				nc := 0
				cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF
			EndIf
			Loop
		EndIf

		If Valtype(uValor) == "N"
			cHtml += "         <td align='RIGHT'>"
			If  cAtrib $ "nQtdProg,nQtdCan,nVlrCanc,nVlrProg"
				cCor := "RED"
			ElseIf cAtrib $ "nQtdReat,nVlrReat"
				cCor := "BLUE"
			Else 
				cCor := cCorAnt
			EndIf 
			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			If ! Empty(uValor) .or. cAtrib == "nVlrUni" //.or. cAtrib == "nQtdRev" .or. cAtrib == "nQuant"
				If cAtrib == "nVlrFat" 
					cHtml += "<b>" + Alltrim(Transform(uValor, "@e 999,999,999.99" )) + " </b>"
				Else 
					cHtml += Alltrim(Transform(uValor, "@e 999,999,999.99" ))
				EndIf 
			EndIf
		ElseIf Valtype(uValor) == "C"
			cHtml += "         <td align='LEFT'>"

			If Alltrim(uValor) == "Cancelado"
				cCor := "RED"
			Else 
				cCor := cCorAnt
			EndIf 

			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			If ! Empty(uValor)
				cHtml += Alltrim(uValor)
			EndIf
		ElseIf Valtype(uValor) == "L"
			cHtml += "         <td align='LEFT'>"

			If uValor
				If ! Empty(cCor)
					cHtml += "<font COLOR='" + cCor + "'>"
				EndIf
				cHtml += "Sim"
			Else
				cHtml += "<font COLOR='RED'>"
				cHtml += "Não"
			EndIf
		ElseIf Valtype(uValor) == "A"
			cHtml += "         <td align='LEFT'>"
			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			aEval(uValor, {|x| cHtml += x + "<br>" })
		EndIf
		cHtml += "</td>" + CRLF
		If ++nc == 12
			nc := 0
			cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF
		EndIf
	Next
	cHtml += "      </tr>" + CRLF
Return cHtml

Method HtmlDet(cTitulo, cAtrib, lVerCalcF, cCor) Class Tgcvxc05
	Local cHtml := ""
	Local nx
	Local ny
	Local oObj
	Local oDet
	Local uValor := NIL
	Local uVlrDet:= NIL
	Local cListAtrib := "nVlrAju/nVlIntera/nVlTIntera/nVlTIntCan"
	Local nc         := 0
	Default lVerCalcF := .T.
	Default cCor := ''

	If cAtrib $ "cPeriodico;cDescSitu;" .and. Self:IsIgual(cAtrib)
		Return cHtml
	EndIf

	If ! cAtrib $ "??" .and. Empty(Self:CountVlr(cAtrib))
		Return cHtml
	EndIf
	
	//cHtml += "      <tr>" + CRLF

	If _lSim 
		cHtml += "      <tr style='background-color:#F0FFFF'>" + CRLF
		_lSim := .F.
	Else 
		cHtml += "      <tr style='background-color:#E0EEEE'>" + CRLF
		_lSim := .t.
	EndIf 

	cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF



	For nx:= 1 to len(::aoCroCmp)
		oObj := ::aoCroCmp[nx]

		uValor := &("oObj:" + cAtrib)
		If lVerCalcF .AND. ! ::aoCroCmp[nX]:lPeriodo  .and. ! cAtrib $ cListAtrib
			cHtml += "         <td> </td>" + CRLF
			If ++nc == 12
				nc := 0
				cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF
			EndIf
			Loop
		EndIf

		If Valtype(uValor) == "N"
			cHtml += "         <td align='RIGHT'>"
			If ::lDetPh5
				For ny := 1 to len(::aoCroCmp[nx]:aPH5)
					oDet    := ::aoCroCmp[nx]:aPH5[ny]
					uVlrDet := &("oDet:" + cAtrib)
					If ! Empty(uValor)
						cHtml += "[" + Alltrim(Str(oDet:nRecnoPH5)) + "] " + Alltrim(Str(uVlrDet, 15, 2))  + "<br>"
					EndIf
				Next
			EndIf
			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			If ! Empty(uValor)
				//cHtml += Alltrim(Str(uValor, 15, 2))
				cHtml += Alltrim(Transform(uValor, "@e 999,999,999.99" ))
			EndIf
		ElseIf Valtype(uValor) == "C"
			cHtml += "         <td align='LEFT'>"
			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			If ! Empty(uValor)
				cHtml += Alltrim(uValor)
			EndIf
		ElseIf Valtype(uValor) == "L"
			cHtml += "         <td align='LEFT'>"

			If uValor
				If ! Empty(cCor)
					cHtml += "<font COLOR='" + cCor + "'>"
				EndIf
				cHtml += "Sim"
			Else
				cHtml += "<font COLOR='RED'>"
				cHtml += "Não"
			EndIf
		ElseIf Valtype(uValor) == "A"
			cHtml += "         <td align='LEFT'>"
			If ! Empty(cCor)
				cHtml += "<font COLOR='" + cCor + "'>"
			EndIf
			aEval(uValor, {|x| cHtml += x + "<br>" })
		EndIf
		cHtml += "</td>" + CRLF
		If ++nc == 12
			nc := 0
			cHtml += "         <td><b> " + cTitulo + " </b></td>" + CRLF
		EndIf
	Next
	cHtml += "      </tr>" + CRLF
Return cHtml


Method GravaCSV(cArquivo) Class Tgcvxc05
	::cArqCSV := cArquivo

	Self:CabecCSV()
	Self:LinhaCSV()

Return

Method CabecCSV() Class Tgcvxc05
	Local cCabec    := ""
	Local nx        := 0
	Local cAtrib    := ""
	Local cTitulo   := ""
	Local uValor
	Local oObj

	If File(::cArqCSV)
		Return
	EndIf

	cCabec := ""
	oObj := ::aoCroCmp[1]
	For nx := 1 to len(::aEstruCSV)
		cTitulo := Alltrim(::aEstruCSV[nx, 1])
		cAlign  := Alltrim(Str(::aEstruCSV[nx, 2]))
		cFormat := Alltrim(Str(::aEstruCSV[nx, 3]))
		cAtrib  := ::aEstruCSV[nx, 4]
		If Empty(cAtrib)
			uValor := ""
		Else
			uValor := &("oObj:" + cAtrib)
		EndIf
		cTipo := ValType(uValor)
		cCabec += cTitulo + " " + cTipo + cAlign + cFormat + ";"
	Next
	GrvArq(::cArqCSV, cCabec)

Return

Method LinhaCSV() Class Tgcvxc05
	Local cAtrib    :={}
	Local nx        := 0
	Local nw        := 0
	Local uValor    := NIL
	Local oObj
	Local cTipo     := ""
	Local cLinha    := ""
	Local cConteudo := ""

	For nx:= 1 to ::nQtdMes
		cLinha  := ""
		oObj := ::aoCroCmp[nx]
		If  ::aoCroCmp[nx]:lIntegro
			Loop
		EndIf
		For nw := 1 to len(::aEstruCSV)
			cAtrib := ::aEstruCSV[nw, 4]
			If Empty(cAtrib)
				uValor := ""
			Else
				uValor := &("oObj:" + cAtrib)
			EndIf
			cTipo := ValType(uValor)

			If cTipo == "C"
				cConteudo := Alltrim(uValor)
			ElseIf cTipo == "N"
				uValor := Str(uValor)
				uValor := StrTran(uValor, ".", ",")
				uValor := Alltrim(uValor)
				cConteudo := uValor
			ElseIf cTipo == "D"
				cConteudo := dtoc(uValor)
			ElseIf cTipo == "L"
				cConteudo := If(uValor, "SIM", "NÃO")
			ElseIf cTipo == "A"
				cConteudo := ""
				aeval(uValor, {|x| cConteudo += x + ","})
			Else
				cConteudo := AllToChar(uValor)
			EndIf

			cLinha += cConteudo + ";"
		Next
		GrvArq(::cArqCSV, cLinha)
	Next

Return


Method EstruCSV() Class Tgcvxc05
	Local aEstru := {}

	// < Titulo >
	// < nAlign >  1-Left,2-Center,3-Right,
	// < nFormat > 1-General,2-Number,3-Monetário,4-DateTime
	// renferencia do atributo

	aadd(aEstru, {"CONTRATO"                     , 1, 1, "oCtbCro:cContrato"})
	aadd(aEstru, {"REVISÃO"                      , 1, 1, "oCtbCro:cRevisa"})
	aadd(aEstru, {"NUM PLANILHA"                 , 1, 1, "oCtbCro:cPlanilha"})
	aadd(aEstru, {"ITEM"                         , 1, 1, "oCtbCro:cItem"})
	aadd(aEstru, {"PRODUTO"                      , 1, 1, "oCtbCro:cProduto"})
	aadd(aEstru, {"QUANTIDADE CTR"               , 1, 2, "nQuant"})
	aadd(aEstru, {"VLR UNITÁRIO CTR"             , 3, 3, "oCtbCro:nVlrUniCtr"})
	aadd(aEstru, {"PERIODICIDADE"                , 1, 1, "cPeriodico"})
	aadd(aEstru, {"INÍCIO CONTRATO"              , 1, 1, "oCtbCro:dConIni"})
	aadd(aEstru, {"DT INIC CAR NA VENDA"         , 1, 1, "oCtbCro:cCarIniVnd"})
	aadd(aEstru, {"DT FIM CAR NA VENDA"          , 1, 1, "oCtbCro:cCarFinVnd"})
	aadd(aEstru, {"MES ANO"                      , 1, 1, "cMesAno"})
	aadd(aEstru, {"ANO MES"                      , 1, 1, "cAnoMes"})
	aadd(aEstru, {"SITUAÇÃO"                     , 1, 1, "cDescSitu"})
	aadd(aEstru, {"PERIODICIDADE"                , 1, 1, "cPeriodico"})
	aadd(aEstru, {"QUANTIDADE"                   , 1, 2, "nQuant"})
	aadd(aEstru, {"PERC RATEIO"                  , 3, 3, "nPerRat"})
	aadd(aEstru, {"UNITARIO"                     , 3, 3, "nVlrUni"})
	aadd(aEstru, {"TOTAL"                        , 3, 3, "nVlrTot"})
	aadd(aEstru, {"FATURADO"                     , 3, 3, "nVlrFat"})
	aadd(aEstru, {"FATURADO REF"                 , 3, 3, "nVlrFatRef"})
	aadd(aEstru, {"AJUSTE"                       , 3, 3, "nVlrAju"})
	aadd(aEstru, {"INTERA"                       , 3, 3, "nVlIntera"})
	aadd(aEstru, {"CARENCIA"                     , 3, 3, "nVlrCare"})
	aadd(aEstru, {"BONIFICACAO"                  , 3, 3, "nVlrBoni"})
	aadd(aEstru, {"CANCELADO"                    , 3, 3, "nVlrCanc"})
	aadd(aEstru, {"REATIVADO"                    , 3, 3, "nVlrReat"})
	aadd(aEstru, {"STATUS"                       , 1, 1, "cStatus"})
	aadd(aEstru, {"INTEGRIDADE"                  , 1, 1, "lIntegro"})
	aadd(aEstru, {"AÇÃO"                         , 1, 1, "cAcao"})
	aadd(aEstru, {"ERRO"                         , 1, 1, "aErro"})


Return aEstru


Method SelCmp(aAMSave) Class Tgcvxc05
	Local nx := 0
	Local aAMMark :={}

	aAMSave := {}

	For nx:= 1 to ::nQtdMes
		If ! (::aoCroCmp[nx]:cAcao == "Atualizar Cronograma" .or. ::aoCroCmp[nx]:cAcao == "Gerar Cronograma")
			Loop
		EndIf
		aadd(aAMMark, {.T., ::aoCroCmp[nx]:cMesAno})
	Next

	If Empty(aAMMark)
		MsgAlert("Contrato sem atualizações!!!")
		Return
	EndIf

	If ! Seleciona(aAMMark, "Atualização Contrato - Cliente:" +::cCliente + " - " + ::cLoja + " - " + ::cDescCli)
		Return
	EndIf
	If ! MsgYesNo('Confirma a atualização?')
		Return
	EndIf

	For nx:= 1 to len(aAMMark)
		If aAMMark[nx, 1]
			aadd(aAMSave, CmptoAM(aAMMark[nx, 2]) )
		EndIf
	Next

Return

Method CarregaGetMV() Class Tgcvxc05

::lNovTrat   := GetMv( 'TI_NOVTRT',,.T.) 
return 


Static Function Seleciona(aAMMark, cTitulo)
	Local oPanel1
	Local oCheck
	Local oDlg
	Local oLbx
	Local lOk		:= .F.
	Local aCab      := {" "," Competencias "}
	Local bClick    := {||}
	Local lCheck    := .T.

	DEFINE MSDIALOG oDlg TITLE cTitulo FROM 0,0 TO 470,400 PIXEL OF oMainWnd

	EnchoiceBar( oDlg, {||  lOk:= .t., oDlg:End()} , {|| oDlg:End() } )
	oPanel1 :=TPanel():New( 010, 001, ,oDlg, , , , , , 300, 202, .F.,.T. )
	oPanel1:align := CONTROL_ALIGN_ALLCLIENT
	oLbx:= TwBrowse():New(01,01,490,490,,aCab,, oPanel1,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
	oLbx:bLDblClick 	:= { || DBClick(oLbx,aAMMark) }
	oLbx:align := CONTROL_ALIGN_ALLCLIENT
	oLbx:SetArray( aAMMark )
	oLbx:bLine := {|| Retbline(oLbx,aAMMark) }
	oLbx:Refresh()

	bClick := {|| lCheck:=!lCheck,(AEval(aAMMark, {|x| x[1] := lCheck }), oLbx:Refresh()) }
	oCheck := TCheckBox():New(02,01,"",{|| lCheck}, oPanel1, 080, 020,,bClick,,,,,,.T.,,,)

	ACTIVATE MSDIALOG oDlg CENTERED

Return  lOk


Static Function DBClick(oLbx,aAMMark)

	aAMMark[oLbx:nAt,1] := ! aAMMark[oLbx:nAt,1]
	oLbx:SetArray( aAMMark )
	oLbx:bLine := {|| Retbline(oLbx,aAMMark) }
	oLbx:Refresh()

Return

Static Function RetbLine(oLbx,aLista)
	Local nx
	Local aRet	:= {}
	Local oOK	 	:= LoadBitmap( GetResources(), "LBTICK" 	)
	Local oNo	 	:= LoadBitmap( GetResources(), "LBNO" 	)
	For nX := 1 to len(aLista[oLbx:nAt])
		If nX == 1
			If aLista[oLbx:nAt,nX]
				aadd(aRet,oOK)
			Else
				aadd(aRet,oNo)
			EndIf

		Else
			aadd(aRet,aLista[oLbx:nAt,nX])
		EndIF
	Next
Return aclone(aRet)

Static Function GrvArq(cArquivo, cLinha, lCRLF)
	Local nHandle
	Default lCRLF:= .T.

	If ! File(cArquivo)
		If (nHandle := MSFCreate(cArquivo, 0)) == -1
			Return
		EndIf
	Else
		If (nHandle := FOpen(cArquivo, 2)) == -1
			Return
		EndIf
	EndIf
	FSeek(nHandle, 0, 2)
	FWrite(nHandle, cLinha + If(lCRLF, CRLF, ""))
	FClose(nHandle)

Return

	// ==========================================================================
	// tratamento de datas
Static Function AMtoCmp(cAM)
	Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp)
	Local cAM := Right(cCmp, 4) + Left(cCmp, 2)

Return cAM

Static Function AMCalcDif(cAMMenor, cAMMaior)
	Local nQtdMes:= 0
	Local cAno   := ""
	Local cMes   := ""
	Local cCmp   := ""

	If "/" $ cAMMenor  // se tiver barra está no formato MM/AAAA
		cAMMenor := Right(cAMMenor,4) + Left(cAMMenor,2)
	EndIf
	If "/" $ cAMMaior
		cAMMaior := Right(cAMMaior,4) + Left(cAMMaior,2)
	EndIf

	If Empty(cAMMenor)
		Return nQtdMes
	EndIf

	cCmp := cAMMenor
	While cCmp < cAMMaior
		cAno := Left(cCmp, 4)
		cMes := Right(cCmp, 2)
		If cMes == "12"
			cMes := "01"
			cAno := Soma1(cAno)
		Else
			cMes := Soma1(cMes)
		EndiF
		cCmp := cAno + cMes
		nQtdMes++
	End

Return nQtdMes

Static Function AdicMes(cMesAno, nMes)
	Local cMes := ""
	Local cAno := ""
	Local cMAAux := cMesAno
	Local nx

	If nMes > 0
		For nx := 1 to nMes
			cMes:= Left(cMAAux, 2)
			cAno:= Right(cMAAux, 4)
			If cMes == '12'
				cMAAux := '01/' + Soma1(cAno)
			Else
				cMAAux := Soma1(cMes) + '/' + cAno
			EndIf
		Next
	ElseIf nMes < 0
		For nx := nMes to -1
			cMes:= Left(cMAAux, 2)
			cAno:= Right(cMAAux, 4)
			If cMes == '01'
				cMAAux := '12/' + Tira1(cAno)
			Else
				cMAAux := Tira1(cMes) + '/' + cAno
			EndIf
		Next
	EndIf

Return cMAAux

	// ==========================================================================




