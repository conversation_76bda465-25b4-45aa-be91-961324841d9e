#include "totvs.ch"

/* Projetos

TIADMVIN-3138
TIADMVIN-4354 INTEGRACAO DE PROPOSTA - TGCVA232

*/

User Function Tgcvxc06
Return 

Class Tgcvxc06
	Data oCtbCro
	Data oCroCmp
	Data nMes
	Data cMesAno
	Data cAnoMes
	Data cSituac
	Data cDescSitu
	Data nPeriodico
	Data cPeriodico
	Data cRevisa
	Data nQtdRev
	Data nVlrRev
	Data cRatHist
	
	Data nQuant
	Data nRQuant
	Data nQtdCtr
	Data nQtdCar
    Data nQtdProgC
    Data nQtdProg
	Data nRQtdProg
	Data nQtdCan
	Data nRQtdCan
	Data cMotCan
	Data nQtdFat
	Data nMultaUni
	Data nMultCan
    Data nQtdReat
	Data nRQtdReat
	Data nQtdRetro
	Data nQtdRetCA
	Data nQtdTraE
	Data nRQtdTraE
	Data nQtdTraS
	Data nRQtdTraS
    Data nQtdTroca
    Data nQtdTrCalc
	Data nPerRat
    Data nPerREX
	Data nVlAjuFin
	Data nPDesComis
	Data nVlIntera
	Data nVlIntCan
	
	     
	Data nVlrUni
	
	Data nVlrUniB
	Data nVlrUniI
	Data nVlrUniIA
	Data nVlrUIARE

	Data nVlrTot
	Data nVlrFat
	Data nVlrFatS
	Data nVlrFatRef
	Data nVlrImp
	Data nVlrImpT
	Data nVlrVelho
	Data nVlrTotV
	Data nVlrAju
	Data nVlrTAju
	Data nVlTAjFin
	Data nVlTIntera
	Data nVlTIntCan
	Data nVlrTraE
	Data nVlrTraS
	Data nVlrVNova
	Data nVlrUAud
	Data nVlrTAud
	Data nVlrTotIA

	Data nVlrCare
	Data nVlrUBoni
	Data nVlrBoni
	Data aCalcBoni
	Data nVlrProg
	Data nVlrCanc
	Data nVlrReat
	Data nVlrIncr
	Data nVlrInct
	Data nVlIncAn
	Data nVlrAud
	Data nVlrREX
	Data nVlrREXT
	Data cFlCorp

	Data nBilUpDown
	Data cBilId
	Data nBilVlTot
	Data nBilQtde
	Data nBilVaric
	Data nBilRec
	Data nBilUp
	Data nBilDown

	Data nRoyVlr
	Data nRoyVar
    Data cRoyId
    Data cRoySt

	Data nDeltaTrc
	Data nRDeltaTrc
	Data cIdDltTrc
	Data cTpTroca
	Data nVlrTrc
	Data nRVlrTrc
    
	Data lPeriodo
	Data lIniPer
	Data lGeraCro
	Data lCalcFat
	Data lCarencia
	Data lCancelado
	Data lBonifica
	Data lFinanceiro
	Data lIniIncr
    Data lTroca
    Data lFinalizado
	Data lRecalc
	Data lManual
    
	Data cStatus
	Data lIntegro
	Data cAcao
	Data aErro

	Data nPH5VlrTot
	Data nPH5VlTotT
	Data nPH5VlTrib
	Data nPH5VlAFat
	Data nPH5VlAFaS
	Data nPH5VlFat
	Data nPH5VlFatS
    Data nPH5VlSld
    Data nPH5QtdFat
    Data nPH5Quant
    Data nPH5QtdCan 
    Data nPH5QtdRea 
	Data nPH5VlCar
	Data nPH5VlBon
	Data nPH5VlBonF
	Data nPH5VlCan
	Data nPH5VLInt
	Data nPH5IntCan
	Data nPH5VlReat
	Data nPH5VlReaj
	Data nPH5VlInc
	Data nPH5VlIncF
	Data nPH5VlNova
	Data nPH5BilUp
	Data nPH5BilDown
	Data nPH5BilQtd
	Data nPH5BilVlr
	Data nPH5BilVar

	Data nPH5VlTraS
	Data nPH5VlTraE

	Data nPH5RoyVlr
	Data nPH5RoyVar
	Data cPH5RoyId

	Data nPH5VlREX
	Data nPH5VlTroca
	Data cPH5FlCorp
	Data nPH5Multa
    Data cPH5Propos
    Data cPH5NOTASE
	Data cPH5CondPg
    Data nPH5PerRat
    Data cPH5Situac
    Data cPH5StatRM

	Data nVlrRepre
	Data aPH5

	Data nAtuQuant
	Data nAtuQtFat
	Data nAtuQtCare
	Data nAtuQtCan
	Data nAtuQtReat
	Data nAtuQtTra
	Data nAtuVlrUni
	Data nAtuVlrTot
	Data nAtuVlrImp
	Data nAtuVlrUnFa
	Data nAtuVlrFat
	Data nAtuVlrFaS
	Data nAtuVlrCar
	Data nAtuVlrBon
	Data nAtuVlrCan
	Data nAtuVlrReat
	Data nAtuVlrIncr
	Data nAtuVlrAud
	Data nAtuVlrRE
	Data nAtuBilUp
	Data nAtuBilDown
	Data nAtuBilQtd
	Data nAtuBilVlr
	Data nAtuBilVar
	Data nAtuRoyVar
	Data nAtuVlrTrc
	Data cAtuRoyId
    Data nAtuMulta
    Data nAtuPerRat
	Data nAtuVlrREX
	Data cAtuFlCorp
	Data cNewSeq
    Data cStatPH7
    Data cStatPH6
	Data nCorVlrIncr
	Data nCorUni
	Data nCorBoni
	Data nCorUniB
	Data nCorImp
	Data nCorUniI
	Data nCorAud
	Data nCorTAud
	Data nCorUAud
	Data nCorTotIA
	Data nCorUniIA
	Data nCorREXT
	Data nCorREX
	Data nCorUIARE
	Data nCorFatRef
	Data nCorImpT
	Data nCorFat
    Data nCorFatS
    Data nPerRatF
	Data nVlrPWH
	Data nVlrSoft

    Data cPedVen
    DATA cItemPv  
    DATA cNumMed 
    DATA cItemMd 
    DATA cNota    
    DATA cSerie   
    DATA cItemNF  
    DATA dDtMoeda 
    DATA nTxMoeda 
    DATA nVlMoeda 
    DATA nUniFT2  
    DATA nVlFAT2 
	DATA lExcecao 
    
	Method New(oCtbCro, nMes)
	Method Calc()

	Method SetCNB()
	Method ChkManual()
	Method Reajuste()
	Method Incremento()
	Method Carencia()
	Method ReajExtra()
	Method Bonifica()
	Method Transfere()
	Method Billing()
	Method Royalties()
	Method LoadFinCmp()
    Method Financeiro()
    Method RetPH5()

	Method Valores()
	Method NCCValores()
	Method CorpValores()
	Method CalcBoni()
    Method CheckErro()
    Method VerCro()
    Method VerEnd()
    Method VerNovo()
    Method VerAltera()
	Method Salva()
	Method VlrMensal()
	Method ProxSeq()
	Method QuebraSeq()
	Method TrataSeqMed(cNewSeq) 
    Method AtuDoc()
	Method AtuDbPWH()
	Method AtuModPWH()  
	Method ProxSeqM(oContrato, oModel)
	Method SalvaCtr(oContrato, lReIndex)
	Method CmpAnt(cAtrib)
	Method VlrAtuPH5()
	Method DelPH5()
	Method Free()
	Method ZeraRecalc()
	Method LimpaRecalc()
	Method Formulas()
	Method RetaFormula()
    Method AddErro()
    Method AtuMed()
EndClass


Method New(oCtbCro, nMes) Class Tgcvxc06
	::oCtbCro    := oCtbCro
	::nMes       := nMes
	::cMesAno    := StrZero(Month(::oCtbCro:dDtIni), 2) + "/" + Strzero(Year(::oCtbCro:dDtIni), 4)
	If nMes > 1
		::cMesAno  := AdicMes(::cMesAno, nMes - 1)
	EndIf
	::cAnoMes    := Right(::cMesAno, 4) + Left(::cMesAno, 2)

	::nQuant     := 0
	::nRQuant    := 0
	::nQtdCtr	 := 0
	::nQtdCar    := 0

    ::nQtdProgC  := 0
    ::nQtdProg   := 0
	::nRQtdProg  := 0
	::nQtdCan    := 0
	::nRQtdCan   := 0
	::cMotCan    := ""
	::nQtdFat    := 0
	::nMultaUni  := 0
	::nMultCan   := 0
    ::nQtdReat   := 0
	::nRQtdReat  := 0
	::nQtdRetro  := 0
	::nQtdRetCA	 := 0
	::nQtdTraE   := 0
	::nRQtdTraE  := 0
	::nQtdTraS   := 0
	::nRQtdTraS  := 0
    ::nQtdTroca  := 0
    ::nQtdTrCalc := 0
	::nPerRat    := 0
	::nPerREX    := 0
	::nVlAjuFin  := 0
	::nPDesComis := 1
	::nVlIntera  := 0
	::nVlIntCan  := 0
	::nVlrUni    := 0
	
	::nVlrUniB   := 0
	::nVlrUniI   := 0
	::nVlrUniIA  := 0
	::nVlrUIARE:= 0

	::aCalcBoni  := {}
	::nVlrTot    := 0
	::nVlrFat    := 0
	::nVlrFatRef := 0
	::nVlrFatS   := 0
	::nVlrImp    := 0
	::nVlrImpT   := 0
	::nVlrVelho  := 0
	::nVlrTotV   := 0
	::nVlrAju    := 0
	::nVlrTAju   := 0
	::nVlTAjFin  := 0
	::nVlTIntera := 0
	::nVlTIntCan := 0
 	::nVlrTraE   := 0
	::nVlrTraS   := 0
	::nVlrVNova  := 0
	::nVlrUAud   := 0
	::nVlrTAud   := 0
	::nVlrTotIA  := 0

	::nVlrCare   := 0
	::nVlrUBoni  := 0
	::nVlrBoni   := 0
	::nVlrProg   := 0
	::nVlrCanc   := 0
	::nVlrReat   := 0
	::nVlrIncr   := 0
	::nVlrInct   := 0
	::nVlIncAn	 := 0
	::nVlrAud    := 0
	::nVlrREX    := 0
	::nVlrREXT   := 0
	::cFlCorp    := " "

	::nBilUpDown := 0
	::cBilId     := ""
	::nBilVlTot  := 0
	::nBilQtde   := 0
	::nBilVaric  := 0
	::nBilRec    := 0
	::nBilUp     := 0
	::nBilDown   := 0
	::nRoyVlr    := 0
	::nRoyVar    := 0
    ::cRoyId     := ""
    ::cRoySt     := ""

	::nDeltaTrc   := 0
	::nRDeltaTrc  := 0
	::cIdDltTrc   := ""
	::cTpTroca    := ""
	::nVlrTrc     := 0
	::nRVlrTrc    := 0
    
    ::nPeriodico  := 0
	::cPeriodico  := ""

	::lPeriodo    := .F.
	::lIniPer     := .F.
	::lGeraCro    := .F.
	::lCalcFat    := .F.
	::lCarencia   := .F.
	::lCancelado  := .F.
    ::lTroca      := .F.
    ::lFinalizado := .F.
	::lRecalc     := .F.
	::lManual     := .F. 

	::lBonifica   := .F.
	::lFinanceiro := .F.
	::lIniIncr    := .F.

	::cStatus     := ""
	::lIntegro    := .T.
	::cAcao       := ""
	::aErro       := {}

	::nPH5VlrTot  := 0
	::nPH5VlTotT  := 0
	::nPH5VlTrib  := 0
	::nPH5VlAFat  := 0
	::nPH5VlAFaS  := 0
	::nPH5VlFat   := 0
	::nPH5VlFatS  := 0
    ::nPH5VlSld   := 0
    ::nPH5QtdFat  := 0
    ::nPH5Quant   := 0
    ::nPH5QtdCan  := 0
    ::nPH5QtdRea  := 0
	::nPH5VlCar   := 0
	::nPH5VlBon   := 0
	::nPH5VlBonF  := 0
	::nPH5VlCan   := 0
	::nPH5VlInt   := 0
	::nPH5IntCan  := 0
	::nPH5VlReat  := 0
	::nPH5VlReaj  := 0
	::nPH5VlInc   := 0
	::nPH5VlIncF  := 0
	::nPH5VlNova  := 0
	::nPH5BilUp   := 0
	::nPH5BilDown := 0
	::nPH5BilQtd  := 0
	::nPH5BilVlr  := 0
	::nPH5BilVar  := 0
	::nPH5VlTraS  := 0
	::nPH5VlTraE  := 0
	::nPH5RoyVlr  := 0
	::nPH5RoyVar  := 0
	::cPH5RoyId   := ""
	::nPH5VlREX   := 0
	::nPH5VlTroca := 0
	::nPH5Multa   := 0
	::nVlrRepre   := 0
    ::cPH5Propos  := ""
    ::cPH5NOTASE  := ""
	::cPH5CondPg  := ""
    ::nPH5PerRat  := 0
    ::cPH5Situac  := ""
    ::cPH5StatRM  := ""
	::aPH5        := {}


	::nAtuQuant   := 0
	::nAtuQtFat   := 0
	::nAtuQtCare  := 0
	::nAtuQtCan   := 0
	::nAtuQtReat  := 0
	::nAtuQtTra   := 0
	::nAtuVlrUni  := 0
	::nAtuVlrTot  := 0
	::nAtuVlrImp  := 0
	::nAtuVlrUnFa := 0
	::nAtuVlrFat  := 0
	::nAtuVlrFaS  := 0
	::nAtuVlrCar  := 0
	::nAtuVlrBon  := 0
	::nAtuVlrCan  := 0
	::nAtuVlrReat := 0
	::nAtuVlrIncr := 0
	::nAtuVlrAud  := 0
	::nAtuVlrREX  := 0
	::nAtuBilUp   := 0
	::nAtuBilDown := 0
	::nAtuBilQtd  := 0
	::nAtuBilVlr  := 0
	::nAtuBilVar  := 0
	::nAtuRoyVar  := 0
	::nAtuVlrTrc  := 0
	::cAtuRoyId   := ""
    ::nAtuMulta   := 0
    ::nAtuPerRat  := 0
	::cRevisa     := ""
	::nQtdRev     := 0
	::nVlrRev     := 0
	::cRatHist    := ""
	::cNewSeq     := "001"
    ::cStatPH7    := ""
    ::cStatPH6    := ""

	::nCorVlrIncr := 0
	::nCorUni     := 0
	::nCorBoni    := 0
	::nCorUniB    := 0
	::nCorImp     := 0
	::nCorUniI    := 0
	::nCorTAud    := 0
	::nCorUAud    := 0
	::nCorAud     := 0
	::nCorTotIA   := 0
	::nCorUniIA   := 0
	::nCorREXT    := 0
	::nCorREX     := 0
	::nCorUIARE   := 0
	::nCorFatRef  := 0
	::nCorImpT    := 0
	::nCorFat     := 0
    ::nCorFatS    := 0
    ::nPerRatF    := 0
	::nVlrPWH     := 0
	::nVlrSoft    := 0
	  

    ::cPedVen  := ""
    ::cItemPv  := ""
    ::cNumMed  := ""
    ::cItemMd  := ""
    ::cNota    := ""
    ::cSerie   := ""
    ::cItemNF  := ""
    ::dDtMoeda := ctod("")
    ::nTxMoeda := 0
    ::nVlMoeda := 0 
    ::nUniFT2  := 0
    ::nVlFAT2  := 0
	::lExcecao := .F.
	
			
	If ::cAnoMes == ::oCtbCro:cAMAtu
		::oCtbCro:nMesAtu := ::nMes
	EndIf

Return


Method Calc() Class Tgcvxc06

    If IsDebug(self)  // no console do debug use o SetDebug para informar os parametros, ex: setdebug("201903", "000001", "000001")  //ano mes, planilha, item
        AllWaysTrue() //("Debug aqui")//VARIAVEIS STATICAS __cAnoMes __cNumero __cItem   
    EndIf

	Self:Carencia()
	Self:SetCNB()
	Self:Incremento()
	Self:Bonifica()
	If !::lRecalc
		Self:ReajExtra()
		Self:Transfere()
		Self:Billing()
		Self:Royalties()
	EndIf
	
	Self:Financeiro()
	
    
	Self:Valores()
	Self:NCCValores()
	
	Self:CorpValores()
	Self:CheckErro()
	
	Self:VlrAtuPH5()
	

Return

Method SetCNB() Class Tgcvxc06

    // ===================================== Quantidade na competencia ========================================
    ::nQuant := ::nQtdRev - ::nQtdProgC +  ::nQtdRetro + ::nQtdProg + ::nQtdTrCalc 
    
    If (Empty(::nQuant )             .and. ! ::cSituac $ "CO") .or. ;
       (Empty(::nQuant - ::nQtdProg) .and. ! ::cSituac $ "CO")  

        ::cSituac := "C" //Cancelamento ou tranferencia Total
		::lCancelado := .T.
		If Empty(::oCtbCro:cAnoMesC)
			::oCtbCro:cAnoMesC := ::cAnoMes
		EndIf
	ElseIf Empty(::nQuant - ::nQtdTroca) .and. ::cSituac == "O"
        If Empty(::oCtbCro:cAnoMesO)
            ::oCtbCro:cAnoMesO := ::cAnoMes
        EndIf
    Else
		If ! Empty(::oCtbCro:cSitAtiAnt ) .and. ::cSituac $ "CO"
			If ::nQuant > 0
				::cSituac := ::oCtbCro:cSitAtiAnt 
			EndIf
        EndIf
    EndIf

    // ===================================== define situacao ========================================
    If ! ::cSituac $ "CO"
		::lCancelado := .F.
		If ! Empty(::oCtbCro:cAnoMesC)
			::oCtbCro:cAnoMesC := ""
		EndIf
	EndIf
	::cDescSitu  := ::oCtbCro:DescSitu(::cSituac)
	
    
    // ===================================== define valor unitario ========================================
	// Identifica o Valor unitario 
	If Empty(::nVlrRev)
		::nVlrRev := ::oCtbCro:nVlrUniCtr
	EndIf
	::nVlrUni   := ::nVlrRev 
	::nVlrVelho := ::nVlrUni - ::nVlrAju - ::nVlIntera
	::nVlrTotV  := Round(::nVlrVelho , 2) * (::nQuant * ::nPerRat / 100)

	If ! ::lFinalizado .and. ! ::lCarencia
		Self:ChkManual()
		If ::lManual
			::nVlrUni   := ::oCtbCro:nVlrUniCtr
			::nVlrVelho := ::oCtbCro:nVlrUniCtr
			::nVlrTotV  := Round(::nVlrVelho , 2) * (::nQuant * ::nPerRat / 100)
		EndIf  
	EndIf 
	

Return

Method ChkManual() Class Tgcvxc06
	Local cRevUlt := ""
	Local nVlUltAju := 0
	

	If Len(::octbcro:areajuste) > 0
		nVlUltAju := ::octbcro:areajuste[Len(::octbcro:areajuste), 3] // ultimo valor
		cRevUlt := ::octbcro:areajuste[Len(::octbcro:areajuste), 4] // revisão
	EndIf 

	If Len(::octbcro:aP68Intera) > 0 .and. ::octbcro:aP68Intera[Len(::octbcro:aP68Intera), 6] > cRevUlt
		nVlUltAju := ::octbcro:aP68Intera[Len(::octbcro:aP68Intera), 5]// ultimo valor
	EndIf 

	If nVlUltAju <> NoRound(::oCtbCro:nVlrUniCtr, 2) 
		::lManual := .t. 
	EndIf 

Return 


Method Incremento() Class Tgcvxc06
	Local ny := 0
	Local aReajuste := ::oCtbCro:aReajuste
	
	
	If ::lManual
		Return 
	EndIf 


	::nVlrIncr	:=  0
	::nVlrInct	:= 0

	For ny := 1 to len(aReajuste)

		If ::cAnoMes < aReajuste[ny, 1]
			Loop
		EndIf

		//Data de reajuste estava menor que a competencia a ser aplicada. por isto troquei
		//para comparar com a competencia de aplicação.
		If ::cAnoMes > Left(aReajuste[ny, 6], 6) .and. ::cAnoMes > aReajuste[ny, 1] 
			Loop
		EndIf
		If ! aReajuste[ny, 5] $ "COI;COD" // Se não for coorporativo não ajusta aqui o valor unitario
			Loop
		EndIf
		If ::cAnoMes >= aReajuste[ny, 1]
			::nVlrUni := aReajuste[ny, 3] //nVlrUni
		EndIf
		If ::cAnoMes < Left(aReajuste[ny, 6], 6)
			//::nVlrUni := aReajuste[ny, 3]
			::nVlrRev := ::nVlrUni
			::nVlrTot := (::nQtdRev * ::nPerRat / 100) * ::nVlrUni
		EndIf
		If ::cAnoMes == Left(aReajuste[ny, 6], 6) .OR. ;
		    (::cAnoMes == aReajuste[ny, 1] .AND. Left(aReajuste[ny, 6], 6) < ::cAnoMes) .OR.;
			(::cAnoMes >= aReajuste[ny, 1] .AND. Left(aReajuste[ny, 6], 6) > ::cAnoMes)

			::lIniIncr := .T.
			::oCtbCro:lCorpora := .T.
			::nVlrIncr += (::nQtdRev * ::nPerRat / 100) * (aReajuste[ny, 3] - aReajuste[ny, 2])
			::nVlrInct += ::nQtdRev * (aReajuste[ny, 3] - aReajuste[ny, 2]) 

		EndIf
		If ::cAnoMes $ aReajuste[ny, 1]
			::nVlIncAn := aReajuste[ny, 3]
		EndIf
		::nVlrAju  := 0
	Next

Return

Method ReajExtra() Class Tgcvxc06
	Local nx         := 0
	Local aReajExtra := ::oCtbCro:aReajExtra
    Local cAMIni     := ""
    Local cAMFim     := ""
	Local cLinRec    := ""
	Local nPerREX    := 0
    Local dLimCtr    := ctod("")
    Local cCorp      := ""
    Local cFase      := ""
    Local cObs       := ""
    Local lLinReEsp  := .F.

	// tenta com linha de receita
	For nx := 1 to Len(aReajExtra)
        cAMIni     := aReajExtra[nx, 1]
        cAMFim     := aReajExtra[nx, 6]

		cLinRec    := aReajExtra[nx, 3]
        dLimCtr    := aReajExtra[nx, 5]
        cCorp      := aReajExtra[nx, 7]
        cFase      := aReajExtra[nx, 8]
        cObs       := aReajExtra[nx, 9]
        
		If cLinRec <> Left(::oCtbCro:cLinRec, 2)
			Loop
		EndIf

        lLinReEsp := .t.
        If Dtos(dLimCtr) < Dtos(::oCtbCro:dDtIni)
			Loop
		EndIf
        
        If ::cAnoMes < cAMIni
			Loop
        EndIf
        
        If ! Empty(cAMFim) .and. ::cAnoMes > cAMFim
			Loop
        EndIf

        If ! Empty(cCorp) .and. ::oCtbCro:lCorpora .and. cCorp <> "1"
            Loop 
        EndIf 
        If cFase > "1" .and. ! ::oCtbCro:cPropos $ cObs
            Loop 
        EndIf         

        nPerREX    += aReajExtra[nx, 4]
	Next
	// tenta com linha de receita em branco
	If ! lLinReEsp
		For nx := 1 to Len(aReajExtra)
            cAMIni     := aReajExtra[nx, 1]
            cAMFim     := aReajExtra[nx, 6]
			cLinRec    := aReajExtra[nx, 3]
            dLimCtr    := aReajExtra[nx, 5]
            cCorp      := aReajExtra[nx, 7]
            cFase      := aReajExtra[nx, 8]
            cObs       := aReajExtra[nx, 9]
            
            If Dtos(dLimCtr) < Dtos(::oCtbCro:dDtIni)
			    Loop
		    EndIf

			If ::cAnoMes < cAMIni
				Loop
			EndIf
			If ! Empty(cLinRec)
				Loop
            EndIf
            
            If ! Empty(cAMFim) .and. ::cAnoMes > cAMFim
			    Loop
            EndIf
            If ! Empty(cCorp) .and. ::oCtbCro:lCorpora .and. cCorp <> "1"
                Loop 
            EndIf 
            If cFase > "1" .and. ! ::oCtbCro:cPropos $ cObs
                Loop 
			EndIf 
			
			nPerREX    += aReajExtra[nx, 4]
			
		Next
	EndIf
	::nPerREX := nPerREX

Return



Method Carencia() Class Tgcvxc06
	Local nx:= 0
	Local aCarencia := ::oCtbCro:aCarencia
	Local cCliente  := ""
	Local cLoja     := ""
	Local cAMIni    := ""
    Local cAMFin    := ""
    Local lPrincipal:= .F.

	::lCarencia := .F.

	For nx:= 1 to len(aCarencia)
		cCliente  := aCarencia[nx, 5]
		cLoja     := aCarencia[nx, 6]
		cAMIni    := CmptoAM(aCarencia[nx, 2])
        cAMFin    := CmptoAM(aCarencia[nx, 3])
        lPrincipal:= aCarencia[nx, 7]

        If ::nPerRat < 100 .and. ! lPrincipal
		    If ! Empty(cCliente + cLoja) .and. ! cCliente + cLoja == ::oCtbCro:cCliente + ::oCtbCro:cLoja
    			Loop
            EndIf
        EndIf
		If ::cAnoMes >= cAMIni .and. ::cAnoMes <= cAMFin
			::lCarencia := .T.
			Exit
		EndIf
	Next
Return

Method Bonifica() Class Tgcvxc06
	Local nx:= 0
	Local aBonifica := ::oCtbCro:aBonifica
	Local cCliente  := ""
	Local cLoja     := ""
	Local cAMIni    := ""
	Local cAMFin    := ""
	Local cTpDesc   := ""
	Local lVlrInfo  := .F.
	Local nVlrBoni  := 0
	LOcal nPerBoni  := 0

	::aCalcBoni := {}
	::lBonifica := .F.
	::nVlrBoni  := 0

 	For nx:= 1 to len(aBonifica)
		cCliente  := aBonifica[nx, 5]
		cLoja     := aBonifica[nx, 6]
		cAMIni    := CmptoAM(aBonifica[nx, 2])
		cAMFin    := CmptoAM(aBonifica[nx, 3])

		cTpDesc   := aBonifica[nx, 7]
		nVlrBoni  := aBonifica[nx, 8]
        nPerBoni  := aBonifica[nx, 9]
        lVlrInfo   := .F.
            
        If ::nPerRat < 100
			If !Empty(cCliente + cLoja) 
				If cCliente + cLoja == Self:oCtbCro:cCliente + Self:oCtbCro:cLoja
					lVlrInfo := .T.
				Else
					Loop
				EndIf
			Else
				lVlrInfo := .T. //atendendo pedido aplica valor cheio para todos clientes 02/12/2019
            EndIf  
        EndIf
		If ::cAnoMes >= cAMIni .and. ::cAnoMes <= cAMFin
			::lBonifica := .t.
			If cTpDesc == "P" 
				lVlrInfo := .T.
			EndIf

            aadd(::aCalcBoni, {cTpDesc, nVlrBoni, nPerBoni, lVlrInfo })
		EndIf
	Next
Return

Method Transfere() Class Tgcvxc06
	Local nx:= 0
    Local aTransf   := ::oCtbCro:aTransf
    Local cSituac   := ""
    Local cAnoMesTE := ""
    Local cAnoMesTS := ""
    Local cCtrOrig  := ""
    Local cCtrDest  := ""
    Local nQtdTTraE := 0
    Local nQtdTTraS := 0
    Local nQtdTraE  := 0
    Local nQtdTraS  := 0
	Local nSaldo    := 0
	Local nSldTrf	:= 0


    For nx:= 1 to len(aTransf)
        nSaldo := aTransf[nx, 10]
		If nSaldo > 0 
			cAnoMesTE := aTransf[nx, 1]
			cCtrOrig  := aTransf[nx, 7]
			nQtdTTraE += nSaldo
			If ::cAnoMes == aTransf[nx, 1]
				nQtdTraE := nSaldo
			EndIf
			If ::cAnoMes >= aTransf[nx, 1]
				nSldTrf += nSaldo
			EndIf
		ElseIf nSaldo < 0
			cAnoMesTS:= aTransf[nx, 1]
            cCtrDest  := aTransf[nx, 8]
            nQtdTTraS += nSaldo * -1
            
			If ::cAnoMes == aTransf[nx, 1]
				nQtdTraS  := nSaldo * -1
			EndIf
			If aTransf[nx, 9]  + aTransf[nx, 10] == 0  .and. ::cAnoMes >= aTransf[nx, 1]
				nSldTrf := 0
				cSituac := "C" // na transferencia fica como cancelado
			EndIf
		EndIf
	Next
	
	If nSldTrf > 0
		cSituac := ::cSituac
	EndIf
	 
    If ! Empty(cAnoMesTE) 
        ::oCtbCro:cAnoMesTE := cAnoMesTE
        ::oCtbCro:cCtrOrig  := cCtrOrig
        ::oCtbCro:nQtdTraE  := nQtdTTraE
        ::nQtdTraE          := nQtdTraE
    EndIf

    If ! Empty(cAnoMesTS) 
        ::oCtbCro:cAnoMesTS := cAnoMesTS
        ::oCtbCro:cCtrDest  := cCtrDest
        ::oCtbCro:nQtdTraS  := nQtdTTraS
        ::nQtdTraS          := nQtdTraS            
        If ! Empty(cSituac)
            ::cSituac := cSituac // na transferencia fica como cancelado
            ::cDescSitu  := ::oCtbCro:DescSitu(::cSituac)
        EndIf
    EndIf

Return

Method Billing() Class Tgcvxc06
	Local nx:= 0
	Local aBilling  := ::oCtbCro:aBilling


	For nx:= 1 to len(aBilling)
		If ! aBilling[nx, 1] == ::cAnoMes
			Loop
		EndIf
		::cBilId    := aBilling[nx, 2]
		::nBilVlTot := aBilling[nx, 3]
		::nBilQtde  := aBilling[nx, 4]
		::nBilVaric := aBilling[nx, 5]
		::nBilRec   := aBilling[nx, 6]
	Next

Return

Method Royalties() Class Tgcvxc06
	Local nx:= 0
	Local aRoyalties  := ::oCtbCro:aRoyalties

    For nx:= 1 to len(aRoyalties)
		If ! aRoyalties[nx, 1] == ::cAnoMes
			Loop
		EndIf
		::nRoyVlr   := aRoyalties[nx, 2]
		::nRoyVar   := aRoyalties[nx, 3]
        ::cRoyId    := aRoyalties[nx, 4]
        ::cRoySt    := aRoyalties[nx, 5]
	Next
Return

Method LoadFinCmp() Class Tgcvxc06
	Local nx          := 0
	Local aFinanceiro := ::oCtbCro:aFinanceiro
	Local aInfFat     := {}

	For nx:= 1 to len(aFinanceiro)
		If ::cAnoMes != Self:RetPH5(nX, "PH5_ANOMES") .or. ; 
		  (::oCtbCro:cCliente + ::oCtbCro:cLoja) != (Self:RetPH5(nX, "PH5_CLIENT") + Self:RetPH5(nX, "PH5_LOJA"))
			Loop
		EndIf

		If Empty(Self:RetPH5(nX, "PH5_NUMMED") )
			Loop
		EndIf
		
		AADD(aInfFat, aClone(aFinanceiro[nx]))
		
	Next

	::oCroCmp := Tgcvxc16():New(aInfFat, ::oCtbCro:cCliente, ::oCtbCro:cLoja)
	::oCroCmp:CalcTotais() 

Return


Method Financeiro() Class Tgcvxc06
	Local nx:= 0
	Local aFinanceiro  := ::oCtbCro:aFinanceiro

	Local nPH5VlAFat := 0
	Local nPH5VlAFaS := 0
	Local nPH5VlFat  := 0
	Local nPH5VlFatS := 0
	Local nPH5VlrTot := 0
	Local nPH5VlTrib := 0
    Local nPH5VlSld  := 0
    Local nPH5QtdFat := 0
    Local nPH5Quant  := 0
    LOCAL nPH5QtdCan := 0
    LOCAL nPH5QtdRea := 0
	Local nPH5VlCar  := 0
	Local nPH5VlBon  := 0
	Local nPH5VlBonF := 0
	Local nPH5VlCan  := 0
	Local nPH5VLInt  := 0
	Local nPH5IntCan := 0
	Local nPH5VlReat := 0
	Local nPH5VlReaj := 0
	Local nPH5VlInc  := 0
	Local nPH5VlIncF := 0
	Local nPH5VlNova := 0
	Local nVlrRepre  := 0
	Local nPH5BilUp  := 0
	Local nPH5BilDown:= 0
	Local nPH5BilQtd := 0
	Local nPH5BilVlr := 0
	Local nPH5BilVar := 0
	Local nPH5VlTraS := 0
	Local nPH5VlTraE := 0
	Local nPH5RoyVar := 0
	Local cPH5RoyId  := ""
	Local nPH5Multa  := 0
	Local nPH5VlREX  := 0
	Local nPH5VlTroca:= 0
	Local oPH5       := NIL
    Local cPH5NOTASE := ""
    Local cPH5CondPg := ""
    Local cPH5Situac := ""
    Local cPH5StatRM := ""
	Local nPH5PerRat := 0
	Local nPerFat	 := 0

	::lFinanceiro := .F.

	For nx:= 1 to len(aFinanceiro)
		If ! ::cAnoMes == Self:RetPH5(nX, "PH5_ANOMES")
			Loop
		EndIf
		
        If ! Self:RetPH5(nX, "PH5_CLIENT") + Self:RetPH5(nX, "PH5_LOJA") == ::oCtbCro:cCliente + ::oCtbCro:cLoja 
            Loop
        EndIf
        
		
		::cStatPH7 := Self:RetPH5(nX, "PH7_STATUS")
        ::cStatPH6 := Self:RetPH5(nX, "PH6_STATUS")

		oPH5 := Tgcvxc07():New(::oCtbCro, Self)

		oPH5:cNota         := Self:RetPH5(nX, "PH5_NOTA") 
		oPH5:cSerie        := Self:RetPH5(nX, "PH5_SERIE") 
		oPH5:cItemNf       := Self:RetPH5(nX, "PH5_ITEMNF") 
		oPH5:cStatusFin    := Self:RetPH5(nX, "PH6_STATUS") 
		oPH5:cNumMed       := Self:RetPH5(nX, "PH5_NUMMED") 
		oPH5:cSequen       := Self:RetPH5(nX, "PH5_SEQ") 

		nPerFat := 1
		If Self:RetPH5(nX, "PH5_VLRFAT") > 0
			nPerFat := Self:RetPH5(nX, "PH5_SLDCAN") / Self:RetPH5(nX, "PH5_VLRFAT")  
		EndIf
		
		nPerFat := 1 - nPerFat
		
		If oPH5:cStatusFin  $ "07;08" .or. ! Empty(Self:RetPH5(nX, "PH5_SLDCAN")) .or. (::nPerRat > Self:RetPH5(nX, "PH5_PERRAT")  .and. ::lExcecao )// nota fiscal cancelada
            oPH5:nPH5VlSld := Self:RetPH5(nX, "PH5_SLDCAN")
            oPH5:nPH5VlFat  := Self:RetPH5(nX, "PH5_VLRFAT") - Self:RetPH5(nX, "PH5_SLDCAN")
			
            nPH5VlSld += Self:RetPH5(nX, "PH5_SLDCAN")
			nPH5VlFat += Self:RetPH5(nX, "PH5_VLRFAT") - Self:RetPH5(nX, "PH5_SLDCAN")
			If Empty(Self:RetPH5(nX, "PH5_VLTOT"))
				nPH5VlrTot += Self:RetPH5(nX, "PH5_VLINCR")  * nPerFat
			Else 
				nPH5VlrTot += Self:RetPH5(nX, "PH5_VLTOT") * nPerFat
			EndIF
			nPH5VlIncF += Self:RetPH5(nX, "PH5_VLINCR")  * nPerFat

			If nPH5VlFat > 0 
				::lFinanceiro := .T.
			EndIf

			nPH5Quant  += (Self:RetPH5(nX, "PH5_QUANT") - Self:RetPH5(NX, "PH5_QTDCAN")) * nPerFat
		Else
			If Empty(oPH5:cNumMed)
				::lFinanceiro := .T.
				oPH5:nPH5VlAFat := Self:RetPH5(nX, "PH5_VLRFAT")
				oPH5:nPH5VlAFaS := Self:RetPH5(nX, "PH5_VLFATS")
				nPH5VlAFat += Self:RetPH5(nX, "PH5_VLRFAT")
				nPH5VlInc  += Self:RetPH5(nX, "PH5_VLINCR")
				nPH5VlBon  += Self:RetPH5(nX, "PH5_VLBONI")
				nPH5VlAFaS += Self:RetPH5(nX, "PH5_VLFATS")
				::cPH5Propos  :=  Self:RetPH5(nX, "PH5_PROPOS")
			Else
				oPH5:nPH5VlFat  := Self:RetPH5(nX, "PH5_VLRFAT")
				oPH5:nPH5VlFatS := Self:RetPH5(nX, "PH5_VLFATS")
				nPH5VlFat  += Self:RetPH5(nX, "PH5_VLRFAT")
				nPH5VlBonF += Self:RetPH5(nX, "PH5_VLBONI")
				nPH5VlIncF += Self:RetPH5(nX, "PH5_VLINCR")
                nPH5VlFatS += Self:RetPH5(nX, "PH5_VLFATS")
				nPH5PerRat += Self:RetPH5(nX, "PH5_PERRAT")
				If Empty(Self:RetPH5(nX, "PH5_VLTOT"))
					nPH5VlrTot += Self:RetPH5(nX, "PH5_VLINCR") 
				Else 
					nPH5VlrTot += Self:RetPH5(nX, "PH5_VLTOT") 
				EndIF
                nPH5Quant  += Self:RetPH5(nX, "PH5_QUANT") - Self:RetPH5(NX, "PH5_QTDCAN")
			EndIf

		EndIf
		
		If oPH5:cStatusFin  == "03"
			nVlrRepre += Self:RetPH5(nX, "PH5_VLRFAT")
		EndIf

		oPH5:nPH5VlTrib := Self:RetPH5(nX, "PH5_VLTRIB")
		If Self:RetPH5(nX, "PH5_VLTOT")  > 0  
			oPH5:nPH5VlrTot := Self:RetPH5(nX, "PH5_VLTOT") 
			If !Empty(oPH5:cNumMed) 
				::nPH5VlTotT  := Self:RetPH5(nX, "PH5_VLTOT") 
			EndIf
		EndIf 
        oPH5:nPH5QtdFat := Self:RetPH5(nX, "PH5_QTDFAT") 
        oPH5:nPH5Quant  := Self:RetPH5(nX, "PH5_QUANT") 
        oPH5:nPH5QtdCan := Self:RetPH5(nX, "PH5_QTDCAN") 
        oPH5:nPH5QtdRea := Self:RetPH5(nX, "PH5_QTDREA") 

		oPH5:nPH5VlCar  := Self:RetPH5(nX, "PH5_VLCARE") 
		oPH5:nPH5VlBon  := nPH5VlBon
		oPH5:nPH5VlCan  := Self:RetPH5(nX, "PH5_VLCANC")
		oPH5:nPH5VlInt  := Self:RetPH5(nX, "PH5_VLINTE")
		oPH5:nPH5IntCan := Self:RetPH5(nX, "PH5_INTCAN")
		oPH5:nPH5VlReat := Self:RetPH5(nX, "PH5_VLREA") 
		oPH5:nPH5VlReaj := Self:RetPH5(nX, "PH5_VLREAJ") 
		oPH5:nPH5VlInc  := Self:RetPH5(nX, "PH5_VLINCR") 
		oPH5:cPH5FlCorp := Self:RetPH5(nX, "PH5_FLCORP") 
		oPH5:nPH5VlNova := Self:RetPH5(nX, "PH5_VLNOVA") 

		oPH5:nPH5BilUp  := Self:RetPH5(nX, "PH5_BUP") 
		oPH5:nPH5BilDown:= Self:RetPH5(nX, "PH5_BDOWN")
		oPH5:cPH5BilId  := Self:RetPH5(nX, "PH5_IDBILL") 
		oPH5:nPH5BilQtd := Self:RetPH5(nX, "PH5_QTDBIL") 
		oPH5:nPH5BilVlr := Self:RetPH5(nX, "PH5_VLRBIL")
		oPH5:nPH5BilVar := Self:RetPH5(nX, "PH5_VARBIL")

		oPH5:nPH5VlTraS := Self:RetPH5(nX, "PH5_VLTRAN") 
		oPH5:nPH5VlTraE := Self:RetPH5(nX, "PH5_VLTRAE")  

		oPH5:nPH5RoyVar := Self:RetPH5(nX, "PH5_VARROY") 
		oPH5:cPH5RoyId  := Self:RetPH5(nX, "PH5_IDAPUR")
		oPH5:nPH5Multa  := Self:RetPH5(nX, "PH5_VLMULT") 
		oPH5:nPH5VlREX  := Self:RetPH5(nX, "PH5_XVLRRE") 
		oPH5:nPH5VlTroca:= Self:RetPH5(nX, "PH5_VLRTRC")
        oPH5:nRecnoPH5  := Self:RetPH5(nX, "PH5_RECNO")
        oPH5:nPH5PerRat := Self:RetPH5(nX, "PH5_PERRAT")

		nPH5VlTrib  += Self:RetPH5(nX, "PH5_VLTRIB")
        nPH5QtdFat  := Self:RetPH5(nX, "PH5_QTDFAT")
        nPH5QtdCan  += Self:RetPH5(nX, "PH5_QTDCAN") 
        nPH5QtdRea  += Self:RetPH5(nX, "PH5_QTDREA") 
		nPH5VlCar   += Self:RetPH5(nX, "PH5_VLCARE") 
		nPH5VlCan   += Self:RetPH5(nX, "PH5_VLCANC")
		nPH5VlInt   += Self:RetPH5(nX, "PH5_VLINTE") 
		nPH5IntCan  += Self:RetPH5(nX, "PH5_INTCAN")  
		nPH5VlReat  += Self:RetPH5(nX, "PH5_VLREA") 
		nPH5VlReaj  += Self:RetPH5(nX, "PH5_VLREAJ")

		nPH5VlNova  += Self:RetPH5(nX, "PH5_VLNOVA") 
		nPH5BilUp   += Self:RetPH5(nX, "PH5_BUP") 
		nPH5BilDown += Self:RetPH5(nX, "PH5_BDOWN") 
		nPH5BilQtd  += Self:RetPH5(nX, "PH5_QTDBIL") 
		nPH5BilVlr  += Self:RetPH5(nX, "PH5_VLRBIL") 
		nPH5BilVar  += Self:RetPH5(nX, "PH5_VARBIL")

		nPH5VlTraS  += Self:RetPH5(nX, "PH5_VLTRAN")
		nPH5VlTraE  += Self:RetPH5(nX, "PH5_VLTRAE")

		nPH5RoyVar  += Self:RetPH5(nX, "PH5_VARROY") 
		cPH5RoyId   := Self:RetPH5(nX, "PH5_IDAPUR") 
		nPH5Multa   += Self:RetPH5(nX, "PH5_VLMULT") 
		nPH5VlREX   += Self:RetPH5(nX, "PH5_XVLRRE") 
        nPH5VlTroca += Self:RetPH5(nX, "PH5_VLRTRC") 
        cPH5NOTASE  := Self:RetPH5(nX, "PH5_NOTASE")
        cPH5CondPg  := Self:RetPH5(nX, "PH5_CONDPG")
        cPH5Situac  := Self:RetPH5(nX, "PH5_SITUAC")
        cPH5StatRM  := Self:RetPH5(nX, "PH5_STATRM")
        
		aadd(::aPH5, oPH5)
	Next
	If !Empty(nPH5VlrTot)
		::nPH5VlrTot := nPH5VlrTot
	EndIf
    ::nPH5VlTrib := nPH5VlTrib
	::nPH5VlAFat := nPH5VlAFat
	::nPH5VlAFaS := nPH5VlAFaS
	::nPH5VlFat  := nPH5VlFat
	::nPH5VlFatS := nPH5VlFatS
    ::nPH5VlSld  := nPH5VlSld
    ::nPH5QtdFat := nPH5QtdFat
    ::nPH5Quant  := nPH5Quant
    ::nPH5QtdCan := nPH5QtdCan
    ::nPH5QtdRea := nPH5QtdRea
	::nPH5VlCar  := nPH5VlCar
	::nPH5VlBon  := nPH5VlBon
	::nPH5VlBonF := nPH5VlBonF
	::nPH5VlCan  := nPH5VlCan
	::nPH5VlInt  := nPH5VlInt
	::nPH5IntCan := nPH5IntCan
	::nPH5VlReat := nPH5VlReat
	::nPH5VlReaj := nPH5VlReaj
	::nPH5VlIncF := nPH5VlIncF
	::nPH5VlInc  := nPH5VlInc
	::nPH5VlNova := nPH5VlNova
	::nVlrRepre  := nVlrRepre

	::nPH5BilUp   := nPH5BilUp
	::nPH5BilDown := nPH5BilDown
	::nPH5BilQtd  := nPH5BilQtd
	::nPH5BilVlr  := nPH5BilVlr
	::nPH5BilVar  := nPH5BilVar

	::nPH5VlTraS  := nPH5VlTraS
	::nPH5VlTraE  := nPH5VlTraE

	::nPH5RoyVar  := nPH5RoyVar
	::cPH5RoyId   := cPH5RoyId
	::nPH5Multa   := nPH5Multa
	::nPH5VlREX   := nPH5VlREX
    ::nPH5VlTroca := nPH5VlTroca
    ::cPH5NOTASE  := cPH5NOTASE
    ::cPH5CondPg  := cPH5CondPg
    ::cPH5Situac  := cPH5Situac
    ::cPH5StatRM  := cPH5StatRM
    ::nPH5PerRat  := nPH5PerRat

Return

Method RetPH5(nLinha, cNameCpo) Class Tgcvxc06
    Local uRet := NIL    
    Local np   := 0
    Local aFinanceiro := ::oCtbCro:aFinanceiro

    np:= Ascan(aFinanceiro[nLinha], {|x| x[1] == cNameCpo})

    uRet := aFinanceiro[nLinha, np, 2]

Return uRet


Method CalcBoni() Class Tgcvxc06
	Local nVlrTot  := (::nVlrUni + ::nRoyVar ) * ::nQuant
	Local nVlrBoni := 0
	Local cTipo    := ""
	Local nDesc    := 0
	Local nPerc    := 0
	Local nx       := 0
	Local lVlrInfo := .F. 

	::nVlrBoni := 0

	For nx := 1 to len(::aCalcBoni)
		cTipo    := ::aCalcBoni[nx, 1]
		nDesc    := ::aCalcBoni[nx, 2]
        nPerc    := ::aCalcBoni[nx, 3]
        lVlrInfo := ::aCalcBoni[nx, 4]
		If cTipo == "P"
            nDesc := nVlrTot * nPerc / 100
        Else
            If lVlrInfo
                nDesc := nDesc / ::nPerRat * 100
			EndIf
        EndIf
		nVlrBoni += nDesc
		//nVlrTot  -= nDesc
		//kibino
	Next
    ::nVlrBoni := nVlrBoni
    ::nVlrUBoni := Round(::nVlrBoni / ::nQuant, 2)

Return

Method Valores() Class Tgcvxc06
	Local cAMFIni   := CmptoAM(::oCtbCro:cCmpFIni)
	Local cSitAnt   := Self:CmpAnt("cSituac")
	Local nVlCalCan	:= 0
	Local cAMCancel	:= ""
	Local ny		:= 0

    Default cSitAnt := ""
	
	
    //::nQtdCan -= ::nQtdTraS
    If ::oCtbCro:lRoyalties //.and. ::cRoySt == "2" // Finalizado
        ::nVlrUni    := ::nRoyVlr / ::nQuant
        ::nVlrTot    := ::nRoyVlr
	EndIf
	
	If ::oCtbCro:lBilling .and. ! Empty(::cBilId)
		
		If Empty(::nBilQtde)
			::nQuant  := 1
		Else
			::nQuant    := ::nBilQtde
		EndIf

		::nQtdFat := ::nQuant  * ::nPerRat / 100 

	EndIf
	

	If ::oCtbCro:lBilling .and. ! Empty(::cBilId)
		::nVlrUni        := ::nBilVlTot / ::nQuant 
		::nBilVaric      -= ::nVlrAju   * ::nQuant 
		::nBilUpDown     := ::nBilVlTot - ::nVlrTot

		//::nBilUpDown     := ::nBilUpDown * ::nPerRat / 100 
		IF ::nBilUpDown > 0
			::nBilUp     := ::nBilUpDown
		Else
			::nBilDown   := ::nBilUpDown * -1
		EndIf
		
	EndIf

	If ::lBonifica
		Self:CalcBoni()
	EndIf
	
	// ======================================
	// aplicando o rateio nas quantidades
	// ======================================
	::nQtdCtr   := ::nQuant 
	::nRQuant   := ::nQuant   * ::nPerRat / 100  
	::nRQtdProg := ::nQtdProg * ::nPerRat / 100 
	::nRQtdCan  := ::nQtdCan  * ::nPerRat / 100  
	::nRQtdReat := ::nQtdReat * ::nPerRat / 100  
	::nRQtdTraE := ::nQtdTraE * ::nPerRat / 100  
	::nRQtdTraS := ::nQtdTraS * ::nPerRat / 100 

    If ::nRQtdCan - ::nRQtdReat > 0
        ::nRQtdCan  := ::nRQtdCan - ::nRQtdReat 
        ::nRQtdReat := 0
    ElseIf ::nRQtdCan - ::nRQtdReat < 0
        ::nRQtdCan  := 0 
        ::nRQtdReat := ::nRQtdReat  - ::nRQtdCan
    ElseIf ::nRQtdCan - ::nRQtdReat == 0
        ::nRQtdCan  := 0 
        ::nRQtdReat := 0
    EndIf

	// ======================================
	// calculando os valores unitarios
	// ======================================
    
    If ::nVlrAju == 0 .and. ::nVlIntera == 0 
		nVlCalCan := ::nVlrUni 
	Else 
		nVlCalCan := ::nVlrVelho
    EndIf
    
	::nVlrProg := Round(nVlCalCan   * ::nRQtdProg , 2)
	::nVlrCanc := Round(nVlCalCan   * ::nRQtdCan , 2)
	::nVlIntCan:= Round(::nVlIntera * (::nRQtdCan + ::nRQtdProg ) , 2)
	::nVlrReat := Round(nVlCalCan   * ::nRQtdReat, 2)
	::nVlrTraE := Round(nVlCalCan   * ::nRQtdTraE, 2) 
	::nVlrTraS := Round(nVlCalCan   * ::nRQtdTraS, 2) * -1

	If ::lCarencia
		::nQtdCar   := ::nRQuant 
		::nVlrCare := Round(::nVlrUni * ::nQtdCar, 2)
	EndIf

	If !::oCtbCro:lBilling .and. Empty(::cBilId)
		::nQtdFat := ::nRQuant - ::nRQtdProg - ::nQtdTroca
	EndIf

    If ! ::lCancelado 
        ::nVlrTot := ::nRQuant * ::nVlrUni        
    EndIf


    If ::oCtbCro:lRoyalties //.and. ::cRoySt == "2" // Finalizado
        ::nQtdFat    := ::nRQuant 
    EndIf

	::nVlTIntera := ::nVlIntera * (::nRQuant + ::nRQtdCan)
	::nVlTIntCan := ::nVlIntera * (::nRQtdCan + ::nRQtdProg )

	// Calcula valor unitario da multa de cancelamento
	::nMultaUni := ::nMultCan / ::nQtdFat

    // Valor Unitario Base sem Rateio
    If ::nPerRat == 100 .AND. ::oCtbCro:nImpostC <> 1
        ::nVlrUniB     := Round(::nVlrUni + ::nRoyVar + ::nMultaUni - ::nVlrUBoni, 2)
    Else
        ::nVlrUniB     := ::nVlrUni + ::nRoyVar + ::nMultaUni - ::nVlrUBoni
    EndIf 

	::nVlrAju   := ::nVlrAju - ::nVlAjuFin
	::nVlrTAju  := ::nVlrAju   * (::nQtdFat + ::nRQtdProg + ::nRQtdCan)
	::nVlTAjFin := ::nVlAjuFin * (::nQtdFat + ::nRQtdProg + ::nRQtdCan)

    ::nVlrUBoni := ::nVlrUBoni * ::nPerRat / 100
    ::nVlrBoni  := ::nVlrBoni * ::nPerRat / 100

	// Valor Unitario Base com Imposto rem Rateio
	::nVlrImp      := ::nVlrUniB / ::oCtbCro:nImpostC - ::nVlrUniB  // caso não tenha imposto a ser acrescido o indice está igual a 1
	::nVlrImpT     := ::nVlrImp * ::nQtdFat
	::nVlrUniI     := ::nVlrUniB + ::nVlrImp

	// Valor Unitario Base com Imposto e Audita
	::nVlrAud      := ::nVlrUniI * ::oCtbCro:nPercAudi / 100     // caso não tenha % audita a porcentagem está com zero
	::nVlrTAud     := ::nVlrAud * ::nQtdFat
	::nVlrUAud     := ::nVlrTAud / ::nQtdFat

	If ::nPerRat == 100 .AND. ::oCtbCro:nImpostC <> 1
		::nVlrUniIA    := Round(::nVlrUniI + ::nVlrUAud, 2)
	Else
		::nVlrUniIA    := ::nVlrUniI + ::nVlrUAud
	EndIf 
	::nVlrTotIA    := ::nVlrUniIA * ::nQtdFat

	::nRDeltaTrc := Round(::nDeltaTrc, 2)
	::nRVlrTrc   := Round(::nVlrTrc  , 2)
	If ::nPerRat > 0 .and. ::nPerRat < 100 //Rateio
		::nRDeltaTrc := Round(::nDeltaTrc * ::nPerRat / 100, 2)
		::nRVlrTrc   := Round(::nVlrTrc   * ::nPerRat / 100, 2)
	EndIf

    // Valor Unitario Base com Imposto, Audita e RE
    If ::oCtbCro:nImpostC <> 1
        ::nVlrREXT     := Round(::nVlrTotIA * ::nPerREX / 100, 2)
    Else
        ::nVlrREXT     := ::nVlrTotIA * ::nPerREX / 100
    EndIf
	::nVlrREX      := ::nVlrREXT / ::nQtdFat
	::nVlrUIARE    := ::nVlrUniIA + ::nVlrREX

	// Valor Total de Faturamento Referencia considerando o rateio
	::nVlrFatRef   := ::nVlrUIARE * ::nQtdFat

	If ::oCtbCro:lBilling .and. Empty(::cBilId)
		::nVlrTAju := 0
	EndIf

	If ::oCtbCro:lCorpora .and. ! ::lIniIncr .and. Empty(::nPH5VlFat)
		::nVlrIncr := 0
	EndIf

	// Situação: Ativo, Cancelado, Gratuito, Manual, Suspenso, Pendente
	If ::lPeriodo  // dentro do peridodo
        If ::cSituac $ "AP" ;
           .OR. (::lCancelado .and. ::cAnoMes == ::oCtbCro:cAnoMesC .and. ! cSitAnt $ "GMS" ) ;
           .OR. (::cAnoMes == ::oCtbCro:cAnoMesO .and. ! cSitAnt $ "GMS" ) 
            ::lGeraCro := .T.
		Else
			::lGeraCro := .F.
		EndIf
		If ::cSituac $ "AP"
			::lCalcFat := .T.
		Else
			::lCalcFat := .F.

			If Len(::oCtbCro:aCancela)  > 0	
				If SELF:CSITUAC $ "GMS"
					::lGeraCro := .F.
					::lCalcFat := .F.
				Else	
					::lGeraCro := .T.
					::lCalcFat := .T.
				EndIf

				If ::nRQtdCan > 0
					For ny := 1 to Len(::oCtbCro:aCancela) 
						cSitAnt := ::oCtbCro:aCancela[ny,9]
						cAMCancel := ::oCtbCro:aCancela[ny,1]
						If cSitAnt $ "GMS"						
							::lGeraCro := .F.
							::lCalcFat := .F.
							Loop
						EndIf
						If ::cAnoMes >= cAMCancel				
							If ::cAnoMes == cAMCancel
								::oCtbCro:cAnoMesC := cAMCancel
								::lGeraCro := .T.
								::lCalcFat := .F.
								::cSituac := "C"
								::cDescSitu  := ::oCtbCro:DescSitu(::cSituac)
								Exit
							Else
								::lGeraCro := .F.
								::lCalcFat := .F.
							EndIf
						EndIf
					Next
				EndIf
			EndIf		
		EndIf
	EndIf
	
    If (::cAnoMes < cAMFIni  .and. ! ::lCarencia) .or. (::oCtbCro:cSituac $ "GMS" .And. ::cSituac == "C")     
		::lGeraCro := .F.
		::lCalcFat := .F.
	EndIf

	If  ::lCalcFat  .and. ! ::lCarencia .and. ! ::lCancelado .and. ::cAnoMes >= ::oCtbCro:cAnoMesTE
		::nVlrFat  := ::nVlrFatRef
		::nVlrFatS := Round(::nVlrFat, 2) - Round(::nVlrImpT, 2) - Round(::nVlrTAud, 2) - Round(::nVlrREXT, 2)
	Else
		::nVlrFat  := 0
		::nVlrImpT := 0
	EndIf

	If ::oCtbCro:cTipRec == "2" .and. ::cAnoMes > Left(Dtos(::oCtbCro:dConIni), 6) // faturamento pontual
		::nVlrUni   := 0
		::nVlrTot   := 0
		::nVlrAju   := 0
		::nVlrTAju  := 0
		::nVlrProg  := 0
		::nVlrCanc  := 0
		::nVlIntCan := 0
		::nVlrReat  := 0
		::nVlrFatRef:= 0
		::nVlrFat   := 0
		::nVlrFatS  := 0
		::nVlrImpT  := 0
		::nVlIntera := 0
		::lGeraCro  := .F.
	EndIf

	
Return

Method NCCValores() Class Tgcvxc06

	Local nvlrUnit	:= 0
	Local nDifFat	:= 0
    
	//::lfinalizado := .F. // ajustado no TGCVXC05.PRW
	If SELF:nPerrat == 100 .or. SELF:nPerrat == 0
		nvlrUnit := Round(self:NVLRUNI, 2)
	Else
		nvlrUnit := self:NVLRUNI
	EndIf

	If ::oCroCmp:nPH5VlFtTS > 0 .AND. ::oCroCmp:nPH5VlFtTS + ::oCroCmp:nvlTotBo + 0.01 >=  SELF:NQTDCTR * nvlrUnit
		::lfinalizado := .T.
	EndIF
	If self:oCtbCro:lCorpora
		If ::oCroCmp:nPH5VlFtTS > 0
			If !::lfinalizado  
				nDifFat := Round(SELF:nRQuant * self:NVLRUNI,2) - ::oCroCmp:nVlFatur
			EndIF
		EndIf
		If nDifFat < 0
			nDifFat := nDifFat * -1
		EndIf
		::cFlCorp := " "
		If nDifFat <= 0.02 .and. !Empty(::oCroCmp:nPH5VlFtTS )
			::lfinalizado := .T.
		Else
			// A partir da segunda sequencia de faturamento com incremento, flega como complementar
			If Len(Self:aPH5) > 0 .And. nDifFat > 0 .And. ::oCroCmp:nSldCanTot == 0
				::cFlCorp := "1"
			EndIf
		EndIf

		If ::oCroCmp:nVlTIncFat <> ROUND(SELF:nVlrInct,2)  	
			If ::oCroCmp:nVlCIncFat <> ROUND(SELF:nVlrIncr,2)                      
				::lfinalizado := .F.
			EndIf
		EndIf
	Else
		If ::oCroCmp:nPH5VlFtTS  > 0 
			If ::oCroCmp:nVlTDevFat == 0 .and. ! ::lExcecao
				::lfinalizado := .T.
			EndIf	
		EndIf
	EndIf

	If !(::lfinalizado)	
		::nPH5VlFat := ::oCroCmp:nvlFatcl
		::nPH5VlBonF := ::oCroCmp:nvlBoncl
		::nPH5VlIncF  := ::oCroCmp:nVlTIncFat
	EndIf
	
Return

Method CorpValores() Class Tgcvxc06

	If ! ::oCtbCro:lCorpora
		Return
	EndIf

	If Empty(::nVlrIncr)
		Return
	EndIf

	::nCorVlrIncr := ::nVlrIncr - ::nPH5VlIncF
	If ::LCARENCIA
		If ::nVlrUni == ::nVlrIncr
			::nVlrCare := ::nCorVlrIncr
		EndIf
	EndIf
	If ! Empty(::nCorVlrIncr)
		::nVlrAju := 0
		::nVlrTAju := 0
	EndIf

	If Empty(::nPH5VlFat) // Não tem valor financeiro forte, o valor fat terá o valor cheio
		Return
	EndIf

	// Calculo valores para faturamento
	::nCorUni   := ::nCorVlrIncr
	::nCorBoni  := ::nVlrBoni - ::nPH5VlBonF
	// Valor Unitario Base sem Rateio
	::nCorUniB  := Round(::nCorUni - ::nCorBoni, 2)

	// Valor Unitario Base com Imposto sem Rateio
	::nCorImp   := ::nCorUniB  / ::oCtbCro:nImpostC - ::nCorUniB
	::nCorImpT  := ::nCorImp * ::nQtdFat
	::nCorUniI  := ::nCorUniB + ::nCorImp

	// Valor Unitario Base com Imposto e Audita
	::nCorAud   := ::nCorUniI * ::oCtbCro:nPercAudi / 100     // caso não tenha % audita a porcentagem está com zero
	::nCorTAud  := ::nCorAud * ::nQtdFat
	::nCorUAud  := ::nCorTAud / ::nQtdFat

	::nCorUniIA    := Round(::nCorUniI + ::nCorUAud, 2)
	::nCorTotIA    := ::nCorUniIA * ::nQtdFat

	// Valor Unitario Base com Imposto, Audita e RE
	::nCorREXT     := Round(::nCorTotIA * ::nPerREX / 100, 2)
	::nCorREX      := ::nCorREXT / ::nQtdFat
	::nCorUIARE    := ::nCorUniIA + ::nCorREX

	// Valor Total de Faturamento Referencia considerando o rateio
	::nCorFatRef   := ::nCorUIARE * ::nQtdFat

	If  ::lCalcFat  .and. ! ::lCarencia .and. ! ::lCancelado .and. ::cAnoMes >= ::oCtbCro:cAnoMesTE
		::nCorFat  := ::nCorFatRef
		::nCorFatS := Round(::nCorFat, 2) - Round(::nCorImpT, 2) - Round(::nCorTAud, 2) - Round(::nCorREXT, 2)
	Else
		::nCorFat := 0
		::nCorImpT:= 0
	EndIf

Return


/*
cStatus:
"Fora do Periodo"
"Calculado"
"Em Aberto"
"Represado"
"Finalizado"
lIntegro :
.T. // Não existem inconsistencia comparando calculo com a base
.F. // Existem inconsistencia na base do cronograma
cAcao:
aErro: // array com a lista de ocorrencias

*/

Method CheckErro() Class Tgcvxc06

    ::cStatus  := ""   //Ex.  "Fora do Periodo"; "Calculado";"Em Aberto";"Represado";"Finalizado";"Não Gera Cronograma"....
    ::cAcao    := ""   // "Gerar Cronograma";"Atualizar Cronograma"; "Excluir Cronograma"; "Notificação"; ""
    ::lIntegro := .T.  // Base de dados integro com as tabelas historicas
    ::aErro    := {}   // Lista de ocorrencias quando não está integro 
    
    If ! Self:VerCro() // Verifica se tem cronograma na competencia, caso tenha cronograma indevido, seta para a exclusão do mesmo quando estiver fraco
        Return
    EndIf
    
    If ! Self:VerNovo()  // Seta a acão para a geração do cronograma
        Return 
    EndIF 
    Self:VerAltera() // Seta a ação para ateração do financeiro atual quando fraco ou a criação da linha complementar

	If ! Self:VerEnd() // Verifica se a competencia está fechada com base nos atributos objeto pai (Tgcvxc05)
        Return
    EndIf 
    
Return 

Method VerCro() Class Tgcvxc06
    Local lRet := .F.
    Local cAMFIni  := CmptoAM(::oCtbCro:cCmpFIni)

    Begin Sequence
        If ! ::lPeriodo  // nao pode ter ph5
            ::cStatus  :="Fora do Periodo"
            Break
        EndIf 

        If ! Empty(::oCtbCro:cAnoMesTE)  .and. ::cAnoMes < ::oCtbCro:cAnoMesTE
            ::cStatus  :="Calc. na Origem"
            Break
        EndIf 

        If ! Empty(::oCtbCro:cAnoMesO)  .and. ::cAnoMes < ::oCtbCro:cAnoMesO
            ::cStatus  :="Calc. na Origem"
            Break
        EndIf 

        If ::oCtbCro:cTipRec == "2" .and. ::cAnoMes > Left(Dtos(::oCtbCro:dConIni), 6)
            ::cStatus  :="Faturamento Pontual"
            Break 
        EndIf

        If ::cAnoMes < cAMFIni  .and. ! ::lCarencia
            ::cStatus  :="Não Gera Cronograma"
            Break 
        EndIf 

        If ::cSituac $ "C" .and. ::cAnoMes > ::oCtbCro:cAnoMesC
            ::cStatus  :="Cancelado"
            Break
        EndIf

        If ::cSituac $ "O" .and. ::cAnoMes > ::oCtbCro:cAnoMesO
            ::cStatus  :="Troca"
            Break
        EndIf

        If ::cAnoMes > ::oCtbCro:CANOMESF .And. !(::cAnoMes == ::oCtbCro:cAnoMesC)
           ::cStatus  :="Item Vencido"
           Break
        EndIf 

        If ! ::lGeraCro
            ::cStatus  :="Não Gera Cronograma"
            Break
	    EndIf
    
        lRet := .T. 
    Recover 
        If ::lFinanceiro
            Aadd(::aErro, "Cronograma Indevido")
            ::cAcao    := "Excluir Cronograma"
            ::lIntegro := .F.
        EndIf
    End Sequence

Return lRet  




Method VerNovo() Class Tgcvxc06
	Local nDifFat := 0

    If ::lFinalizado
	   	Return .t.
	EndIf

	If ::oCtbCro:lCorpora .and. ::nPH5VlIncF <> ::nCorVlrIncr
		Return .t.
	EndIf

	If ::nPH5VlFats > 0
		If Round(SELF:nQtdCtr *  self:NVLRUNI, 2) > ::oCroCmp:nPH5VlFtTS
			nDifFat := Round(SELF:nRQuant *  self:NVLRUNI, 2) - self:nPH5VlFats
		EndIf
		If nDifFat < 0
			nDifFat := nDifFat * -1
		EndIf

		If nDifFat < 0.02
			::cAcao := ""
			::cStatus  :="Cmp Finalizada"
			::lFinalizado := .T.
			Return .F.
		else
			Return .t.
		EndIf
	EndIf

 	If ::lFinanceiro 
        If ::nVlrFat < 0 
            Aadd(::aErro, "Cronograma valor negativo")
            ::cAcao    := "Excluir Cronograma"
            ::lIntegro := .F.
            Return .F.
        EndIf

		If ::nPerRat == 0 
            Aadd(::aErro, "Cliente fora do rateio")
            ::cAcao    := "Excluir Cronograma"
            ::lIntegro := .F.
            Return .F.
        EndIf
    EndIf


    If ::nVlrFat < 0
        ::cAcao    := "Notificação"
        ::cStatus  := "Faturamento Negativo"
        Self:AddErro("Valor fatura negativo, corrigir contrato.")
        aadd(::oCtbCro:aAMAtu, ::cAnoMes)
        Return .f.
    EndIf

    If ::lBonifica .and. ::lCarencia
        ::cAcao    := "Notificação"
        ::cStatus  := "Carencia e Bonficação"
        Self:AddErro("Carencia com bonficação, corrigir contrato.")
        aadd(::oCtbCro:aAMAtu, ::cAnoMes)
        Return .f.
    EndIf
    If ::lBonifica .and. ::lCancelado  .and. Empty(::oCtbCro:cAnoMesTS) .and. ::cAnoMes <= ::oCtbCro:cAnoMesC .and. !::cMotCan == "228"
        ::cAcao    := "Notificação"
        ::cStatus  := "Bonificação e Cancelamento"
        Self:AddErro("Bonificação com Cancelamento, corrigir contrato.")
        aadd(::oCtbCro:aAMAtu, ::cAnoMes)
        Return .f.
    EndIf
    If ::lCarencia .and. ::lCancelado .and. Empty(::oCtbCro:cAnoMesTS) .and. ::cAnoMes <= ::oCtbCro:cAnoMesC .and. !::cMotCan == "228"
        ::cAcao    := "Notificação"
        ::cStatus  := "Carencia e Cancelamento"
        Self:AddErro("Carencia com Cancelamento, corrigir contrato.")
        aadd(::oCtbCro:aAMAtu, ::cAnoMes)
        Return .f.
    EndIf

	
    
    ::cAcao  := "Gerar Cronograma"
    If ::nVlrFat > 0
        If ::cAnoMes < Left(Dtos(Date()), 6)
            ::cStatus  := "Represado"
        ElseIf ::cAnoMes == Left(Dtos(Date()), 6)
            ::cStatus  := "Pendente"
        Else
            ::cStatus  := "Futuro"
        EndIf
    EndIf    

Return .f.


Method VerAltera() Class Tgcvxc06
	
	Local nVlrFatAnt  := Self:CmpAnt("nVlrFat")
	Local nVlrCareAnt := Self:CmpAnt("nVlrCare")
	Local nVlrCancAnt := Self:CmpAnt("nVlrCanc")
	Local nVlrProgAnt := Self:CmpAnt("nVlrProg")
	Local nPH5VlFat   := 0

    // Verificar valor fat
    nPH5VlFat := ::nPH5VlFat 
    If ::nPH5VlIncF < 0
        nPH5VlFat += (::nPH5VlIncF / ::oCtbCro:nImpostC)
    EndIf
    If ! ::oCtbCro:lCorpora
        If nPH5VlFat > 0
            If (nPH5VlFat == Round(::nVlrFat, 2))  .or.  ((::nVlrAju + ::nVlIntera) > 0 .and. nVlrFatAnt <> NIL .and. (nPH5VlFat == Round(nVlrFatAnt, 2)))
                ::cStatus  := "Finalizado"
                ::cAcao    := ""
                Return 
            ElseIf nPH5VlFat > Round(::nVlrFat, 2)
                ::cStatus  := "Finalizado"
                ::cAcao    := ""
                Return 
            EndIf
        EndIf

    Else
        If ! Empty(::nPH5VlFat) .and. ! Empty(::nVlrIncr)
			If  ! (Round(::nCorVlrIncr, 2) == ::nPH5VlInc)
				::cStatus  :="Incremento Divergente"
				If ! ::lFinanceiro
					::cAcao  := "Gerar Cronograma"
				Else
					::cAcao  := "Atualizar Cronograma"
				EndIf
				If Empty(::nCorVlrIncr)
					::cAcao    := "Excluir Cronograma"
                EndIf
                Return 
			Else
                If ::lFinalizado
                    ::cStatus  := "Finalizado"
                    ::cAcao    := ""
                    Return 
				EndIf
			EndIf
		EndIf
    EndIf 

    // Verificar valor a faturar
	If ::nPH5VlAFat > 0
		If AllTrim(::cPH5RoyId) <> ::cRoyId 
			::cAcao  := "Atualizar Cronograma"
		Else
			If (::nPH5VlAFat <> Round(::nVlrFat, 2)) .or. ((::nVlrAju + ::nVlIntera) > 0 .and. nVlrFatAnt <> NIL .and. (::nPH5VlAFat <> Round(nVlrFatAnt, 2)))
				If ::cAnoMes < Left(Dtos(Date()), 6)
					::cStatus  := "Represado"
				ElseIf ::cAnoMes == Left(Dtos(Date()), 6)
					::cStatus  := "Pendente"
				Else
					::cStatus  := "Futuro"
				EndIf
				::cAcao  := "Atualizar Cronograma"
				Return 
			EndIf
		EndIf
    EndIf

    // Verficar valores de carencia
    If ::nPH5VlCar > 0 .or. ::nVlrCare > 0
        If ! (::nPH5VlCar == Round(::nVlrCare, 2)) .and.  ! ((::nVlrAju + ::nVlIntera) > 0 .and. nVlrCareAnt <> NIL .and. (::nPH5VlCar == Round(nVlrCareAnt, 2)))
            ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Carencia Divergente"
            Return 
        EndIf
    EndIf
    
    // Verificar valores de cancelamento
    If (::nPH5VlCan > 0 .or.  ::nVlrCanc > 0) .AND. Empty(::nVlrReat)
        If ! (::nPH5VlCan == Round(::nVlrCanc + ::nVlrProg, 2)) .and.  ! ((::nVlrAju + ::nVlIntera) > 0 .and. nVlrCancAnt <> NIL .and. (::nPH5VlCan == Round(nVlrCancAnt + nVlrProgAnt, 2)))
            ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Cancelamento Divergente"
            Return
        EndIf
    EndIf


	If (::nPH5VlInt > 0 .or. ::nVlTIntera > 0) 
        If ! (::nPH5VlInt == Round(::nVlTIntera, 2)) 
	        ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Cancelamento Divergente"
            Return
        EndIf
    EndIf

	If (::nPH5IntCan > 0 .or. ::nVlTIntCan > 0) 
        If ! (::nPH5IntCan == Round(::nVlTIntCan, 2)) 
	        ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Cancelamento Divergente"
            Return
        EndIf
    EndIf

    // Verificar valores de reativação
    If (::nPH5VlReat > 0 .or.  ::nVlrReat > 0) 
		If ! (::nPH5VlReat == Round(::nVlrReat, 2) )
			::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Reativação Divergente"
            Return
        EndIf
    EndIf

    // Verificar valore de troca
	If ::nPH5VlTroca > 0 .or. ::nRVlrTrc > 0
        If Empty(::nPH5VlTroca) .or. ! (::nPH5VlTroca == Round(::nRVlrTrc, 2))
            ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Troca Divergente"
            Return 
        EndIf
    EndIf

	// Verificar valore de transferencia saida
	If ::nPH5VlTraS > 0 .or. ::nVlrTraS > 0
        If ! (::nPH5VlTraS == Round(::nVlrTraS, 2))
            ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Transferencia Saida Divergente"
            Return 
        EndIf
    EndIf

	// Verificar valore de transferencia entrada
	If ::nPH5VlTraE > 0 .or. ::nVlrTraE > 0
        If ! (::nPH5VlTraE == Round(::nVlrTraE, 2))
            ::cAcao    := "Atualizar Cronograma"
            ::cStatus  := "Transferencia Entrada Divergente"
            Return 
        EndIf
    EndIf


    // verificar valor de imposto
    If ! ::nPH5VlTrib == Round(::nVlrImpT, 2) 
        ::cAcao    := "Atualizar Cronograma"    
        ::cStatus  :="Imposto Divergente"
        Return 
    EndIf

    // verificar valor de imposto
    If ! Empty(::nPH5VlAFat) .and. ! (::nPH5VlaFaS == Round(::nVlrFatS, 2) )
        ::cAcao    := "Atualizar Cronograma"
        ::cStatus  :="Fatura s/ Imp. Divergente"
        Return 
    EndIf

    // Verificar quantidade
    If ::nPH5QtdFat > 0 .and. ::cSituac $ "AP"
        If ::nPH5QtdFat <> Round(::nQtdFat, 4) .and. ::nPerRat == 100
            ::cAcao  := "Atualizar Cronograma"    
            ::cStatus  :="Quantidade Divergente"
            Return
        EndIf
    EndIf

    // Verificar proposta
    If ::nPH5VlAFat > 0 .and. ::cPH5Propos <> ::oCtbCro:cPropos
        ::cAcao    := "Atualizar Cronograma"
        ::cStatus  :="Proposta Divergente"
        Return
	EndIf
	
	If ::NVLRFAT <> ::nPH5VlAFat
		::cAcao    := "Atualizar Cronograma"
        ::cStatus  :="Valor Faturado Divergente"
        Return
	EndIf

    If  ! ::cPH5NotaSE == ::oCtbCro:cNotaSe
        ::cAcao  := "Atualizar Cronograma"
        ::cStatus  :="NotaSe  Divergente"
        Return
    EndIF

    If  ! ::cPH5CondPg == ::oCtbCro:cCondPg
        ::cAcao  := "Atualizar Cronograma"
        ::cStatus  :="Cond Pg  Divergente"
        Return 
    EndIF

	If  ! ::nPH5VlReaj == Round(::nVlrTAju, 2)
        ::cAcao  := "Atualizar Cronograma"
        ::cStatus  :="Valor Reajuste Divergente"
        Return 
    EndIF

    If  ! ::cPH5Situac == ::cSituac 
        ::cAcao  := "Atualizar Cronograma"
        ::cStatus  :="Situação divergente"
        Return  
    EndIF
    
    If  ! ::cPH5StatRM == ::oCtbCro:cStatRM 
        ::cAcao  := "Atualizar Cronograma"
        ::cStatus  :="StatRM divergente"
        Return 
    EndIF
    
    If ::nVlrFat < 0
        ::cAcao    := "Excluir Cronograma"
        ::cStatus  := "Faturamento Negativo"
        Self:AddErro("Valor fatura negativo, corrigir contrato.")
        Return         
    EndIf
    
    If ::nPerRatF > 0 .and. ::nPerRatF < 101 .and. ::nPerRatF > ::nPerRat 
        ::cAcao    := "Notificação"
        ::cStatus  := "% de Rateio divergente"
        Self:AddErro("% Rateio menor que o anterior com medição!")
        Return 
    EndIf

	
    If ::lBonifica .and. ::lCarencia
        ::cAcao    := "Excluir Cronograma"
        ::cStatus  := "Carencia e Bonficação"
        Self:AddErro("Carencia com bonficação, corrigir contrato.")
		Return 
    EndIf

    If ::lBonifica .and. ::lCancelado  .and. Empty(::oCtbCro:cAnoMesTS) .and. ::cAnoMes <= ::oCtbCro:cAnoMesC .and. !::cMotCan == "228"
        ::cAcao    := "Excluir Cronograma"
        ::cStatus  := "Bonificação e Cancelamento"
        Self:AddErro("Bonificação com Cancelamento, corrigir contrato.")
        Return 
    EndIf

    If ::lCarencia .and. ::lCancelado .and. Empty(::oCtbCro:cAnoMesTS) .and. ::cAnoMes <= ::oCtbCro:cAnoMesC .and. !::cMotCan == "228"
        ::cAcao    := "Excluir Cronograma"
        ::cStatus  := "Carencia e Cancelamento"
        Self:AddErro("Carencia com Cancelamento, corrigir contrato.")
        Return 
    EndIf

    ::cAcao := ""
    If ::nVlrFat > 0
        If ::cAnoMes < Left(Dtos(Date()), 6)
            ::cStatus  := "Represado"
        ElseIf ::cAnoMes == Left(Dtos(Date()), 6)
            ::cStatus  := "Pendente"
        Else
            ::cStatus  := "Futuro"
        EndIf
    EndIf    
    //::cAcao  := "Atualizar Cronograma"
Return


Method VerEnd() Class Tgcvxc06
	
	Local nvlrUnit	:= 0
	Local nDifFat	:= 0
	Local cAcaoOld  := ::cAcao 

	If ::cAcao    == "Excluir Cronograma"
		Return
	EndIf
    
	//::lfinalizado := .F. // ajustado no TGCVXC05.PRW
	If SELF:nPerrat == 100 .or. SELF:nPerrat == 0
		nvlrUnit := Round(self:NVLRUNI, 2)
	Else
		nvlrUnit := self:NVLRUNI
	EndIf

	If  (::nPH5VlFat > 0 .and.  ( (::nVlrFat + ::nAtuVlrBon - ::nPH5VlBonF) - ::nPH5VlFat)   <= 0) 
		::cAcao    := ""
		::lfinalizado := .T.
	Endif

	If ::oCroCmp:nPH5VlFtTS > 0 .AND. ::oCroCmp:nPH5VlFtTS + ::oCroCmp:nvlTotBo + 0.01 >=  SELF:NQTDCTR * nvlrUnit
		::lfinalizado := .T.
	EndIF

	If self:oCtbCro:lCorpora
		If ::oCroCmp:nPH5VlFtTS > 0
			If !::lfinalizado  
				nDifFat := Round(SELF:nRQuant * self:NVLRUNI,2) - ::oCroCmp:nVlFatur
			EndIF
		EndIf
		If nDifFat < 0
			nDifFat := nDifFat * -1
		EndIf
		::cFlCorp := " "
		If nDifFat <= 0.02 .and. !Empty(::oCroCmp:nPH5VlFtTS )
			::lfinalizado := .T.
		Else
			// A partir da segunda sequencia de faturamento com incremento, flega como complementar
			If Len(Self:aPH5) > 0 .And. nDifFat > 0 .And. ::oCroCmp:nSldCanTot == 0
				::cFlCorp := "1"
			EndIf
		EndIf

		If ::oCroCmp:nVlTIncFat <> ROUND(SELF:nVlrInct,2)  	
			If ::oCroCmp:nVlCIncFat <> ROUND(SELF:nVlrIncr,2)                      
				::lfinalizado := .F.
				::cAcao       := cAcaoOld 
			EndIf
		EndIf
	Else
		If ::oCroCmp:nPH5VlFtTS  > 0 
			If ::oCroCmp:nVlTDevFat == 0 
				::lfinalizado := .T.
			EndIf	
		EndIf
	EndIf

	If ::lfinalizado	
		::cStatus  := "Finalizado"
		::cAcao    := ""
	EndIf

	If ::cStatPH7 == "10" .and. Empty(::nCorVlrIncr) .and.  ::nPH5VlFat > 0 .AND. Round(::NVLRFAT, 2) == ::nPH5VlFat
        ::cStatus  := "Cmp Finalizada"
		::cAcao    := ""
        Return .f.
    EndIf

Return



Method VlrAtuPH5() Class Tgcvxc06
    Local nVlrUniB  := 0
    Local nVlrImp   := 0 
    Local nVlrImpT  := 0
    Local nVlrUniI  := 0
    Local nVlrAud   := 0
    Local nVlrTAud  := 0
    Local nVlrUAud  := 0
    Local nVlrUniIA := 0
    Local nVlrTotIA := 0
    Local nVlrREXT  := 0
	Local nDifIncT  := 0
	Local nVlrNoRE  := 0
	Local nVlrNoAud := 0
	Local nVlrNoTrb := 0
	Local nAtuBuni  := 0 

	If ! (::cAcao == "Atualizar Cronograma" .or. ::cAcao == "Gerar Cronograma" .or. ::cAcao == "Excluir Cronograma")
		Return
	EndIf

	If ::lCarencia
		If ::nPH5VlFats > 0
			::nVlrCare	:= ::nVlrCare - ::nPH5VlFats
			::nVlrUni	:= ::nVlrCare / ::nRQuant
			::nVlrTot	:= ::nRQuant * ::nVlrUni
			::nVlrVelho := ::nVlrUni
			::nVlrTotV	:= ::nRQuant * ::nVlrUni	
		EndIF
	EndIf

    If Empty(::nRQuant) .and. ! Empty(::nRQtdCan)
        ::nRQuant  := ::nRQtdCan - ::nRQtdReat
		If ::nVlrAju == 0 .and. ::nVlIntera == 0 
			::nVlrTot := ::nRQuant * ::nVlrUni
		Else 
			::nVlrTotV := Round(::nVlrVelho , 2) * ::nRQuant
		EndIf 
    EndIf 
   

	::nAtuQuant   := ::nRQuant - ::nPH5Quant

	If ::oCtbCro:lCorpora
		::nAtuQuant := 1
	EndIf

	::nAtuQtFat   := ::nQtdFat
	::nAtuQtCare  := ::nQtdCar
    ::nAtuQtCan   := ::nRQtdCan 
	::nAtuQtReat  := ::nRQtdReat
    ::nAtuQtCan   += ::nRQtdProg 
	::nAtuQtTra   := ::nRQtdTraS

	If ::nVlrAju == 0 .and. ::nVlIntera == 0 
		::nAtuVlrUni  := ::nVlrUni 
	Else 
		::nAtuVlrUni  := ::nVlrVelho
	EndIf 
	::nAtuVlrTot  := ::nAtuVlrUni * ::nAtuQuant



	::nAtuVlrImp  := ::nVlrImpT
	::nAtuVlrAud  := ::nVlrTAud
	::nAtuVlrREX  := ::nVlrREXT

	::nAtuVlrFat  := Round(::nVlrFat, 2)
	::nAtuVlrFaS := ::nVlrFatS 

	::nAtuVlrUnFa := U_RoundUnid(::nAtuVlrFat / ::nAtuQtFat, 6, ::oCtbCro:cUniNeg) //Round(::nAtuVlrFat / ::nAtuQtFat, 6)
	::nAtuVlrFat  := Round(::nAtuVlrUnFa * ::nAtuQtFat, 2)

    ::nAtuPerRat  := ::nPerRat  
    
    ::nAtuVlrCar  := ::nVlrCare
    ::nAtuVlrBon  := ::nVlrBoni 

    ::nAtuVlrCan  := ::nVlrCanc 
    ::nAtuVlrReat := ::nVlrReat

    ::nAtuVlrCan  += ::nVlrProg 

	::nVlTIntera := ::nAtuQuant * ::nVlIntera
	::nVlTIntCan := ::nAtuQtCan * ::nVlIntera

	
	//If ::nVlrFat - ::oCroCmp:nvlFatcl   > 0 .OR. (::oCtbCro:lCorpora .AND. ::nPH5VlIncF <> ::nCorVlrIncr)

	 	//::oCroCmp:nVlCIncFat <> ROUND(SELF:nVlrIncr,2)     
		//If ::oCroCmp:nvlFatcl < ::nVlrFat 
	If (::nPH5VlFat > 0 .and.  ( (::nVlrFat + ::nAtuVlrBon - ::nPH5VlBonF) - ::nPH5VlFat)   > 0) .OR. (::oCtbCro:lCorpora .AND. ::nPH5VlIncF <> ::nCorVlrIncr)
		If ::oCroCmp:nPH5VlTFat < self:nQtdctr * ( SELF:nAtuVlrUnFa  +  ::nAtuVlrBon - ::nPH5VlBonF )
	
			::nAtuVlrFat  := ::nVlrFat - ::oCroCmp:nvlFatcl 

			If ::nAtuVlrFat < 0
				::nAtuVlrFat := 0
			EndIf
			If ! ::oCtbCro:lCorpora
				::nAtuQtFat   := ::nAtuQtFat  - ::nPH5Quant
				If ::nAtuQtFat == 0 
					::nAtuQtFat := 1
				EndIf
			EndIf
			nVlrNoRE     := self:nAtuVlrFat / (1+(self:nPerREX / 100))
			nVlrREXT     := Round(::nAtuVlrFat - nVlrNoRE, 2)

			nVlrNoAud      := nVlrNoRE /  (1+(self:oCtbCro:nPercAudi / 100)) 
			nVlrAud       := Round(nVlrNoRE - nVlrNoAud, 2)

			nVlrNoTrb      := nVlrNoAud *  ::oCtbCro:nImpostC
			nVlrImp       := Round(nVlrNoTrb - nVlrNoAud, 2)
			
			

		Else
			::nAtuVlrFat := 0
			
		EndIf

        ::nAtuVlrBon  := ::nAtuVlrBon - ::nPH5VlBonF
        If ::nAtuQuant > 0
			nAtuBuni :=  ::nAtuVlrBon / ::nAtuQuant
		Else 
			nAtuBuni :=  ::nAtuVlrBon
		EndIf 

		If ::nPerRat == 100 .AND. ::oCtbCro:nImpostC <> 1
			nVlrUniB     := Round(::nAtuVlrUni - nAtuBuni, 2)
		Else
        	nVlrUniB     := ::nAtuVlrUni   - nAtuBuni
		EndIf
        // Valor Unitario Base com Imposto rem Rateio
        nVlrImp      := nVlrUniB / ::oCtbCro:nImpostC - nVlrUniB  // caso não tenha imposto a ser acrescido o indice está igual a 1
        nVlrImpT     := nVlrImp  * ::nAtuQtFat
        nVlrUniI     := nVlrUniB + nVlrImp
    
        // Valor Unitario Base com Imposto e Audita
        nVlrAud      := nVlrUniI * ::oCtbCro:nPercAudi / 100     // caso não tenha % audita a porcentagem está com zero
        nVlrTAud     := nVlrAud * ::nAtuQtFat
        nVlrUAud     := nVlrTAud / ::nAtuQtFat
    
        nVlrUniIA    := Round(nVlrUniI + nVlrUAud, 2)
        nVlrTotIA    := nVlrUniIA * ::nAtuQtFat
    
        // Valor Unitario Base com Imposto, Audita e RE
        nVlrREXT     := Round(nVlrTotIA * ::nPerREX / 100, 2)
        
               
        ::nAtuVlrUnFa := ::nAtuVlrFat / ::nAtuQtFat
        ::nAtuPerRat  := ::nPerRat - ::nPH5PerRat    
		If Left(::oCtbCro:cModImpC, 1)== "9"
			::nAtuVlrImp := 0
		Else
			::nAtuVlrImp  := ::nAtuVlrFat - (::nAtuVlrFat * ::oCtbCro:nImpostC)
		EndIf
		::nAtuVlrFat  := Round(::nAtuVlrFat, 2)
        ::nAtuVlrUnFa := U_RoundUnid(::nAtuVlrUnFa, 6, ::oCtbCro:cUniNeg) //Round(::nAtuVlrUnFa, 6)
        ::nAtuVlrImp  := nVlrImpT
    	::nAtuVlrAud  := nVlrTAud
	    ::nAtuVlrREX  := nVlrREXT

        ::nAtuVlrFaS  := Round(::nAtuVlrFat, 2) - Round(::nAtuVlrImp, 2) - Round(::nAtuVlrAud, 2) - Round(::nAtuVlrREX, 2)
       
	EndIf


	If ::lCarencia
		::nAtuVlrImp  := 0
		::nAtuVlrTot  := ::nVlrTot
		::nAtuVlrUni  := ::nVlrUni
		::nAtuVlrFat  := 0
		::nAtuVlrFaS  := 0
		::nAtuVlrUnFa := 0
		::nAtuVlrAud  := 0
		::nAtuVlrREX  := 0
	EndIf

	nDifIncT := self:nAtuVlrTot  - self:nVlrIncr
	If nDifIncT < 0
		nDifIncT := -nDifIncT
	EndIf

	If Empty(::nAtuVlrFat) .and. ! Empty(::nVlrIncr) .And. nDifIncT < 0.02 
		::nAtuVlrImp  := 0
		
		//::nAtuVlrUni  := 0
		::nAtuVlrUnFa := 0
		::nAtuVlrFat  := 0
		::nAtuVlrFaS  := 0 
		::nAtuVlrAud  := 0
		::nAtuVlrREX  := 0
	EndIf
	
	If ! Empty(::nVlrIncr)
		::nAtuVlrIncr := ::nCorVlrIncr
		If ::nVlrUni == ::nVlrIncr
			::nAtuVlrUni := ::nCorVlrIncr
			::nAtuVlrTot :=  0
			If ::nCorFat > 0
				::nAtuVlrFat := ::nCorFat
				::nAtuVlrFaS := ::nCorFatS 

				::nAtuVlrImp := ::nCorImpT
				::nAtuVlrAud := ::nCorTAud
				::nAtuVlrUnFa:= ::nCorFat
				If ! Empty(::nCorREX)
					::nAtuVlrREX:= ::nCorREXT
				EndIf
			Else
				::nAtuVlrFat  := 0
				::nAtuVlrFaS  := 0
				::nAtuVlrImp  := 0
				::nAtuVlrUnFa := 0
				::nAtuVlrREX  := 0
			EndIf
		Else

			If ::lIniIncr
				If NoRound(::nAtuVlrIncr,2) < 0  .And. ::nAtuVlrFat == 0 
					::nAtuVlrFaS  := 0
					::nAtuVlrImp  := 0
					::nAtuVlrUni  := 0
					::nAtuVlrTot  := 0
				Endif 

				If NoRound(::nAtuVlrIncr,2) != 0 .And. ::nPH5VlFat > 0 
					If NoRound(::nPH5VlIncF,2) < NoRound(::nAtuVlrIncr,2) 
						::nAtuVlrUni := ::nAtuVlrIncr 
						::nVlrUni    := ::nAtuVlrUni 
						::nAtuVlrTot := ::nAtuQuant * ::nAtuVlrIncr 
						::nAtuPerRat := ::nPerRat
						::nAtuVlrFaS := ::nCorFatS 
						::nAtuVlrImp := ::nCorImpT
						If ! Empty(::nCorREX)
							::nAtuVlrREX:= ::nCorREXT
						EndIf
					EndIf
				Endif 
				
				If NoRound(::nAtuVlrFaS,2) < 0 
					::nAtuVlrFat  := 0 
					::nAtuVlrFaS  := 0
					::nAtuVlrImp  := 0
					::nAtuVlrUni  := 0
					::nAtuVlrTot  := 0
				Endif
			EndIf

			If ::lCarencia
				//::nAtuVlrCar := ::nAtuVlrTot 
			EndIf
		EndIf
	EndIf
	If ! Empty(::nAtuVlrIncr)
		::nVlrAju  := 0
		::nVlrTAju := 0
	EndIf
	::cAtuFlCorp  := ::cFlCorp

	::nAtuBilUp   := ::nBilUp
	::nAtuBilDown := ::nBilDown
	::nAtuBilQtd  := ::nBilQtde
	::nAtuBilVlr  := ::nBilVlTot
	::nAtuBilVar  := ::nBilVaric
	::nAtuRoyVar  := ::nRoyVar
	::cAtuRoyId   := ::cRoyId
	::nAtuVlrTrc  := ::nRVlrTrc

	::nAtuMulta   := ::nMultCan - ::nPH5Multa

	::nAtuVlrUni  := Round(::nAtuVlrUni , 2)
	::nAtuVlrUnFa := U_RoundUnid(::nAtuVlrUnFa, 6, ::oCtbCro:cUniNeg)//Round(::nAtuVlrUnFa, 6)

	If Empty(::nAtuVlrFat)
		::nAtuQtFat := 0
	EndIf
	If ::nAtuPerRat <> 100 .and. ::nAtuVlrFat > 0
		::nAtuQtFat   := 1
		::nAtuVlrUnFa := ::nAtuVlrFat
	EndIf

	aadd(::oCtbCro:aAMAtu, ::cAnoMes)

	If ::cAnoMes == ::oCtbCro:cAnoMesI .and. Empty(::oCtbCro:cAnoMesTE)
		::nVlrVNova  := ::nAtuVlrTot
		::nAtuVlrTot := 0
		::nVlrTot    := 0
	EndIf
Return

Method Free() Class Tgcvxc06
	Local nx

	For nx:= 1 to len(::aPH5)
		FreeObj(::aPH5[nx])
	Next
	aSize(::aPH5, 0)

	If ::nPerRat > 0
		::oCroCmp:Destroy()
		FreeObj(::oCroCmp)
	EndIf
	
	aSize(::aCalcBoni, 0)
	aSize(::aErro    , 0)

	::aPH5      := nil
	::aCalcBoni := nil
	::aErro     := nil
Return

Method ZeraRecalc() Class Tgcvxc06
	Local nx

	For nx:= 1 to len(::aPH5)
		FreeObj(::aPH5[nx])
	Next
	aSize(::aPH5, 0)

	aSize(::aCalcBoni, 0)
	aSize(::aErro    , 0)

	::aPH5      := {}
	::aCalcBoni := {}
	::aErro     := {}

	Self:LimpaRecalc()
Return

Method LimpaRecalc() Class Tgcvxc06
	::nVlrCare    := 0 
	::nQtdCar     := 0 
	::nRDeltaTrc  := 0 
	::nRVlrTrc    := 0 
	::nVlrFat     := 0 
	::nVlrFatS    := 0  
	::nPH5VlrTot  := 0
	::nPH5VlTotT  := 0
	::nPH5VlTrib  := 0
	::nPH5VlAFat  := 0
	::nPH5VlAFaS  := 0
	::nPH5VlFat   := 0
	::nPH5VlFatS  := 0
    ::nPH5VlSld   := 0
    ::nPH5QtdFat  := 0
    ::nPH5Quant   := 0
    ::nPH5QtdCan  := 0
    ::nPH5QtdRea  := 0
	::nPH5VlCar   := 0
	::nPH5VlBon   := 0
	::nPH5VlBonF  := 0
	::nPH5VlCan   := 0
	::nPH5VLInt   := 0
	::nPH5IntCan  := 0 
	::nPH5VlReat  := 0  
	::nPH5VlReaj  := 0  
	::nPH5VlInc   := 0
	::nPH5VlIncF  := 0
	::nPH5VlNova  := 0
	::nPH5BilUp   := 0
	::nPH5BilDown := 0
	::nPH5BilQtd  := 0
	::nPH5BilVlr  := 0
	::nPH5BilVar  := 0
	::nPH5VlTraS  := 0
	::nPH5VlTraE  := 0
	::nPH5RoyVlr  := 0
	::nPH5RoyVar  := 0 
	::nPH5VlREX   := 0  
	::nPH5VlTroca := 0    
	::nPH5Multa   := 0     
    ::nPH5PerRat  := 0  
	::nVlrRepre   := 0    
	::nAtuQuant   := 0     
	::nAtuQtFat   := 0     
	::nAtuQtCare  := 0    
	::nAtuQtCan   := 0     
	::nAtuQtReat  := 0    
	::nAtuQtTra   := 0    
	::nAtuVlrUni  := 0 
	::nAtuVlrTot  := 0  
	::nAtuVlrImp  := 0  
	::nAtuVlrUnFa := 0
	::nAtuVlrFat  := 0 
	::nAtuVlrFaS  := 0  
	::nAtuVlrCar  := 0  
	::nAtuVlrBon  := 0  
	::nAtuVlrCan  := 0  
	::nAtuVlrReat := 0
	::nAtuVlrIncr := 0
	::nAtuVlrAud  := 0  
	::nAtuVlrRE   := 0 
	::nAtuBilUp   := 0 
	::nAtuBilDown := 0
	::nAtuBilQtd  := 0 
	::nAtuBilVlr  := 0
	::nAtuBilVar  := 0
	::nAtuRoyVar  := 0
	::nAtuVlrTrc  := 0
    ::nAtuMulta   := 0   
    ::nAtuPerRat  := 0    
	::nAtuVlrREX  := 0  
	::nVlrBoni    := 0
	::nVlrUBoni   := 0   
	::cAtuFlCorp  := ""
	::cPH5RoyId   := "" 
	::cPH5FlCorp  := "" 
	::cPH5Propos  := ""  
    ::cPH5NOTASE  := ""  
	::cPH5CondPg  := "" 
	::cPH5Situac  := ""  
    ::cPH5StatRM  := "" 
	::cAtuRoyId   := ""
	::lBonifica   := .F.          
Return


Method Formulas() Class Tgcvxc06
	Local cHtml := ""
	Local aFormula := Self:RetaFormula()
	Local nx

	cHtml += "<font size=3 face=calibri>  " + CRLF
	cHtml += "   <table width=100% border=0> " + CRLF

	If ::oCtbCro:cTipRec == "1"
		cHtml += "      <tr><td>Contrato<b> " + ::oCtbCro:cContrato + "</b> Revisão <b> " + ::oCtbCro:cRevisa +" </b>Planilha<b> " + ::oCtbCro:cPlanilha + "</b> Item<b> "+ ::oCtbCro:cItem + "</b></td></tr>" + CRLF
	Else
		cHtml += "      <tr><td>Contrato<b> " + ::oCtbCro:cContrato + "</b> Revisão <b> " + ::oCtbCro:cRevisa +" </b>Planilha<b> " + ::oCtbCro:cPlanilha + "</b> Item<b> "+ ::oCtbCro:cItem + "</b> Tipo<b> PONTUAL</b></td></tr>" + CRLF
	EndIf
	If ::oCtbCro:lSetorPub
		cHtml += "     <tr><td>Cliente<b> " + ::oCtbCro:cCliente + "</b> Loja <b> " + ::oCtbCro:cLoja + " - " + ::oCtbCro:cDescCli + " </b> <u>SETOR BUBLICO</u></td></tr>" + CRLF
	Else
		cHtml += "     <tr><td>Cliente<b> " + ::oCtbCro:cCliente + "</b> Loja <b> " + ::oCtbCro:cLoja + " - " + ::oCtbCro:cDescCli + " </b> </td></tr>" + CRLF
	EndIf
	cHtml += "      <tr><td>Produto<b> "              + ::oCtbCro:cProduto  + " - " + ::oCtbCro:cDescPro +"</b></td></tr>" + CRLF
	cHtml += "      <tr><td>Situação <b> "            + ::oCtbCro:cDescSitu + " </td></tr>" + CRLF
	cHtml += "      <tr><td>Periodicidade<b> "        + ::oCtbCro:cPeriodico                   + " </td></tr>" + CRLF
	cHtml += "   </table>" + CRLF
	cHtml += "<br>" + CRLF
	cHtml += "<font size=3 face=calibri>  " + CRLF
	cHtml += "   <table border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
	cHtml += "      <tr>"
	cHtml += "      <td width = 200><b>Descrição </b></td>"
	cHtml += "      <td width = 100><b>Variavel</b></td>"
	cHtml += "      <td><b>Expressão</b></td>"
	cHtml += "      <td width = 100><b>Resultado</b></td>"
	cHtml += "      <td><b>Formula</b></td>"
	cHtml += "      </tr>" + CRLF
	For nx := 1 to len(aFormula)
		If len(aFormula[nx]) == 1 // Titulo
			cHtml += "      <tr>"
			cHtml += "      <td><b><u><font COLOR='RED'>" + aFormula[nx, 1] + "</font></u></b></td>" + CRLF
			cHtml += "      </tr>" + CRLF
		Else 
			cHtml += "      <tr>"
			cHtml += "      <td>" + aFormula[nx, 1] + "</td>"
			cHtml += "      <td>" + aFormula[nx, 2] + "</td>"
			cHtml += "      <td>" + aFormula[nx, 4] + "</td>"
			cHtml += "      <td>" + aFormula[nx, 3] + "</td>"
			cHtml += "      <td>" + aFormula[nx, 5] + "</td>"
			cHtml += "      </tr>" + CRLF
		EndIf
	Next
	cHtml += "   </table>" + CRLF
Return cHtml


Method  RetaFormula() Class Tgcvxc06
	Local aFormula := {}
	aadd(aFormula, {"Memoria de Calculo"      })
	aadd(aFormula, {"Competencia"              , "cMesAno"   , Texto({::cMesAno  })  , Texto({::cMesAno})                                                                 , " Competencia" })
	aadd(aFormula, {"Percentual de rateio"     , "nPerRat"   , Texto({::nPerRat  })  , Texto({::nPerRat})                                                                 , " % conforme reteio" })
	aadd(aFormula, {"Quantidade Contrato  "    , "nQtdRev"   , Texto({::nQtdRev  })  , Texto({::nQtdRev})                                                                 , " Quantidade apurada conforme revisão" })
	aadd(aFormula, {"Quantidade Base"          , "nQuant"    , Texto({::nQuant  })   , Texto({::nQuant  , "*", ::nPerRat, " / 100" })                                     , " nQuant   * nPerRat / 100" })
	aadd(aFormula, {"Valor Unitario"           , "nVlrUni"   , Texto({::nVlrUni  })  , Texto({::nVlrUni })                                                                , " Valor unitario conforme revsião" })
	aadd(aFormula, {"Valor Total Bruto"        , "nVlrTot"   , Texto({::nVlrTot  })  , Texto({::nVlrUni, "*", ::nQuant })                                                 , " nVlrUni * nQuant" })
	If ! Empty(::nVlrProg) .or. ! Empty(::nVlrCanc) .or. ! Empty(::nVlrReat)
		aadd(aFormula, {"Cancelamentos / Reativações"      })
		aadd(aFormula, {"Quantidade Programada"    , "nQtdProg"  , Texto({::nQtdProg })  , Texto({::nQtdProg, "*", ::nPerRat, " / 100" })                                     , " nQtdProg * nPerRat / 100" })
		aadd(aFormula, {"Valor Programado"         , "nVlrProg"  , Texto({::nVlrProg })  , Texto({::nVlrUni, "*", ::nQtdProg })                                               , " nVlrUni * nQtdProg " })
		aadd(aFormula, {"Quantidade Cancelada "    , "nQtdCan"   , Texto({::nQtdCan  })  , Texto({::nQtdCan , "*", ::nPerRat, " / 100" })                                     , " nQtdCan  * nPerRat / 100" })
		aadd(aFormula, {"Valor Cancelado"          , "nVlrCanc"  , Texto({::nVlrCanc })  , Texto({::nVlrUni, "*", ::nQtdCan  })                                               , " nVlrUni * nQtdCan "  })
		aadd(aFormula, {"Quantidade Reativada "    , "nQtdReat"  , Texto({::nQtdReat })  , Texto({::nQtdReat, "*", ::nPerRat, " / 100" })                                     , " nQtdReat * nPerRat / 100" })
		aadd(aFormula, {"Valor Reativado"          , "nVlrReat"  , Texto({::nVlrReat })  , Texto({::nVlrUni, "*", ::nQtdReat })                                               , " nVlrUni * nQtdReat " })
	EndIf
	If ! Empty(::nVlrTraE) .or. ! Empty(::nVlrTraS )
		aadd(aFormula, {"Transferencia"      })
		aadd(aFormula, {"Quantidade Tranf. Entrada", "nQtdTraE"  , Texto({::nQtdTraE })  , Texto({::nQtdTraE, "*", ::nPerRat, " / 100" })                                     , " nQtdTraE * nPerRat / 100" })
		aadd(aFormula, {"Valor Tranf. Entrada"     , "nVlrTraE"  , Texto({::nVlrTraE })  , Texto({::nVlrUni, "*", ::nQtdTraE })                                               , " nVlrUni * nQtdTraE " })
		aadd(aFormula, {"Quantidade Tranf. Saida"  , "nQtdTraS"  , Texto({::nQtdTraS })  , Texto({::nQtdTraS, "*", ::nPerRat, " / 100" })                                     , " nQtdTraS * nPerRat / 100" })
		aadd(aFormula, {"Valor Tranf. Saida"       , "nVlrTraS"  , Texto({::nVlrTraS })  , Texto({::nVlrUni, "*", ::nQtdTraE })                                               , " nVlrUni * nQtdTraS " })
	EndIf	
	If ! Empty(::nVlrAju)
		aadd(aFormula, {"Reajuste"      })
		aadd(aFormula, {"Valor Unitario Velho"     , "nVlrVelho" , Texto({::nVlrVelho }) , Texto({::nVlrVelho})                                                               , " Valor Unitario Velho" })
		aadd(aFormula, {"Valor Total Velho"        , "nVlrTotV"  , Texto({::nVlrTotV })  , Texto({::nVlrTotV})                                                                , " Valor Total Velho Velho" })
		aadd(aFormula, {"Valor Reajuste Unitario"  , "nVlrAju"   , Texto({::nVlrAju })   , Texto({::nVlrAju})                                                                 , " Valor reajuste CXL" })
		aadd(aFormula, {"Valor Reajuste Total"     , "nVlrTAju"  , Texto({::nVlrTAju })  , Texto({::nVlrAju, "*", ::nQuant })                                                 , " nVlrUni * nQuant " })
	EndIf 

	If ! Empty(::nVlAjuFin)
		aadd(aFormula, {"Ajuste Financeiro"      })
		aadd(aFormula, {"Valor Unitario Velho"     , "nVlrVelho" , Texto({::nVlrVelho }) , Texto({::nVlrVelho})                                                               , " Valor Unitario Velho" })
		aadd(aFormula, {"Valor Total Velho"        , "nVlrTotV"  , Texto({::nVlrTotV })  , Texto({::nVlrTotV})                                                                , " Valor Total Velho" })
		aadd(aFormula, {"Valor Ajuste Unitario"    , "nVlrAju"   , Texto({::nVlAjuFin }) , Texto({::nVlAjuFin})                                                                 , " Valor reajuste CXL" })
		aadd(aFormula, {"Valor Ajuste Total"       , "nVlTAjFin" , Texto({::nVlTAjFin }) , Texto({::nVlAjuFin, "*", ::nQuant })                      							, " nVlrUni * nQuant " })
	EndIf 

	If ! Empty(::nVlIntera)
		aadd(aFormula, {"Intera"      })
		aadd(aFormula, {"Valor Unitario Intera"   , "nVlIntera" , Texto({::nVlIntera }) , Texto({::nVlIntera})                                                               , " Valor Unitario Intera" })
		aadd(aFormula, {"Valor Total Intera"      , "nVlIntera * nQuant"  , Texto({::nVlIntera * ::nQuant })  , Texto({::nVlIntera * ::nQuant})                              , " Valor Total Intera" })

		If ! Empty(::nQtdCan + ::nQtdProg)
			aadd(aFormula, {"Valor Total Cancelado Intera" , "nVlIntera * (nQtdCan + nQtdProg)", Texto({::nVlIntera * (::nQtdCan + ::nQtdProg)}) , Texto({::nVlIntera * (::nQtdCan + ::nQtdProg)})                                                                , " Valor Total Intera" })
		EndIf

		aadd(aFormula, {"Valor Faturar Intera"      , "nVlIntera * (nQuant - (nQtdCan + nQtdProg))"  , Texto({::nVlIntera * (::nQuant - (::nQtdCan + ::nQtdProg)) })  , Texto({::nVlIntera * (::nQuant - (::nQtdCan + ::nQtdProg)) })                              , " Valor Total Intera" })
	EndIf


	If ::lCarencia
		aadd(aFormula, {"Carencia"      })
		aadd(aFormula, {"Carencia"                 , "lCarencia" , Texto({::lCarencia})  , Texto({::lCarencia })                                                              , " Apurado conforme (PH4) " })
		aadd(aFormula, {"Quantidade Carencia"      , "nQtdCar"   , Texto({::nQtdCar  })  , Texto({"SE(lCarencia = Sim ,", ::nQuant, ", 0)" })                                 , " Se(lCarencia=Sim, nQuant, 0) " })
		aadd(aFormula, {"Valor Carencia"           , "nVlrCare"  , Texto({::nVlrCare })  , Texto({::nVlrUni, "*", ::nQtdCar  })                                               , " nVlrUni * nQtdCar " })
	EndIf
	If ! Empty(::nVlrBoni)
		aadd(aFormula, {"Bonificação"      })
		aadd(aFormula, {"Valor Total Bonificação"  , "nVlrBoni"  , Texto({::nVlrBoni })  , Texto({::nVlrBoni})                                                                , " Valor apurado (PH4)" })
		aadd(aFormula, {"Valor Unit. Bonificação"  , "nVlrUBoni" , Texto({::nVlrUBoni})  , Texto({::nVlrBoni, "/", ::nQuant  })                                               , " nVlrBoni / nQuant" })
	EndIf		
	If ! Empty(::nMultCan)
		aadd(aFormula, {"Multa"})
		aadd(aFormula, {"Multa Total Cancelamento" , "nMultCan"  , Texto({::nMultCan })  , Texto({::nMultCan})                                                                , " Multa de cancelamento apurada na (PH3)" })
		aadd(aFormula, {"Multa Unit  Cancelamento" , "nMultaUni" , Texto({::nMultaUni})  , Texto({::nMultCan, "/", ::nQtdFat })                                               , " nMultCan / nQtdFat" })
	EndIf 
	If ! Empty(::nRoyVar)
        aadd(aFormula, {"Royalties"})
        aadd(aFormula, {"Valor Royalties"          , "nRoyVlr"   , Texto({::nRoyVlr  })  , Texto({::nRoyVlr})                                                                 , " Valor de Royalties unitario apurado (PNG)" })
		aadd(aFormula, {"Delta Royalties"          , "nRoyVar"   , Texto({::nRoyVar  })  , Texto({::nRoyVar})                                                                 , " Delta de Royalties unitario apurado (PNG)" })
	EndIf	
	aadd(aFormula, {"Base p/ faturamento"})
	aadd(aFormula, {"Quantidade Base Fat"      , "nQtdFat"   , Texto({::nQtdFat  })  , Texto({::nQuant, "-", ::nQtdProg })                                                , " nQuant - nQtdProg " })
	aadd(aFormula, {"Valor Unit. Base "        , "nVlrUniB"  , Texto({::nVlrUniB })  , Texto({"Arred(" ,::nVlrUni,"+", ::nRoyVar,"+", ::nMultaUni,"-",::nVlrUBoni,", 2)"}), " Arred(nVlrUni + nRoyVar + nMultaUni - nVlrUBoni, 2)" })
	aadd(aFormula, {"Imposto"})
	aadd(aFormula, {"Indice de Imposto"        , "nImpostC"  , Texto({::oCtbCro:nImpostC }) , Texto({::oCtbCro:nImpostC})                                                 , " Indice do modelo de tributação " })
	aadd(aFormula, {"Valor Imposto Unitario"   , "nVlrImp"   , Texto({::nVlrImp  })  , Texto({::nVlrUniB, "/", ::oCtbCro:nImpostC, " - ", ::nVlrUniB})                    , " (nVlrUniB / nImpostC) - nVlrUniB" })
	aadd(aFormula, {"Valor Imposto Total"      , "nVlrImpT"  , Texto({::nVlrImpT})   , Texto({::nVlrImp , "*", ::nQtdFat})                                                , " nVlrImp * nQtdFat " })
	aadd(aFormula, {"Valor Unit. c\ Imposto"   , "nVlrUniI"  , Texto({::nVlrUniI })  , Texto({::nVlrUniB, "+", ::nVlrImp})                                                , " nVlrUniB + nVlrImp " })
	aadd(aFormula, {"Audita"})
	aadd(aFormula, {"% Audita"                 , "nPercAudi" , Texto({::oCtbCro:nPercAudi }) , Texto({::oCtbCro:nPercAudi})                                               , " % Audita aplicavel no produto " })
	aadd(aFormula, {"Valor Audita Base"        , "nVlrAud"   , Texto({::nVlrAud  })  , Texto({::nVlrUniI, "*", ::oCtbCro:nPercAudi ,"/ 100"})                             , " nVlrUniI * nPercAudi / 100" })
	aadd(aFormula, {"Valor Total Audita"       , "nVlrTAud"  , Texto({::nVlrTAud })  , Texto({::nVlrAud, "*", ::nQtdFat   })                                              , " nVlrAud * nQtdFat" })
	aadd(aFormula, {"Valor Unit. Audita"       , "nVlrUAud"  , Texto({::nVlrUAud})   , Texto({::nVlrTAud, "/", ::nQtdFat  })                                              , " nVlrTAud / nQtdFat" })
	aadd(aFormula, {"Valor Unit. Imp. e Aud."  , "nVlrUniIA" , Texto({::nVlrUniIA})  , Texto({"Arred(" ,::nVlrUniI, "+", ::nVlrUAud,", 2)" })                             , " Arred(nVlrUniI + nVlrUAud, 2)" })
	aadd(aFormula, {"Valor Total Imp. e Aud."  , "nVlrTotIA" , Texto({::nVlrTotIA})  , Texto({::nVlrUniIA, "*", ::nQtdFat })                                              , " nVlrTotIA * nQtdFat" })
	
	aadd(aFormula, {"Reajuste Extraordinario"})
	aadd(aFormula, {"% Reajuste Extraordinario", "nPerRex"   , Texto({::nPerRex })   , Texto({::nPerRex})                                                                 , " % apurada de ajuste extraordinario" })
	aadd(aFormula, {"Valor Total RE"           , "nVlrREXT"  , Texto({::nVlrREXT })  , Texto({"Arred(" ,::nVlrTotIA, "*", ::nPerREX, "/100, 2)"})                         , " Arred(nVlrTotIA * nPerREX / 100, 2)" })
	aadd(aFormula, {"Valor Unitario RE"        , "nVlrRex"   , Texto({::nVlrRex })   , Texto({::nVlrREXT, "/", ::nQtdFat })                                               , " nVlrREXT / nQtdFat" })
	aadd(aFormula, {"Valor Unit. Imp/Aud/RE"   , "nVlrUIARE" , Texto({::nVlrUIARE})  , Texto({::nVlrUniIA, "+", ::nVlrRex})                                               , " nVlrUniIA + nVlrRex" })
	aadd(aFormula, {"Faturamento"})
	aadd(aFormula, {"Valor Fat Referencia"     , "nVlrFatRef", Texto({::nVlrFatRef}) , Texto({::nVlrUIARE, "*", ::nQtdFat})                                               , " nVlrUIARE * nQtdFat" })
	aadd(aFormula, {"Valor Fat "               , "nVlrFat"   , Texto({::nVlrFat})    , Texto({::nVlrFat})                                                                 , " nVlrFat = nVlrFatRef, nVlrFat pode ter valor 0 por Carencia" })
	aadd(aFormula, {"Valor Fat s/ Imposto"     , "nVlrFatS"  , Texto({::nVlrFatS})   , Texto({::nVlrFat, "-", ::nVlrImpT, "-", ::nVlrTAud, "-", ::nVlrREXT})              , " nVlrFat - nVlrImpT - nVlrTAud - nVlrREXT" })

	If ::oCtbCro:lCorpora .and. ! Empty(::nVlrIncr) .and. ! Empty(::nPH5VlFat) // Não tem valor financeiro forte, o valor fat terá o valor cheio
		aadd(aFormula, {"Corporativo"})
		aadd(aFormula, {"Valor Incremento "           , "nVlrIncr"  , Texto({::nVlrIncr })  , Texto({::nVlrIncr })                                                            , " Incremento apurado" })
		aadd(aFormula, {"Valor Corp Incremento Fat "  , "nPH5VlIncF", Texto({::nPH5VlIncF}) , Texto({::nPH5VlIncF })                                                          , " Incremento utilizando na 1 vez" })
		aadd(aFormula, {"Valor Corp Incremento "      , "nCorUni"   , Texto({::nCorUni })   , Texto({::nVlrIncr, "-", ::nPH5VlIncF})                                          , " nVlrIncr - nPH5VlIncF" })
		aadd(aFormula, {"Valor Corp Bonificação Fat " , "nPH5VlBonF", Texto({::nPH5VlBonF}) , Texto({::nPH5VlBonF })                                                          , " Bonificação utilizada Anteriormente" })
		aadd(aFormula, {"Valor Corp Bonificação "     , "nCorBoni"  , Texto({::nCorBoni })  , Texto({::nVlrBoni, "-", ::nPH5VlBonF})                                          , " nVlrBoni - nPH5VlBon" })
		aadd(aFormula, {"Valor Corp Unit. Base "      , "nCorUniB"  , Texto({::nCorUniB })  , Texto({"Arred(" ,::nCorUni,"-",::nCorBoni, ", 2)"})                             , " Arred(nCorUni - nCorBoni, 2)" })
		aadd(aFormula, {"Valor Corp Imposto Unitario" , "nCorImp"   , Texto({::nCorImp  })  , Texto({::nCorUniB, "/", ::oCtbCro:nImpostC, " - ", ::nCorUniB})                 , " (nCorUniB / nImpostC) - nCorUniB" })
		aadd(aFormula, {"Valor Corp Imposto Total"    , "nCorImpT"  , Texto({::nCorImpT})   , Texto({::nCorImp, "*", ::nQtdFat})                                              , " nCorImp * nQtdFat" })
		aadd(aFormula, {"Valor Corp Unit. c\ Imposto" , "nCorUniI"  , Texto({::nCorUniI })  , Texto({::nCorUniB, "+", ::nCorImp})                                             , " nCorUniB + nCorImp" })
		aadd(aFormula, {"Valor Corp Audita Base"      , "nCorAud"   , Texto({::nCorAud  })  , Texto({::nCorUniI, "*", ::oCtbCro:nPercAudi ,"/ 100"})                          , " nCorUniI * nPercAudi / 100" })
		aadd(aFormula, {"Valor Corp Total Audita"     , "nCorTAud"  , Texto({::nCorTAud })  , Texto({::nCorAud , "*", ::nQtdFat})                                             , " nCorAud * nQtdFat" })
		aadd(aFormula, {"Valor Corp Unit. Audita"     , "nCorUAud"  , Texto({::nCorUAud})   , Texto({::nCorTAud, "/", ::nQtdFat})                                             , " nCorTAud / nQtdFat" })
		aadd(aFormula, {"Valor Corp Unit. Imp. e Aud.", "nCorUniIA" , Texto({::nCorUniIA})  , Texto({"Arred(" ,::nCorUniI, "+", ::nCorUAud, ", 2)"})                          , " Arred(nCorUniI + nCorUAud, 2)" })
		aadd(aFormula, {"Valor Corp Total Imp. e Aud.", "nCorTotIA" , Texto({::nCorTotIA})  , Texto({::nCorUniIA, "*", ::nQtdFat})                                            , " nCorUniIA * nQtdFat" })
		aadd(aFormula, {"Valor Corp Total RE"         , "nCorREXT"  , Texto({::nCorREXT })  , Texto({"Arred(" ,::nCorTotIA, "*", ::nPerREX, "/100, 2)"})                      , " Arred(nCorTotIA * nPerREX / 100, 2)" })
		aadd(aFormula, {"Valor Corp Unitario RE"      , "nCorREX"   , Texto({::nCorREX })   , Texto({::nCorREX, "/", ::nQtdFat })                                             , " nCorREX / nQtdFat" })
		aadd(aFormula, {"Valor Corp Unit. Imp/Aud/RE" , "nCorUIARE" , Texto({::nCorUIARE})  , Texto({::nCorUniIA, "+", ::nCorREX})                                            , " nCorUniIA + nVlrRex" })
		aadd(aFormula, {"Valor Corp Fat Ref."         , "nCorFatRef", Texto({::nCorFatRef}) , Texto({::nCorUIARE, "*", ::nQtdFat})                                            , " nCorUIARE * nQtdFat" })
		aadd(aFormula, {"Valor Corp Fat "             , "nCorFat"   , Texto({::nCorFat})    , Texto({::nCorFat})                                                              , " nCorFat = nCorFatRef, nCorFat pode ter valor 0 por Carencia" })
		aadd(aFormula, {"Valor Corp Fat s/ Imposto"   , "nCorFatS"  , Texto({::nCorFatS})   , Texto({::nCorFat, "-", ::nCorImpT, "-", ::nCorTAud, "-", ::nCorFat})            , " nCorFat - nCorImpT - nCorTAud - nCorFat" })
	EndIf

Return aFormula

Method AddErro(cMsg)  Class Tgcvxc06
    Local np 

    np:= Ascan(::oCtbCro:aMsgErro, {|x| x[1] == ::cAnoMes})
    If Empty(np)
        aadd(::oCtbCro:aMsgErro, {::cAnoMes, cMsg + CRLF})
    Else 
        ::oCtbCro:aMsgErro[np, 2] += cMsg + CRLF  
    EndIf

Return

Static Function Texto(aPar)
	Local nx   := 0
	Local uVar := NIL
	Local cRet := ""

	For nx := 1 to len(aPar)
		uVar := aPar[nx]
		cTipo := Valtype(uVar)

		If cTipo == "C"
			cRet += " " + uVar + " "
		ElseIf cTipo == "N"
			cRet += " " + Alltrim(Str(uVar)) + " "
		ElseIf cTipo == "L"
			IF uVar
				cRet += " SIM "
			Else
				cRet += " NÃO "
			EndIF
		EndIf
	Next

Return cRet


Method CmpAnt(cAtrib) Class Tgcvxc06
	Local oObj
	Local uValor
	If ::nMes == 1  // para o primeiro mes não existe o anterior
		Return NIL
	EndIf

	oObj := ::oCtbCro:aoCroCmp[::nMes - 1]
	uValor := &("oObj:" + cAtrib)

Return uValor

Method Salva() Class Tgcvxc06

	If ! ::lGeraCro .and. ! ::cAcao == "Excluir Cronograma"
		Return
	EndIf

	If ! (::cAcao == "Atualizar Cronograma" .or. ::cAcao == "Gerar Cronograma" .or. ::cAcao == "Excluir Cronograma")
		Return
	EndIf
    
    //

    Begin Transaction
    
        Self:DelPH5()

		If ::cAcao == "Atualizar Cronograma" .or. ::cAcao == "Gerar Cronograma"
            
			Self:ProxSeq()

			Self:AtuDoc()
            			
			Self:VlrMensal()
			
            PH5->(RecLock("PH5", .T.))
			PH5->PH5_FILIAL := xFilial("PH5")
			PH5->PH5_CONTRA := ::oCtbCro:cContrato
			PH5->PH5_REVISA := ::oCtbCro:cRevisa
			PH5->PH5_NUMERO := ::oCtbCro:cPlanilha
			PH5->PH5_COMPET := AMtoCmp(::cAnoMes)
			PH5->PH5_ANOMES := ::cAnoMes
			PH5->PH5_CMPCTB := Left(Dtos(Date()), 6)
			PH5->PH5_CONDIC := StrZero(::nPeriodico - 1, 2)
			PH5->PH5_CLIENT := ::oCtbCro:cCliente
			PH5->PH5_LOJA   := ::oCtbCro:cLoja
			PH5->PH5_CONDPG := ::oCtbCro:cCondPG
			PH5->PH5_CODISS := ::oCtbCro:cCodISS
			PH5->PH5_NOTASE := ::oCtbCro:cNotaSe
			PH5->PH5_MOEDA  := ::oCtbCro:cMoeda
			PH5->PH5_MASCCC := ::oCtbCro:cMascCC
			PH5->PH5_ITEM   := ::oCtbCro:cItem
			PH5->PH5_PRODUT := ::oCtbCro:cProduto
			PH5->PH5_FOLDPR := ::oCtbCro:cFoldPr

			PH5->PH5_GRUPO  := ::oCtbCro:cGrupo
			PH5->PH5_UNINEG := ::oCtbCro:cUniNeg
			PH5->PH5_GU     := ::oCtbCro:cGU
			PH5->PH5_SITUAC := ::cSituac
			PH5->PH5_STATRM := ::oCtbCro:cStatRM

			PH5->PH5_PROPOS := ::oCtbCro:cPropos
			PH5->PH5_REVPRO := ::oCtbCro:cRevPro
			PH5->PH5_ITMPRO := ::oCtbCro:cItmPro

			PH5->PH5_SEQ    := ::cNewSeq


			PH5->PH5_PERRAT := ::nAtuPerRat

			PH5->PH5_QUANT  := ::nAtuQuant
			PH5->PH5_QTDFAT := ::nAtuQtFat
			PH5->PH5_QTDCAN := ::nAtuQtCan
			PH5->PH5_QTDREA := ::nAtuQtReat


			PH5->PH5_VLUNIT := ::nAtuVlrUni
			PH5->PH5_VLTOT  := ::nAtuVlrTot
			PH5->PH5_VLREAJ := ::nVlrTAju
			PH5->PH5_VLREAF := ::nVlTAjFin
			PH5->PH5_VLINTE := ::nVlTIntera
			PH5->PH5_INTCAN := ::nVlTIntCan
			PH5->PH5_VLDCOM := ::nAtuVlrFaS - (::nAtuVlrFaS * ::nPDesComis)
			PH5->PH5_INDCOM := ::nPDesComis

			PH5->PH5_VLINCR := ::nAtuVlrIncr

			PH5->PH5_VLBONI := ::nAtuVlrBon
			PH5->PH5_VLCARE := ::nAtuVlrCar
			PH5->PH5_VLCANC := ::nAtuVlrCan
			PH5->PH5_VLREA  := ::nAtuVlrReat
			PH5->PH5_VLTRAN := ::nVlrTraS
			PH5->PH5_VLTRAE := ::nVlrTraE

			PH5->PH5_VLRFAT := ::nAtuVlrFat
			PH5->PH5_VLTRIB := ::nAtuVlrImp
			PH5->PH5_VLFATS := ::nAtuVlrFaS
			PH5->PH5_VLNOVA := ::nVlrVNova
			PH5->PH5_FATOR  := ::oCtbCro:nImpostC
			PH5->PH5_IMPOST := ::oCtbCro:cModImpC
			PH5->PH5_UNIFAT := ::nAtuVlrUnFa

			PH5->PH5_AUDITA := If(Empty(::oCtbCro:nPercAudi), "2", "1")   /// verificar se conteudo
			PH5->PH5_PERAUD := ::oCtbCro:nPercAudi
			PH5->PH5_VLRAUD := ::nAtuVlrAud
			PH5->PH5_DTOPER := Date()
			PH5->PH5_HROPER := Time()

			PH5->PH5_BUP    := ::nAtuBilUp
			PH5->PH5_BDOWN  := ::nAtuBilDown
			PH5->PH5_IDBILL := ::cBilID
			PH5->PH5_QTDBIL := ::nAtuBilQtd
			PH5->PH5_VLRBIL := ::nAtuBilVlr
			PH5->PH5_VARBIL := ::nAtuBilVar

			PH5->PH5_VARROY := ::nAtuRoyVar
			PH5->PH5_IDAPUR := ::cAtuRoyId

			PH5->PH5_VLMULT := ::nAtuMulta
			PH5->PH5_XPERRE := ::nPerREX
			PH5->PH5_XVLRRE := ::nAtuVlrREX
			PH5->PH5_FLCORP := ::cAtuFlCorp
			
			PH5->PH5_DLTTRC := ::nRDeltaTrc
			PH5->PH5_IDTROC := ::cIdDltTrc
			PH5->PH5_TPTROC := ::cTpTroca
			PH5->PH5_VLRTRC := ::nAtuVlrTrc

			// CAMPOS COM INFORMACOES DO PEDIDO
			PH5->PH5_PEDVEN := ::cPedVen
			PH5->PH5_ITEMPV := ::cItemPv

			// CAMPOS COM INFORMAÇÕES DE MEDICAO
			PH5->PH5_NUMMED := ::cNumMed
			PH5->PH5_ITEMMD := ::cItemMd

			// CAMPOS COM INFORMACOES DO FATURAMENTO
			PH5->PH5_NOTA   := ::cNota    
			PH5->PH5_SERIE  := ::cSerie   
			PH5->PH5_ITEMNF := ::cItemNF  
			PH5->PH5_DTMOED := ::dDtMoeda 
			PH5->PH5_TXMOED := ::nTxMoeda 
			PH5->PH5_VLMOED := ::nVlMoeda 
			PH5->PH5_UNIFT2 := ::nUniFT2  
			PH5->PH5_VLFAT2 := ::nVlFAT2 
			PH5->PH5_REVHIS := ::cRatHist 
			
			PH5->PH5_MENSAL := ::nVlrPWH 
			PH5->PH5_SOFTWA := ::nVlrSoft
			PH5->PH5_DIAFAT := ::oCtbCro:nDiaFat

			PH5->(MsUnLock())

			GrvPedPWH()

			If ! Empty(::cBilID)
				PHM->(DbGoto(::nBilRec))
				PHM->(RecLock("PHM", .F.))
				PHM->PHM_STATUS := "2"
				PHM->(MsUnLock())
			EndIf
		EndIf
	End Transaction

Return

Method VlrMensal() Class Tgcvxc06

	Local nVlrTotFat := 0

	::nVlrSoft := 0

	If ::nVlrVNova == 0
		nVlrTotFat += ::nAtuVlrTot
	Else
		nVlrTotFat += ::nVlrVNova
	EndIf

	nVlrTotFat     += ::nAtuVlrIncr 
	nVlrTotFat     += ::nVlrTAju
	nVlrTotFat     -= ::nAtuVlrBon 
	nVlrTotFat     -= ::nAtuVlrCar
	nVlrTotFat     -= ::nVlrTraS
	nVlrTotFat     -= ::nAtuVlrTrc

	::nVlrSoft := nVlrTotFat - ::nVlrPWH 
Return

Method ProxSeq() Class Tgcvxc06
	Local cNewSeq   := "000"
	Local aAreaPH5  := PH5->(GetArea())
	Local aAreaPH6  := PH6->(GetArea())
    Local aAreaCNB  := CNB->(GetArea())
    Local cChavePH5 := ""
    Local cChaveCNB := ""
	Local lAchouMed := .F. 

	cChavePH5 := xFilial("PH5")
	cChavePH5 += ::oCtbCro:cContrato
	cChavePH5 += ::oCtbCro:cRevisa
	cChavePH5 += ::oCtbCro:cPlanilha
	cChavePH5 += AMtoCmp(::cAnoMes)
	cChavePH5 += StrZero(::nPeriodico - 1, 2)
	cChavePH5 += ::oCtbCro:cCliente
	cChavePH5 += ::oCtbCro:cLoja
	cChavePH5 += ::oCtbCro:cCondPG
	cChavePH5 += ::oCtbCro:cNotaSe
	cChavePH5 += ::oCtbCro:cMoeda
	cChavePH5 += ::oCtbCro:cMascCC
    cChavePH5 += ::oCtbCro:cGU
    
    CNB->(DbSetOrder(3))  // CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM + CNB_REVISA
   
	//PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_COMPET+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU+PH5_SEQ
	PH5->(DbSetOrder(6))
    PH5->(DbSeek(cChavePH5))
    While PH5->(! Eof() .and. PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_COMPET+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU == cChavePH5 )
	    cChaveCNB := xFilial("CNB") + PH5->(PH5_CONTRA + PH5_NUMERO + PH5_ITEM + PH5_REVISA)
        CNB->(DbSeek(cChaveCNB))

        cNewSeq := PH5->PH5_SEQ
        
        // CNB_STATRM|CNB_XTPCNT|CNB_TIPREC|B1_CODISS|CNB_VENCTO
		If Self:QuebraSeq(PH5->PH5_CODISS , PH5->PH5_STATRM , CNB->CNB_XTPCNT, CNB->CNB_TIPREC, CNB->CNB_VENCTO, CNB->CNB_XFREFA, CNB->CNB_XDIAFA, CNB->CNB_XTPPG)
        
			If Empty(PH5->PH5_NUMMED) .or. PH5->PH5_NUMMED == ::cNumMed 
	            PH6->(DbSetOrder(1)) 
        
				If ::cNumMed == PH5->PH5_NUMMED .And. !empty(::cNumMed )
					lAchouMed := .T.
				EndIf

				//PH6_FILIAL+PH6_CONTRA+PH6_REVISA+PH6_NUMERO+PH6_COMPET+PH6_CONDIC+PH6_CLIENT+PH6_LOJA+PH6_CONDPG+PH6_NOTASE+PH6_MOEDA+PH6_MASCCC+PH6_GU+PH6_SEQ
				If PH6->(DbSeek(FwxFilial("PH6") + PH5->(PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_COMPET+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU+PH5_SEQ)))
        			If !Empty(PH6->PH6_NUMMED)
						PH5->(DbSkip())
						Loop 
					EndIf
				EndIf

				RestArea(aAreaCNB)
				RestArea(aAreaPH5)
				RestArea(aAreaPH6)

				If !empty(::cNumMed ) .And. !lAchouMed
					cNewSeq  := Soma1(cNewSeq)
					SELF:TrataSeqMed(cNewSeq)
				EndIf	
				
                ::cNewSeq  := cNewSeq
                Return
            EndIf
        EndIf
        
		PH5->(DbSkip())
    End

    RestArea(aAreaCNB)
	RestArea(aAreaPH5)
	RestArea(aAreaPH6)

	::cNewSeq  := Soma1(cNewSeq)
Return


Method QuebraSeq(cCodISS, cStatRM, cxTpCnt, cTipRec, dVencto, cFreFat, nDiafat, cTpPago) Class Tgcvxc06
	Local lReturn := .F.

	If Empty(cFreFat)
		cFreFat := "1"
	EndIf

	If Empty(cTpPago)
		cTpPago := "1"
	EndIf

	If cCodISS == ::oCtbCro:cCodISS .AND.;  
		cStatRM == ::oCtbCro:cStatRM .AND.;     
		cxTpCnt == ::oCtbCro:cTipCnt .AND.;     
		cTipRec == ::oCtbCro:cTipRec .AND.;
		cFreFat == ::oCtbCro:cFreFat .AND.; 
		Iif(cFreFat == "2", nDiafat == ::oCtbCro:nDiafat, .T.).AND.; 
		Iif(cTipRec == "2", dVencto == ::oCtbCro:dVencto, .T.)    
		//cTpPago == ::oCtbCro:cTpPago .AND.; 

		lReturn := .T.
	EndIf 

Return lReturn

Method AtuDoc() Class Tgcvxc06
    Local aArea := GetArea()
    Local aAreaSC5 := SC5->(GetArea())
    Local aAreaSC6 := SC6->(GetArea())
    Local aAreaSD2 := SD2->(GetArea())
	Local aAreaPH5 := PH5->(GetArea())
	Local cKeyPH6  := ""
    Local cCliRat  := ::oCtbCro:cCliente
    Local cLojRat  := ::oCtbCro:cLoja
    Local cAMFat   := AMtoCmp(::cAnoMes)
    Local cCodISS  := ::oCtbCro:cCodISS
    Local cStatRM  := ::oCtbCro:cStatRM
    Local cTipCnt  := ::oCtbCro:cTipCnt
    Local cTipRec  := ::oCtbCro:cTipRec
    Local cCondic  := StrZero(::nPeriodico - 1, 2)
    Local cCondPG  := ::oCtbCro:cCondPG
    Local cNotaSe  := ::oCtbCro:cNotaSe
    Local cMoeda   := ::oCtbCro:cMoeda
    Local cMascCC  := ::oCtbCro:cMascCC
    Local cGU      := ::oCtbCro:cGU
    Local cPropos  := ::oCtbCro:cPropos


    If SC5->(FieldPos("C5_KEYPH6")) == 0
        Return 
    EndIf 

    cKeyPH6  := cCliRat + cLojRat + cAMFat+ cCodISS + cStatRM + cTipCnt + cTipRec + cCondic + cCondPG + cNotaSe + cMoeda + cMascCC + cGU + cPropos
    
    SC5->(DbOrderNickName("SC5KEYPH6")) //C5_FILIAL + C5_KEYPH6
    If ! SC5->(DbSeek(::oCtbCro:cUniNeg + cKeyPH6))
        RestArea(aAreaSC5)
        RestArea(aArea) 
        Return 
    EndIf 

    SC6->(DbOrderNickName("SC6KEYPH6"))  //C6_FILIAL+C6_NUM+C6_XPROPOS+C6_XITPROP
    If ! SC6->(DbSeek(::oCtbCro:cUniNeg + SC5->C5_NUM + ::oCtbCro:cPropos +::oCtbCro:cItmPro))
        RestArea(aAreaSC5)
        RestArea(aArea) 
        Return 
    EndIf 

	
	::cNumMed := SC5->C5_MDNUMED
    ::cItemMd := SC6->C6_ITEMED

    IF Empty(::cItemMd)
        Self:AtuMed()
    EndIf 
    
    If Empty(SC5->C5_MDCONTR)
        SC5->(Reclock("SC5", .F.))
        SC5->C5_MDCONTR := ::oCtbCro:cContrato
        SC5->C5_XMDREV  := ::oCtbCro:cRevisa
        SC5->C5_MDPLANI := ::oCtbCro:cPlanilha
        SC5->C5_XCOMPET := AMtoCmp(::cAnoMes) 
        SC5->C5_MDNUMED := ::cNumMed
        SC5->(MsUnLock())
    EndIf 
 
    If Empty(SC6->C6_CONTRT)
        SC6->(Reclock("SC6", .F.))
        SC6->C6_CONTRT  := ::oCtbCro:cContrato
        SC6->C6_XITMPLA := ::oCtbCro:cItem
        SC6->C6_ITEMED  := ::cItemMd
        SC6->(MsUnLock())

        //C6_TPCONTR  Tipo de contratao // não é utilizado nos fontes 
        //C6_ITCONTR  Item de contrato  // não é utilizado nos fontes
        //C6_ITEMCNB  Item do contrato  // não está sendo gravado nada, não atualizar
    EndIf 
    ::cPedVen := SC6->C6_NUM
    ::cItemPv := SC6->C6_ITEM 

	If ::oCtbCro:lNovTrat 
		PH5->(DbSetOrder(4))    
		PH5->(DbSeek( Fwxfilial('PH5') + (::oCtbCro:cGrupo + ::oCtbCro:cUniNeg) + ::cPedVen + ::cItemPv  ))
		If PH5->(!Eof())
			If PH5->PH5_SEQ <> ::cNewSeq
				::cNumMed := ''
				::cItemMd := ''
				::cPedVen := '' 
				::cItemPv := ''  
				RestArea(aAreaPH5)
				RestArea(aAreaSD2)
				RestArea(aAreaSC6)
				RestArea(aAreaSC5)
				RestArea(aArea)
				return
			Endif 
		Endif 
	EndIf 

    SD2->(DbSetOrder(8)) //   D2_FILIAL+D2_PEDIDO+D2_ITEMPV
    If SD2->(DbSeek(::oCtbCro:cUniNeg + ::cPedVen + ::cItemPv))
        ::cNota   := SD2->D2_DOC
        ::cSerie  := SD2->D2_SERIE
        ::cItemNF := SD2->D2_ITEM
        If ::oCtbCro:cMoeda != '1'
            ::nTxMoeda := GetAdvFVal("SM2", "M2_TXMOED" + AllTrim(::oCtbCro:cMoeda), SD2->D2_EMISSAO, 1, CriaVar('PH5_TXMOED', .F.))
            ::nVlMoeda := GetAdvFVal("SM2", "M2_MOEDA"  + AllTrim(::oCtbCro:cMoeda), SD2->D2_EMISSAO, 1, CriaVar('PH5_VLMOED', .F.))
            ::dDtMoeda := SD2->D2_EMISSAO
            ::nUniFT2  := SD2->D2_PRCVEN
            ::nVlFAT2  := SD2->D2_TOTAL
        EndIf
    EndIf 

	If ::oCtbCro:lModelo
		Self:AtuModPWH(SC6->C6_FILIAL, SC6->C6_NUM, SC6->C6_ITEM)
	Else
		Self:AtuDbPWH()
	EndIf

	

	RestArea(aAreaPH5)
    RestArea(aAreaSD2)
    RestArea(aAreaSC6)
    RestArea(aAreaSC5)
    RestArea(aArea)

Return 

Method AtuModPWH(cFilPed, cNumPed, cItmPed) Class Tgcvxc06
	Local oModPWH := ::oCtbCro:oModelo:GetModel("PWHDETAIL")
	Local ni      := 1

	For ni:=1 To oModPWH:Length()
		oModPWH:GoLine(ni)
		If oModPWH:IsDeleted()	
			Loop
		EndIf

		If oModPWH:GetValue("PWH_PEDVEN") <> cNumPed	
			Loop
		EndIf

		If oModPWH:GetValue("PWH_ITEMPV") <> cItmPed	
			Loop
		EndIf

		If oModPWH:GetValue("PWH_UNINEG") <> cFilPed	
			Loop
		EndIf

		oModPWH:LoadValue("PWH_CONTRA", ::oCtbCro:cContrato)
		oModPWH:LoadValue("PWH_REVISA", ::oCtbCro:cRevisa  )
		oModPWH:LoadValue("PWH_NUMERO", ::oCtbCro:cPlanilha)
		oModPWH:LoadValue("PWH_ITEM"  , ::oCtbCro:cItem    )
		oModPWH:LoadValue("PWH_GRUPO" , ::oCtbCro:cGrupo   )
		oModPWH:LoadValue("PWH_IMPOST", ::oCtbCro:cModImpC )
		oModPWH:LoadValue("PWH_AMCRON", ::cAnoMes          )
		oModPWH:LoadValue("PWH_SEQ"   , ::cNewSeq          )
		oModPWH:LoadValue("PWH_SITUAC", ::cSituac          )
		oModPWH:LoadValue("PWH_NOTASE", ::oCtbCro:cNotaSe  )
		oModPWH:LoadValue("PWH_NOTA"  , ::cNota            )
		oModPWH:LoadValue("PWH_SERIE" , ::cSerie           )
		oModPWH:LoadValue("PWH_ITEMNF", ::cItemNF          ) 
		PWH->(MsUnLock())

        ::nVlrPWH += oModPWH:GetValue("PWH_VLMENS")
	Next

Return


Method AtuDbPWH() Class Tgcvxc06
	Local cFilPWH  := FwxFilial("PWH")

	PWH->(DbSetOrder(4)) //PWH_FILIAL+PWH_UNINEG+PWH_PEDVEN+PWH_ITEMPV  

	PWH->(DbSeek(cFilPWH + SC6->(C6_FILIAL + C6_NUM + C6_ITEM) ) )

    While !PWH->(EOF()) .And. cFilPWH + SC6->(C6_FILIAL + C6_NUM + C6_ITEM) == PWH->(PWH_FILIAL + PWH_UNINEG + PWH_PEDVEN + PWH_ITEMPV  )
	
		PWH->(RecLock("PWH", .F.))
		PWH->PWH_CONTRA := ::oCtbCro:cContrato
		PWH->PWH_REVISA := ::oCtbCro:cRevisa
		PWH->PWH_NUMERO := ::oCtbCro:cPlanilha
		PWH->PWH_ITEM   := ::oCtbCro:cItem
		PWH->PWH_GRUPO  := ::oCtbCro:cGrupo
		PWH->PWH_IMPOST  :=::oCtbCro:cModImpC
		PWH->PWH_AMCRON := ::cAnoMes
		PWH->PWH_SEQ 	:= ::cNewSeq
		PWH->PWH_SITUAC := ::cSituac
		PWH->PWH_NOTASE	:= ::oCtbCro:cNotaSe
		PWH->PWH_NOTA 	:= ::cNota
		PWH->PWH_SERIE	:= ::cSerie 
		PWH->PWH_ITEMNF	:= ::cItemNF 
		PWH->(MsUnLock())

        ::nVlrPWH += PWH->PWH_VLMENS

        PWH->(DbSkip())
    End


Return

Method AtuMed() Class Tgcvxc06
     
    If Empty(::cNumMed)
        ::cNumMed := CN130NumMd()
    EndIf 

    //Nr Contrato + Nr Revisao + Nr Planilha + Nr Medicao 
    cChave := ::oCtbCro:cUniNeg   + ::oCtbCro:cContrato + ::oCtbCro:cRevisa + ::oCtbCro:cPlanilha + ::cNumMed

    CND->(DbSetOrder(1)) //CND_FILIAL+CND_CONTRA+CND_REVISA+CND_NUMERO+CND_NUMMED
    If ! CND->(DbSeek(cChave)) 

        CND->(RecLock("CND", .T.))
        CND->CND_FILIAL := ::oCtbCro:cUniNeg  
        CND->CND_NUMMED := ::cNumMed
        CND->CND_CONTRA := ::oCtbCro:cContrato
        CND->CND_REVISA := ::oCtbCro:cRevisa
        CND->CND_NUMERO := ::oCtbCro:cPlanilha
        CND->CND_COMPET := AMtoCmp(::cAnoMes)
        CND->CND_SITUAC := "E" 
        CND->CND_PEDIDO := SC5->C5_NUM 
        CND->CND_XCLIEN := ::oCtbCro:cCliente 
        CND->CND_XLJCLI := ::oCtbCro:cLoja 
        CND->CND_CONDPG := ::oCtbCro:cCondPG
        CND->CND_XNUMER := ::oCtbCro:cPlanilha
        CND->CND_XCONDI := StrZero(::nPeriodico - 1, 2)
        CND->CND_VLRAJU := 0
        CND->CND_DTFIM  := SC5->C5_EMISSAO
        CND->(MsUnlock())
        
        /*
        CND->CND_PARC1 ... CND_PARCZ
        CND->CND_DATA1 ... CND_DATAZ
        */
        
        CNA->(DbSetOrder(1)) //CNA_FILIAL+CNA_CONTRA+CNA_REVISA+CNA_NUMERO
        CNA->(DbSeek(xFilial("CNA") + ::oCtbCro:cContrato + ::oCtbCro:cRevisa + ::oCtbCro:cPlanilha))

        CXN->(RecLock("CXN", .T.))
        CXN->CXN_FILIAL := ::oCtbCro:cUniNeg  
        CXN->CXN_CONTRA := ::oCtbCro:cContrato				
        CXN->CXN_REVISA := ::oCtbCro:cRevisa
        CXN->CXN_NUMMED := ::cNumMed				
        CXN->CXN_NUMPLA := ::oCtbCro:cPlanilha				
        CXN->CXN_DTINI  := CNA->CNA_DTINI  	 	
        CXN->CXN_DTFIM  := CNA->CNA_DTFIM  		
        CXN->CXN_CRONOG := CNA->CNA_CRONOG			
        CXN->CXN_TIPPLA := CNA->CNA_TIPPLA			
        CXN->CXN_CLIENT := ::oCtbCro:cCliente 				
        CXN->CXN_LJCLI	:= ::oCtbCro:cLoja 			
        CXN->CXN_CHECK  := .T.
		CXN->CXN_VLSALD := 0					
        CXN->CXN_VLTOT  := 0
        CXN->CXN_VLPREV := 0
        CXN->CXN_XCONDI := StrZero(::nPeriodico - 1, 2)
        CXN->CXN_XMOEDA := ::oCtbCro:cMoeda
        CXN->CXN_XNOTAS := ::oCtbCro:cNotaSe
        CXN->CXN_XMASCC := ::oCtbCro:cMascCC
        CXN->CXN_XCONPG := ::oCtbCro:cCondPG
        CXN->CXN_XCOMPE := AMtoCmp(::cAnoMes) 
        CXN->(MsUnlock())
    EndIf 

    CNE->(RecLock("CNE", .T.))
    CNE->CNE_FILIAL := ::oCtbCro:cUniNeg  
    CNE->CNE_CONTRA := ::oCtbCro:cContrato 
    CNE->CNE_ITEM   := ::oCtbCro:cItem
    CNE->CNE_REVISA := ::oCtbCro:cRevisa
    CNE->CNE_NUMERO := ::oCtbCro:cPlanilha 
    CNE->CNE_NUMMED := ::cNumMed
    CNE->CNE_PRODUT := ::oCtbCro:cProduto 
    CNE->CNE_XGU    := ::oCtbCro:cGU
    CNE->CNE_XSEQH5 := ::cNewSeq
    CNE->CNE_QUANT  := ::nAtuQtFat
    CNE->CNE_VLUNIT := ::nAtuVlrUni
    CNE->CNE_VLTOT  := ::nAtuVlrFat
    CNE->CNE_XCONPG := ::oCtbCro:cCondPG
    CNE->CNE_XMASCC := ::oCtbCro:cMascCC
    CNE->CNE_XMOEDA := ::oCtbCro:cMoeda

    CNE->(MsUnlock())

    ::cItemMd := ::oCtbCro:cItem
    
 
 Return 


Method ProxSeqM(oContrato, oModel) Class Tgcvxc06
    Local cNewSeq   := "000"
    Local nx        := 0
    Local cChavePH5 := ""
    Local cObjPH5   := ""

    Local oModCNA	:= oModel:GETMODEL("CNADETAIL")   
    Local aChaveCNA := ""
    Local nPosCNA   := 0
    Local oModCNB	:= oModel:GETMODEL("CNBDETAIL")   
    Local aChaveCNB := ""
	Local nPosCNB   := 0
	Local oModPH6	:= oModel:GETMODEL("PH6DETAIL")  
	Local aSeekPH6	:= {}
	Local nPosPH6	:= 0
	Local oModPH7	:= oModel:GETMODEL("PH7DETAIL")  
	Local aSeekPH7	:= {}
	Local nPosPH7	:= 0
	Local aSaveLines:= FWSaveRows()
	Local cCodISS   := ""
	Local cStatRM   := ""
	Local cXTpCnt   := ""
	Local cTipRec   := ""
	Local dVencto   := CtoD("")
	Local cXFreFa   := ""
	Local cXDiaFa   := 0
	Local cXTpPg    := ""
	
	If oModPH6 == NIL

		Self:ProxSeq()
		Return 
	
	EndIf 
	
	//cChavePH5 := ::oCtbCro:cContrato
	//cChavePH5 += ::oCtbCro:cRevisa
	cChavePH5 := ::oCtbCro:cPlanilha
	cChavePH5 += AMtoCmp(::cAnoMes)
	cChavePH5 += StrZero(::nPeriodico - 1, 2)
	cChavePH5 += ::oCtbCro:cCliente
	cChavePH5 += ::oCtbCro:cLoja
	cChavePH5 += ::oCtbCro:cCondPG
	cChavePH5 += ::oCtbCro:cNotaSe
	cChavePH5 += ::oCtbCro:cMoeda
	cChavePH5 += ::oCtbCro:cMascCC
    cChavePH5 += ::oCtbCro:cGU

    For nx:= 1 to len(oContrato:aPH5)
        //cObjPH5 := oContrato:GetPH5("PH5_CONTRA", nx)
        //cObjPH5 += oContrato:GetPH5("PH5_REVISA", nx)
        cObjPH5 := oContrato:GetPH5("PH5_NUMERO", nx)
        cObjPH5 += oContrato:GetPH5("PH5_COMPET", nx)
        cObjPH5 += oContrato:GetPH5("PH5_CONDIC", nx)
        cObjPH5 += oContrato:GetPH5("PH5_CLIENT", nx)
        cObjPH5 += oContrato:GetPH5("PH5_LOJA"  , nx)
        cObjPH5 += oContrato:GetPH5("PH5_CONDPG", nx)
        cObjPH5 += oContrato:GetPH5("PH5_NOTASE", nx)
        cObjPH5 += oContrato:GetPH5("PH5_MOEDA" , nx)
        cObjPH5 += oContrato:GetPH5("PH5_MASCCC", nx)
        cObjPH5 += oContrato:GetPH5("PH5_GU"    , nx)


        If ! cChavePH5 == cObjPH5
            Loop 
        EndIf 


        aChaveCNA := { {"CNA_NUMERO" , oContrato:GetPH5("PH5_NUMERO"  , nx)}}
        
        nPosCNA := U_TGVFndMVC(oModCNA, aChaveCNA) 
        oModCNA:GoLine(nPosCNA)

        If oModCNA:IsDeleted()
            Loop
        EndIf

        aChaveCNB := { {"CNB_NUMERO" , oContrato:GetPH5("PH5_NUMERO"  , nx)},; 
                       {"CNB_ITEM"	 , oContrato:GetPH5("PH5_ITEM"    , nx)}} 
        
        nPosCNB := U_TGVFndMVC(oModCNB, aChaveCNB) 
        If Empty(nPosCNB)
            Loop 
        EndIf
        oModCNB:GoLine(nPosCNB)

        If oModCNB:IsDeleted()
            Loop
        EndIf

        cNewSeq := oContrato:GetPH5("PH5_SEQ", nx)

		cCodISS   := oContrato:GetPH5("PH5_CODISS", nx)
		cStatRM   := oContrato:GetPH5("PH5_STATRM", nx)
		cXTpCnt   := oModCNB:GetValue("CNB_XTPCNT")
		cTipRec   := oModCNB:GetValue("CNB_TIPREC")
		dVencto   := oModCNB:GetValue("CNB_VENCTO")
		cXFreFa   := oModCNB:GetValue("CNB_XFREFA")
		cXDiaFa   := oModCNB:GetValue("CNB_XDIAFA")
		cXTpPg    := oModCNB:GetValue("CNB_XTPPG")


		If Self:QuebraSeq(cCodISS, cStatRM, cXTpCnt, cTipRec, dVencto, cXFreFa, cXDiaFa, cXTpPg)

			If Empty(oContrato:GetPH5("PH5_NUMMED", nx))
				aSeekPH7	:= {}
				//AAdd(aSeekPH7, { "PH7_CONTRA" ,  oContrato:GetPH5("PH5_CONTRA", nx) })
				//AAdd(aSeekPH7, { "PH7_REVISA" ,  oContrato:GetPH5("PH5_REVISA", nx) })
				AAdd(aSeekPH7, { "PH7_NUMERO" ,  oContrato:GetPH5("PH5_NUMERO", nx) })
				AAdd(aSeekPH7, { "PH7_COMPET" ,  oContrato:GetPH5("PH5_COMPET", nx) })
				AAdd(aSeekPH7, { "PH7_CONDIC" ,  oContrato:GetPH5("PH5_CONDIC", nx) })
				AAdd(aSeekPH7, { "PH7_MOEDA" ,  oContrato:GetPH5("PH5_MOEDA", nx) })
				AAdd(aSeekPH7, { "PH7_GU"   ,  oContrato:GetPH5("PH5_GU"  , nx) })
				If (nPosPH7 := U_TGVFndMVC(oModPH7,aSeekPH7))  > 0
					oModPH7:GoLine(nPosPH7)
				EndIf 

				aSeekPH6	:= {}
				//AAdd(aSeekPH6, { "PH6_CONTRA" ,  oContrato:GetPH5("PH5_CONTRA", nx) })
				//AAdd(aSeekPH6, { "PH6_REVISA" ,  oContrato:GetPH5("PH5_REVISA", nx) })
				AAdd(aSeekPH6, { "PH6_NUMERO" ,  oContrato:GetPH5("PH5_NUMERO", nx) })
				AAdd(aSeekPH6, { "PH6_COMPET" ,  oContrato:GetPH5("PH5_COMPET", nx) })
				AAdd(aSeekPH6, { "PH6_CONDIC" ,  oContrato:GetPH5("PH5_CONDIC", nx) })
				AAdd(aSeekPH6, { "PH6_CLIENT" ,  oContrato:GetPH5("PH5_CLIENT", nx) })
				AAdd(aSeekPH6, { "PH6_LOJA"   ,  oContrato:GetPH5("PH5_LOJA"  , nx) })
				AAdd(aSeekPH6, { "PH6_CONDPG" ,  oContrato:GetPH5("PH5_CONDPG", nx) })
				AAdd(aSeekPH6, { "PH6_NOTASE" ,  oContrato:GetPH5("PH5_NOTASE", nx) })
				AAdd(aSeekPH6, { "PH6_MOEDA"  ,  oContrato:GetPH5("PH5_MOEDA" , nx) })
				AAdd(aSeekPH6, { "PH6_MASCCC" ,  oContrato:GetPH5("PH5_MASCCC", nx) })
				AAdd(aSeekPH6, { "PH6_GU"     ,  oContrato:GetPH5("PH5_GU"    , nx) })
				AAdd(aSeekPH6, { "PH6_SEQ"    ,  cNewSeq })
				If (nPosPH6 := U_TGVFndMVC(oModPH6,aSeekPH6))  > 0
					oModPH6:GoLine(nPosPH6)
					If ! oModPH6:IsDeleted()
						If !Empty(oModPH6:GetValue("PH6_NUMMED"))
							Loop
						EndIF
					EndIf
				EndIf 
				::cNewSeq  := cNewSeq
				FWRestRows(aSaveLines)
				Return         
			EndIf
			
		EndIf
    Next
	
    ::cNewSeq  := Soma1(cNewSeq)
    FWRestRows(aSaveLines)
    
    
Return


Method DelPH5() Class Tgcvxc06
	Local nx := 0

	PWH->(DbSetOrder(3)) //PWH_FILIAL+PWH_CONTRA+PWH_NUMERO+PWH_ITEM+PWH_AMCRON+PWH_SEQ+PWH_CLIENT+PWH_LOJA+PWH_ANOMES            

	For nx:= 1 to len(::aPH5)
		If ! Empty(::aPH5[nx]:cNumMed)
			Loop
		EndIf
		PH5->(DbGoto(::aPH5[nx]:nRecnoPH5))
		DeletePWH()
		PH5->(RecLock("PH5", .F.))
		PH5->(DbDelete())
		PH5->(MsUnLock())
	Next

Return

Static Function DeletePWH()
    Local cChvPWH    := FwxFilial("PWH") + PH5->(PH5_CONTRA + PH5_NUMERO + PH5_ITEM + PH5_ANOMES + PH5_SEQ + PH5_CLIENT + PH5_LOJA)
    Local aRecDelPWH := {}
    Local ni         := 0
	
	PWH->(DbSetOrder(3)) //PWH_FILIAL+PWH_CONTRA+PWH_NUMERO+PWH_ITEM+PWH_AMCRON+PWH_SEQ+PWH_CLIENT+PWH_LOJA+PWH_ANOMES            
    PWH->(DbSeek(cChvPWH))

    While PWH->(!Eof()) .and. cChvPWH == PWH->(PWH_FILIAL + PWH_CONTRA + PWH_NUMERO + PWH_ITEM + PWH_AMCRON + PWH_SEQ + PWH_CLIENT + PWH_LOJA)
        If PWH->PWH_TIPO == "4"    
            If Empty(PWH->PWH_PEDVEN)
                AADD(aRecDelPWH, PWH->(Recno()))
            EndIf
        EndIf
        PWH->(DbSkip())
    End

    For ni:= 1 to Len(aRecDelPWH)
        PWH->(DbGoTo(aRecDelPWH[ni]))

        PWH->(RecLock("PWH", .F.))
            PWH->(DbDelete())
        PWH->(MsUnLock())
    Next

    aSize(aRecDelPWH, 0)
    aRecDelPWH := Nil
Return


Method SalvaCtr(oContrato, lReIndex) Class Tgcvxc06
	Local np := 0

	If ! Self:lGeraCro
		Return
	EndIf

	If ! (::cAcao == "Atualizar Cronograma" .or. ::cAcao == "Gerar Cronograma")
		Return
	EndIf

	Self:ProxSeqM(oContrato, oContrato:oModelo)

	Self:AtuDoc()
	Self:VlrMensal()
	oContrato:NewPH5() // inicialiaza array
	np:= len(oContrato:aPH5)

	oContrato:PutPH5("PH5_FILIAL", xFilial("PH5")                             , nP)
	oContrato:PutPH5("PH5_CONTRA", Self:oCtbCro:cContrato                     , nP)
	oContrato:PutPH5("PH5_REVISA", Self:oCtbCro:cRevisa                       , nP)
	oContrato:PutPH5("PH5_NUMERO", Self:oCtbCro:cPlanilha                     , nP)
	oContrato:PutPH5("PH5_COMPET", AMtoCmp(Self:cAnoMes)                      , nP)
	oContrato:PutPH5("PH5_ANOMES", Self:cAnoMes                               , nP)
	oContrato:PutPH5("PH5_CMPCTB", Left(Dtos(Date()), 6)                      , nP)
	oContrato:PutPH5("PH5_CONDIC", StrZero(Self:oCtbCro:nPeriodico - 1, 2)    , nP)
	oContrato:PutPH5("PH5_CLIENT", Self:oCtbCro:cCliente                      , nP)
	oContrato:PutPH5("PH5_LOJA  ", Self:oCtbCro:cLoja                         , nP)
	oContrato:PutPH5("PH5_CONDPG", Self:oCtbCro:cCondPG                       , nP)
	oContrato:PutPH5("PH5_CODISS", Self:oCtbCro:cCodISS                       , nP)
	oContrato:PutPH5("PH5_NOTASE", Self:oCtbCro:cNotaSe                       , nP)
	oContrato:PutPH5("PH5_MOEDA ", Self:oCtbCro:cMoeda                        , nP)
	oContrato:PutPH5("PH5_MASCCC", Self:oCtbCro:cMascCC                       , nP)
	oContrato:PutPH5("PH5_ITEM  ", Self:oCtbCro:cItem                         , nP)
	oContrato:PutPH5("PH5_PRODUT", Self:oCtbCro:cProduto                      , nP)
	oContrato:PutPH5("PH5_FOLDPR", Self:oCtbCro:cFoldPr                       , nP)
	oContrato:PutPH5("PH5_GRUPO ", Self:oCtbCro:cGrupo                        , nP)
	oContrato:PutPH5("PH5_UNINEG", Self:oCtbCro:cUniNeg                       , nP)
	oContrato:PutPH5("PH5_GU    ", Self:oCtbCro:cGU                           , nP)
	oContrato:PutPH5("PH5_SITUAC", Self:cSituac                               , nP)
	oContrato:PutPH5("PH5_STATRM", Self:oCtbCro:cStatRM                       , nP)
	oContrato:PutPH5("PH5_PROPOS", Self:oCtbCro:cPropos                       , nP)
	oContrato:PutPH5("PH5_REVPRO", Self:oCtbCro:cRevPro                       , nP)
	oContrato:PutPH5("PH5_ITMPRO", Self:oCtbCro:cItmPro                       , nP)
	oContrato:PutPH5("PH5_SEQ   ", Self:cNewSeq                               , nP)
	oContrato:PutPH5("PH5_PERRAT", Self:nAtuPerRat                            , nP)
	oContrato:PutPH5("PH5_QUANT ", Self:nAtuQuant                             , nP)
	oContrato:PutPH5("PH5_QTDFAT", Self:nAtuQtFat                             , nP)
	oContrato:PutPH5("PH5_QTDCAN", Self:nAtuQtCan                             , nP)
	oContrato:PutPH5("PH5_QTDREA", Self:nAtuQtReat                            , nP)
	oContrato:PutPH5("PH5_VLUNIT", Self:nAtuVlrUni                            , nP)
	oContrato:PutPH5("PH5_VLTOT ", Self:nAtuVlrTot                            , nP)
	oContrato:PutPH5("PH5_VLREAJ", Self:nVlrTAju                              , nP)
	oContrato:PutPH5("PH5_VLREAF", Self:nVlTAjFin                             , nP)
	oContrato:PutPH5("PH5_VLINTE", Self:nVlTIntera                            , nP)
	oContrato:PutPH5("PH5_INTCAN", Self:nVlTIntCan                            , nP)

	oContrato:PutPH5("PH5_VLDCOM", Self:nAtuVlrFaS - (Self:nAtuVlrFaS * Self:nPDesComis) , nP)
	oContrato:PutPH5("PH5_INDCOM", Self:nPDesComis                            , nP)
	oContrato:PutPH5("PH5_VLINCR", Self:nAtuVlrIncr                           , nP)
	oContrato:PutPH5("PH5_FLCORP", Self:cAtuFlCorp                            , nP)
	oContrato:PutPH5("PH5_VLBONI", Self:nAtuVlrBon                            , nP)
	oContrato:PutPH5("PH5_VLCARE", Self:nAtuVlrCar                            , nP)
	oContrato:PutPH5("PH5_VLCANC", Self:nAtuVlrCan                            , nP)
	oContrato:PutPH5("PH5_VLREA ", Self:nAtuVlrReat                           , nP)
	oContrato:PutPH5("PH5_VLTRAN", Self:nVlrTraS                              , nP)
	oContrato:PutPH5("PH5_VLTRAE", Self:nVlrTraE                              , nP)
	oContrato:PutPH5("PH5_VLRFAT", Self:nAtuVlrFat                            , nP)
	oContrato:PutPH5("PH5_VLTRIB", Self:nAtuVlrImp                            , nP)
	oContrato:PutPH5("PH5_VLFATS", Self:nAtuVlrFaS                            , nP)
	oContrato:PutPH5("PH5_VLNOVA", Self:nVlrVNova                             , nP)
	oContrato:PutPH5("PH5_FATOR ", Self:oCtbCro:nImpostC                      , nP)
	oContrato:PutPH5("PH5_IMPOST", Self:oCtbCro:cModImpC                      , nP)
	oContrato:PutPH5("PH5_UNIFAT", Self:nAtuVlrUnFa                           , nP)
	oContrato:PutPH5("PH5_AUDITA", If(Empty(Self:oCtbCro:nPercAudi), "2", "1"), nP) /// verificar se conteudo
	oContrato:PutPH5("PH5_PERAUD", Self:oCtbCro:nPercAudi                     , nP)
	oContrato:PutPH5("PH5_VLRAUD", Self:nAtuVlrAud                            , nP)
	oContrato:PutPH5("PH5_DTOPER", Date()                                     , nP)
	oContrato:PutPH5("PH5_HROPER", Time()                                     , nP)
	oContrato:PutPH5("PH5_BUP   ", Self:nAtuBilUp                             , nP)
	oContrato:PutPH5("PH5_BDOWN ", Self:nAtuBilDown                           , nP)
	oContrato:PutPH5("PH5_IDBILL", Self:cBilID                                , nP)
	oContrato:PutPH5("PH5_QTDBIL", Self:nAtuBilQtd                            , nP)
	oContrato:PutPH5("PH5_VLRBIL", Self:nAtuBilVlr                            , nP)
	oContrato:PutPH5("PH5_VARBIL", Self:nAtuBilVar                            , nP)
	oContrato:PutPH5("PH5_VARROY", Self:nAtuRoyVar                            , nP)
	oContrato:PutPH5("PH5_IDAPUR", Self:cAtuRoyId                             , nP)
	oContrato:PutPH5("PH5_VLMULT", Self:nAtuMulta                             , nP)
	oContrato:PutPH5("PH5_XPERRE", Self:nPerREX                               , nP)
	oContrato:PutPH5("PH5_XVLRRE", Self:nAtuVlrREX                            , nP)
	oContrato:PutPH5("PH5_DLTTRC", Self:nRDeltaTrc                            , nP)
	oContrato:PutPH5("PH5_IDTROC", Self:cIdDltTrc                             , nP)
	oContrato:PutPH5("PH5_TPTROC", Self:cTpTroca                              , nP)
	oContrato:PutPH5("PH5_VLRTRC", Self:nAtuVlrTrc                            , nP)
	oContrato:PutPH5("PH5_REVHIS", Self:cRatHist                              , nP)
	oContrato:PutPH5("PH5_PEDVEN", Self:cPedVen                               , nP)
	oContrato:PutPH5("PH5_ITEMPV", Self:cItemPv                               , nP)
	oContrato:PutPH5("PH5_NUMMED", Self:cNumMed                               , nP)
	oContrato:PutPH5("PH5_ITEMMD", Self:cItemMd                               , nP)
	oContrato:PutPH5("PH5_NOTA"  , Self:cNota                                 , nP)
	oContrato:PutPH5("PH5_SERIE" , Self:cSerie                                , nP)
	oContrato:PutPH5("PH5_ITEMNF", Self:cItemNF                               , nP)
	oContrato:PutPH5("PH5_DTMOED", Self:dDtMoeda                              , nP)
	oContrato:PutPH5("PH5_TXMOED", Self:nTxMoeda                              , nP)
	oContrato:PutPH5("PH5_VLMOED", Self:nVlMoeda                              , nP)
	oContrato:PutPH5("PH5_MENSAL", Self:nVlrPWH                               , nP)
	oContrato:PutPH5("PH5_SOFTWA", Self:nVlrSoft                              , nP)
	oContrato:PutPH5("PH5_DIAFAT", ::oCtbCro:nDiaFat                          , nP)

	If lReIndex
		oContrato:IndexPH5()
	Endif 
Return


	// funcoes auxiliares para tratamento de datas e compentencias

Static Function AdicMes(cMesAno, nMes)
	Local cMes := ""
	Local cAno := ""
	Local cMAAux := cMesAno
	Local nx

	For nx := 1 to nMes
		cMes:= Left(cMAAux, 2)
		cAno:= Right(cMAAux, 4)
		If cMes == '12'
			cMAAux := '01/' + Soma1(cAno)
		Else
			cMAAux := Soma1(cMes) + '/' + cAno
		EndIf
	Next

Return cMAAux

Static Function AMtoCmp(cAM)
	Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp)
	Local cAM := Right(cCmp, 4) + Left(cCmp, 2)

Return cAM
 
Static __cAnoMes := ""
Static __cNumero := ""
Static __cItem   := ""

Static Function SetDebug(cAnoMes, cNumero, cItem)
    Default cAnoMes:= ""
    Default cNumero:= ""
    Default cItem  := ""

    __cAnoMes := cAnoMes
    __cNumero := cNumero
    __cItem   := cItem  

Return 

Static Function IsDebug(oObj)
    Local cAnoMes := oObj:cAnoMes
    Local cNumero := oObj:oCtbCro:cPlanilha
    Local cItem   := oObj:oCtbCro:cItem
		
    If ! Empty(__cAnoMes) .and. ! Empty(__cNumero) .and. ! Empty(__cItem)   
        If cAnoMes + cNumero + cItem == __cAnoMes + __cNumero + __cItem
            Return .T. 
        EndIf 
    ElseIf ! Empty(__cAnoMes) .and. ! Empty(__cNumero)
        If cAnoMes + cNumero == __cAnoMes + __cNumero
            Return .T. 
        EndIf 
    Else

        If cAnoMes == __cAnoMes 
            Return .T. 
        EndIf 
    EndIf 

Return .F.

Method TrataSeqMed(cNewSeq) Class Tgcvxc06
	Local cChave := ::oCtbCro:cUniNeg + ::cNumMed
	
	CNE->(DbSetOrder(4))
	CNE->(Dbseek(::oCtbCro:cUniNeg + ::cNumMed))
	
	while CNE->(!Eof()) .And. CNE->(CNE_FILIAL+CNE_NUMMED) == cChave
		CNE->(reclock("CNE",.F.))
		CNE->CNE_XSEQH5 := cNewSeq 
		CNE->(msunlock())
		CNE->(DbSkip())
	End
	
	CXN->(DbSetOrder(3))
	If CXN->(Dbseek(::oCtbCro:cUniNeg +  ::oCtbCro:cContrato + ::oCtbCro:cRevisa + ::cNumMed))
		CXN->(reclock("CXN",.F.))
		CXN->CXN_XSEQH5 := cNewSeq 
		CXN->(msunlock())
	EndIf
return 

Static Function GrvPedPWH()
    Local cChvPH5 := FwxFilial("PWH") + PH5->(PH5_CONTRA + PH5_NUMERO + PH5_ITEM + PH5_ANOMES + PH5_SEQ + PH5_CLIENT + PH5_LOJA)

    PWH->(DbSetOrder(3)) //PWH_FILIAL+PWH_CONTRA+PWH_NUMERO+PWH_ITEM+PWH_AMCRON+PWH_SEQ+PWH_CLIENT+PWH_LOJA

    PWH->(DbSeek(cChvPH5))

    While PWH->(!Eof()) .and. cChvPH5 == PWH->(PWH_FILIAL + PWH_CONTRA + PWH_NUMERO + PWH_ITEM + PWH_AMCRON + PWH_SEQ + PWH_CLIENT + PWH_LOJA)
        If PWH->(RecLock("PWH", .F.))
            PWH->PWH_PEDVEN := PH5->PH5_PEDVEN
            PWH->PWH_ITEMPV := PH5->PH5_ITEMPV
   
            PWH->(MsUnlock())
        EndIf
        PWH->(DbSkip())
    End

Return

