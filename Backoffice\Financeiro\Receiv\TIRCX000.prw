#INCLUDE 'TOTVS.CH'

#DEFINE COL_VALOR       02
#DEFINE COL_FILIAL      03
#DEFINE COL_FILACD      04
#DEFINE COL_PREFIXO     05
#DEFINE COL_NUM         06
#DEFINE COL_PARCELA     07
#DEFINE COL_TIPO        08
#DEFINE COL_DTCRED      09
#DEFINE COL_BANCO       10
#DEFINE COL_AGENCIA     11
#DEFINE COL_CONTA       12
#DEFINE COL_ACORDOS     13
#DEFINE COL_IDACORDO    14
#DEFINE COL_LOGBX       15

/*/

Biblioteca de Funções Receiv.

Este código-fonte tem a finalidade de armazenar funções genéricas
utilizadas no projeto Receiv.

<AUTHOR>

@since 29/08/2021

/*/

/*/{Protheus.doc} TIRCX00A

    Função responsável por:
        
        1. Verificar se o registro de retorno corresponde a título Receiv;
        2. Definir o acordo como pago.
    
	<AUTHOR>	
	
    @since 29/08/2021

/*/
User Function TIRCX00A(cNsNum, cOcorr, dDataCred, cBanco, cAgencia, cConta,dDataBX,nJuros)

	Local lReceiv   := .F.

	DbSelectArea('P38')
	P38->(DbSetOrder(2))
	If P38->(MsSeek(cNsNum))

		if P38->P38_TIPO <> 'P' .and. alltrim(cOcorr) $ GetMv('RC_OCORBX', , '06|07|08|09|10|11|36|37|38|39')
			lReceiv := .T.

			If !Empty(SEE->EE_CTAOFI)

				cBanco		:= SEE->EE_CODOFI
				cAgencia	:= SEE->EE_AGEOFI
				cConta		:= SEE->EE_CTAOFI

			Endif

			RecLock("P38", .F.)
			P38->P38_BANCO  := cBanco
			P38->P38_AGENCI := cAgencia
			P38->P38_CONTA  := cConta
			P38->P38_OCORRE := cOcorr
			P38->P38_DTCRED := dDataBX
			P38->P38_VLJURO	:= nJuros

			P38->(MsUnLock())
		Endif

	EndIf

Return lReceiv





/*/{Protheus.doc} GRAVACV8
Função genérica pela gravação da pnv e cv8
@type  Function
@since 08/09/2021
@version 1.0
 */
USER FUNCTION GRAVACV8( cAlias, cFil, cChave, cObs, cTitulo )
Local cChaveCV8 := ""
Local cIdDoc    := ""

Default cObs    := ""
Default cTitulo := ""

If cAlias == "SE1"

   DBSELECTAREA("PNV")
   
   RecLock("PNV", .T.)
   PNV->PNV_FILIAL := SE1->E1_FILIAL
   PNV->PNV_PREFIX := SE1->E1_PREFIXO
   PNV->PNV_NUM    := SE1->E1_NUM
   PNV->PNV_PARCEL := SE1->E1_PARCELA
   PNV->PNV_TIPO   := SE1->E1_TIPO
   PNV->PNV_DATA   := dDataBase
   PNV->PNV_DTSERV := Date()
   PNV->PNV_HORA   := Time()
   PNV->PNV_VENREA := SE1->E1_VENCREA
   PNV->PNV_SITUAC := SE1->E1_SITUACA
   PNV->PNV_HIST   := cObs
   PNV->PNV_VALOR  := SE1->E1_VALOR
   PNV->PNV_SALDO  := SE1->E1_SALDO
   PNV->(MsUnLock())
EndIf

DbSelectArea("FK7")
DbSetOrder(2)
      
   FK7->(DbSeek( cFil + cAlias + AvKey(cChave,"FK7_CHAVE")))
   If Alltrim(FK7->FK7_CHAVE) == Alltrim(cChave)
   	cIdDoc	:= FK7->FK7_IDDOC
   Else
   	cIdDoc	:= FINGRVFK7( cAlias, cChave, cFil )
   EndIf
   
   cChaveCV8	:= cFil
   ProcLogIni({}, cIdDoc, , @cChaveCV8,cFil)
   ProcLogAtu("Mensagem: ",cTitulo, cObs,,.T.,cFil)

RETURN


/*/{Protheus.doc} User Function RcvStamp
Funcao Responsavel por Retornar a Data e Hora para busca do S_T_A_M_P_
    @type  Function
    <AUTHOR>
    @since 30/09/2021
    @version Mauricio Madureira
 /*/
User Function RcvStamp(cTime, nInc)
	Local cDtHora:= ""
	Local dData  := Date()

	Default nInc := 3

	If Left(cTime,2) >= "24"
		cTime     := strtran( strtran(Left(cTime,2),"24","00") + Substr(cTime,3,Len(cTime)), ":")
		cTime     := strtran( strtran(Left(cTime,2),"25","01") + Substr(cTime,3,Len(cTime)), ":")
		cTime     := strtran( strtran(Left(cTime,2),"26","02") + Substr(cTime,3,Len(cTime)), ":")
		cTime     := strtran( strtran(Left(cTime,2),"27","03") + Substr(cTime,3,Len(cTime)), ":")
		cDtHora     := dTos(dData+1)+cTime
	Else
		cDtHora     := dTos(dData)+strtran(IncTime(time() ,nInc,0,0 ),":")
	EndIf


Return cDtHora

/*/{Protheus.doc} AcordTit
Verifica se titulo participa de algum Acordo na RECEIV
@type  Function
<AUTHOR> Leite
@since 15/09/2021
@version 1.0
 */
user function AcordTit(cFiltit, cNum, cPref, cParc, cTipo, cCliente)
Local aArea     := GetArea()
Local cP39Alias := GetNextAlias()
Local cRet      := ""

If !IsInCallStack("U_TIRCV016")  
	BeginSql Alias cP39Alias
		SELECT P39.P39_IDACOR
		FROM %TABLE:P39% P39 INNER JOIN %TABLE:P38% P38  ON 
		P39.P39_FILACD = P38.P38_FILIAL AND
		P39.P39_IDACOR = P38.P38_NUM AND
		P38.P38_TIPO   = 'A'  AND P38.P38_CLIENT = %Exp:cCliente%  
		WHERE 
		    P39.P39_FILIAL   = %Exp:cFiltit%
		AND P39.P39_NUM      = %Exp:cNum%
		AND P39.P39_PREFIX   = %Exp:cPref%
		AND P39.P39_PARCEL   = %Exp:cParc%
		AND P39.P39_TIPO     = %Exp:cTipo%
		AND P39.%NOTDEL% 
		AND (P39.P39_QUEBRA = ' ' OR P39.P39_QUEBRA = '2')
		AND P38.%NOTDEL%
	EndSQL
	
	If (cP39Alias)->(!Eof())
		cRet  := (cP39Alias)->(P39_IDACOR)
	Endif
	
	(cP39Alias)->(DbCloseArea())
EndIf

restArea(aArea)
return cRet 

/*/{Protheus.doc} AcordPror
Verifica se titulo participa de algum Acordo/Parcela na RECEIV de prorrogação 
**Observação: este tipo de acordo devera ter sempre 1 unica parcela!

@type  Function
<AUTHOR> Leite
@since 15/09/2021
@version 1.0
 */
user function AcordPror(cFiltit, cNum, cPref, cParc, cTipo, cCliente)
Local aArea     := GetArea()
Local cP39Alias := GetNextAlias()
Local aRet      := {'',''}

BeginSql Alias cP39Alias
	SELECT P39.P39_IDACOR, P38.P38_PARCEL  
	FROM %TABLE:P39% P39 INNER JOIN %TABLE:P38% P38  ON 
	P39.P39_FILACD = P38.P38_FILIAL AND
	P39.P39_IDACOR = P38.P38_NUM AND
	P38.P38_TIPO   = 'P'  AND P38.P38_CLIENT = %Exp:cCliente%  
	WHERE 
	    P39.P39_FILIAL   = %Exp:cFiltit%
	AND P39.P39_NUM      = %Exp:cNum%
	AND P39.P39_PREFIX   = %Exp:cPref%
	AND P39.P39_PARCEL   = %Exp:cParc%
	AND P39.P39_TIPO     = %Exp:cTipo%
	AND P39.%NOTDEL% 
	AND (P39.P39_QUEBRA = ' ' OR P39.P39_QUEBRA = '2')
	AND P38.%NOTDEL%
	ORDER BY P39.P39_PARCEL DESC //acordo de prorrogacao tem sempre 1 unica parcela, por segurança obtemos a ultima
EndSQL

If (cP39Alias)->(!Eof())
	aRet  := {(cP39Alias)->(P39_IDACOR),(cP39Alias)->(P38_PARCEL)}
Endif

(cP39Alias)->(DbCloseArea())
restArea(aArea)
return aRet 




/*/{Protheus.doc} RCVLOG
  Grava o log das integrações
// aChave[x,1] - Chave Financeira
// aChave[x,2] - Stamp da Tabela
@type  Function
<AUTHOR> Madureira
@since 24/11/2021
@version 1.0
 */
User Function RCVLOG( aChave, cOrigem, lJob2 )
Local nA, dData, cHora

Default cOrigem := " "
Default lJob2   := .F.

DbSelectArea("P48")
P48->(DbSetOrder(1))

For nA := 1 to Len(aChave)

	dData := sTod(Left( aChave[nA,2] ,8))
    cHora := Left(Right(aChave[nA,2] ,6),2)+":"+Substr(aChave[nA,2],11,2)+":"+Right(aChave[nA,2],2)

	RecLock("P48", .T.)
	P48->P48_FILIAL := xFilial("P48")
    P48->P48_DATA   := dData
    P48->P48_HORA   := cHora
    P48->P48_COD    := P37->P37_COD
    P48->P48_KEYFIN := aChave[nA,1]
    P48->P48_STATUS := P37->P37_STATUS
    P48->P48_FILP37 := P37->P37_FILIAL
	P48->P48_CODAPI := P37->P37_CODAPI
	P48->P48_ORIGEM := cOrigem
	P48->P48_ENVIO  := 1
	If lJob2 
	  P48->P48_OBS    := "B"
	else
	  P48->P48_OBS    := "A"
	EndIf
	P48->(MsUnLock())
	
Next

Return






/*/{Protheus.doc} JaIntegrou
	Verifica se a ultima integração dessa chave já foi concluída, caso não, ele não integra, 
	e o job incremental irá integrar qdo concluir 
	@type Function
	<AUTHOR> Madureira
	@since 29/12/2021
	@version 1.0
/*/
USER Function LoadingI( cChave, cStamp, cCodApi )
	Local lRetVal := .F.
	Local cAlsP48 := GetNextAlias()
	Local cqry    := " "

	cqry += " SELECT P48_COD FROM " + RETSQLNAME("P48") + " WHERE "
	cqry += " P48_FILIAL = '" + xfilial("P48") + "' "
	cqry += " AND P48_CODAPI = '"+ cCodApi +"' "
	cqry += " AND P48_KEYFIN = '"+ cChave  +"' "
	cqry += " AND P48_DATA||REPLACE(P48_HORA,':') = '" + cStamp + "' "
	//cqry += " AND P48_STATUS = '0' "
	cqry += " AND D_E_L_E_T_ = ' ' "

	DbUseArea(.T., "TOPCONN", TcGenQry(,,cqry),cAlsP48,.T.,.T.)

	If !Empty( (cAlsP48)->P48_COD )
		lRetVal := .T.
	EndIf

	(cAlsP48)->(DbCloseArea())

Return lRetVal










/*/{Protheus.doc} User Function TIRCV028
	Valida integrações entre baixas e titulos
	1. Verifica se enviou baixa e não criou requisição de titulo, localiza na p48 as baixas dessa requisição, para reenviá-las
    2. Verifica se retorno de baixas está ok, mas não mudou status de titulos ( muda status do titulo pra 0) 
    3. Verifica se baixas ficou com erro   ( envia baixas novamente )
    4. Verifica se retorno de baixas está ok, mas e titulos está com erro ( muda status do titulo pra 0)

	@type  Function
	<AUTHOR> Madureira
	@since 30/12/2021
	@version 1.0

/*/
User Function TIRCV028()
	Local cAlsP37 := GetNextAlias()
	Local cqry    := " "
	Local lBxErro5:=.F.
	IF U_RCVENV( cEmpAnt,cFilAnt)

		cqry += " SELECT B.P37_COD, B.P37_STATUS STATBXA, T.P37_STATUS STATTIT, B.R_E_C_N_O_ RECBXA, T.R_E_C_N_O_ RECTIT "
		cqry += " FROM " + RETSQLNAME("P37") + " B "
		cqry += " LEFT JOIN " + RETSQLNAME("P37") + " T ON "
		cqry += " B.P37_COD = T.P37_CODREQ "
		cqry += " AND T.P37_CODAPI = '000028' "
		cqry += " AND T.D_E_L_E_T_ =' ' "
		cqry += " WHERE B.P37_CODAPI IN('000006','000033') AND B.P37_STATUS IN('4','5') "
		cqry += " AND COALESCE(T.P37_STATUS, ' ' ) IN(' ', '5', '9') "
		cqry += " AND B.D_E_L_E_T_ =' ' "
		DbUseArea(.T., "TOPCONN", TcGenQry(,,cqry),cAlsP37,.T.,.T.)
		lBxErro5:=GetMv("TI_RCV28B", .F.,.F.)	//Descarta baixas com erro 5
		While !(cAlsP37)->(EOF())

			Do Case

				// Baixa retornou erro, então já atualiza o status pra reenviar
			Case (cAlsP37)->STATBXA = "5"
				P37->(DBGOTO( (cAlsP37)->RECBXA ))
				If lBxErro5
					If Reclock("P37",.F.)
						P37->P37_STATUS := "0"
						P37->(MsUnLock())
					EndIf
				EndIF


				// Não criou titulos, reenvia baixas pra entrar no processo novamente
			Case (cAlsP37)->STATBXA = "4" .and. Empty( (cAlsP37)->RECTIT )

				P37->(DBGOTO( (cAlsP37)->RECBXA ))
				RecLock("P37",.F.)
				P37->(DbDelete())
				P37->(MsUnLock())

				AtualSE5( (cAlsP37)->P37_COD )

				// Baixa retornou ok, mas não o titulo não, então reenvia titulo
			Case (cAlsP37)->STATBXA = "4" .and. (cAlsP37)->STATTIT $ "5:9"
				P37->(DBGOTO( (cAlsP37)->RECTIT ))
				If Reclock("P37",.F.)
					P37->P37_STATUS := "0"
					P37->(MsUnLock())
				EndIf

			EndCase

			(cAlsP37)->(DbSkip())
		End

		(cAlsP37)->(DbCloseArea())
	EndIF
Return




/*/{Protheus.doc} User Function BuscaBaixa
	Busca quais baixas pertencem a requisição para atualizar e enviar novamente
	@type  Function
	<AUTHOR> Madureira
	@since 30/12/2021
	@version 1.0

/*/
Static Function AtualSE5( cReqBaixa )
	Local cAlsP48   := GetNextAlias()
	Local cQry      := " "
	Local cAtuStamp := " "

	cQry := " SELECT R_E_C_N_O_ RECP48, SUBSTR(P48_KEYFIN,7,9) TITULO, SUBSTR(P48_KEYFIN,1,6) CLIENTE, SUBSTR(P48_KEYFIN,25,11) FILIAL, "
	cQry += " SUBSTR(P48_KEYFIN,19,3) PREFIXO,  SUBSTR(P48_KEYFIN,16,3) PARCELA
	cQry += " FROM " + RETSQLNAME("P48")  + " WHERE "
	cQry += "     P48_FILIAL = '" + xFilial("P48") + "' "
	cQry += " AND P48_COD    = '" + cReqBaixa      + "' "
	cQry += " AND P48_CODAPI = '000006' "
	cQry += " AND D_E_L_E_T_ =' ' "
	DbUseArea(.T., "TOPCONN", TcGenQry(,,cQry),cAlsP48,.T.,.T.)

	While !(cAlsP48)->(EOF())

		// Somente pra atualizar o S_T_A_M_P_ e ser enviado novamente pra receiv
		cAtuStamp := " UPDATE " + RETSQLNAME("SE5") + " SET E5_BANCO = E5_BANCO WHERE "
		cAtuStamp += " E5_NUMERO  = '" + (cAlsP48)->TITULO   + "' AND "
		cAtuStamp += " E5_CLIENTE = '" + (cAlsP48)->CLIENTE  + "' AND "
		cAtuStamp += " E5_FILORIG = '" + (cAlsP48)->FILIAL   + "' AND "
		cAtuStamp += " E5_PREFIXO = '" + (cAlsP48)->PREFIXO  + "' AND "
		cAtuStamp += " E5_PARCELA = '" + (cAlsP48)->PARCELA  + "' AND "
		cAtuStamp += " D_E_L_E_T_ =' ' "
		TcSqlExec(cAtuStamp)

		P48->(DBGOTO( (cAlsP48)->RECP48 ))
		RecLock("P48",.F.)
		P48->(DbDelete())
		P48->(MsUnLock())

		(cAlsP48)->(DbSkip())
	End

	(cAlsP48)->(DbCloseArea())

Return

 /*/{Protheus.doc} Funcao para formatacao do STAMP para Receiv no formado 
	AAAAMMDD HH:MM:SS
	@type  Function
	<AUTHOR>
	@since 03/02/2022
	@version version
	@param param_name, param_type, param_descr
	@return return_var, return_type, return_description
	@example
	(examples)
	@see (links_or_references)
	/*/
User Function MaskStmp(cStamp)
	Local cStmp:=""
	If(!Empty(Alltrim(cStamp)))
		cStmp:=SubStr(cStamp,1,8)+ " " +  SubStr(cStamp,9,2)+":"+  SubStr(cStamp,11,2)+":"+SubStr(cStamp,13,2)
	Else
		cStmp:=	dTos(Date()) + " " +  IncTime(time() ,3,0,0 )
	EndIF

Return cStmp

 /*/{Protheus.doc} Funcao para refornar o codigo do Credor nas API`s
	@type  Function
	<AUTHOR> Menabue Lima
	@since 18/02/2022
	@version 1.0

	/*/
User Function  RCVCRDOR()
	Local nCredor:= GetMv("TI_RCVCRED" , .F.,0000)


Return nCredor


/*/{Protheus.doc} User Function RCVENV
	Funcao Responsavel por ambrir o ambiente 
	@type  Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
	/*/

User Function  RCVENV( cEmp,cFil,nOpen)

	Local lAmbOk := .T.
	Default cEmp :='00'
	Default cFil :='00001000100'
	Default nOpen:= 0
	if(nOpen==0)
		If Select("SM0") == 0
			RpcSetType(3)
			If !RpcSetEnv(cEmp, cFil)
				Final("Erro ao abrir ambiente")
				lAmbOk := .F.
			EndIf
		EndIf
	Else
		RpcClearEnv()
		RpcSetType(3)
		If !RpcSetEnv(cEmp, cFil)
			Final("Erro ao abrir ambiente")
			lAmbOk := .F.
		EndIf
	EndIF
	FWMonitorMSG("***** ABRINDO AMBIENTE ***** "+ cEmpAnt+"/"+cFilant )


Return lAmbOk

 /*/{Protheus.doc} Funcao para distribuir proporcionalmente a quantidade de registros a serem processados entre threads
	@type  Function
	<AUTHOR> Pinheiro
	@since 29/03/2023
	@version 1.0
	/*/
User Function SliceThr( nQtdReg, nQtdThread, nQuebra, lShowLog )

	Local aLinhas            := { }
	Local n1QtdRegsPorThread := 0
	Local n2QtdAux           := 0
	Local n3QtdRegSobraram   := 0
	Local nLinIni            := 1
	Local nLinFim            := 0
	Local nY                 := 1

	Local cMsg
	Local aMessage

	Default lShowLog         := .F.

	If nQtdReg < nQtdThread
		nQtdThread := 1
	EndIf

	If nQtdReg >= nQuebra
		n1QtdRegsPorThread := NoRound( nQtdReg / nQtdThread, 0)
		n2QtdAux := n1QtdRegsPorThread * nQtdThread
		n3QtdRegSobraram := nQtdReg - n2QtdAux
	Else
		nQTdThread := 1
		n1QtdRegsPorThread := nQtdReg
	EndIF

	If n1QtdRegsPorThread == 0
		nQtdThread := 1
	EndIf

	If lShowLog
		aMessage := {}
		cMsg     := "--- Registros: " + cValToChar( nQtdReg )
		aadd( aMessage, {"",cMsg} )

		if( n3QtdRegSobraram > 0 )
			cMsg := "--- " + cvaltochar( n3QtdRegSobraram ) + " threads com " + cValToChar( n1QtdRegsPorThread + 1 ) + " registros - " + " Threads: " + cValToChar(nY) + " - " + cValToChar(n3QtdRegSobraram)
			aadd( aMessage, {"",cMsg} )
			cMsg := "--- " + cvaltochar( nQtdThread - n3QtdRegSobraram ) + " threads com " + cValToChar( n1QtdRegsPorThread ) + " registros - " + " Threads: " + cValToChar(n3QtdRegSobraram+1 ) + " - " + cValToChar(nQtdThread)
			aadd( aMessage, {"",cMsg} )
		Else
			cMsg := "--- " + cvaltochar( nQtdThread ) + " threads com " + cValToChar( n1QtdRegsPorThread ) + " registros - " + " Threads: " + cValToChar(n3QtdRegSobraram+1 ) + " - " + cValToChar(nQtdThread)
			aadd( aMessage, {"",cMsg} )
		EndIf
	EndIf

	for nY := 1 to nQtdThread

		If nY <= n3QtdRegSobraram
			nLinFim += ( n1QtdRegsPorThread + 1 )
		Else
			nLinFim += n1QtdRegsPorThread
		EndIF

		aadd( aLinhas, {nLinIni, nLinFim })

		If lShowLog
			cMsg := "--- Lap: "  + cValToChar( nY ) + " ini: " + cValToChar(aLinhas[nY,1]) + " fim: " + cValToChar(aLinhas[nY,2])
			aadd( aMessage, {"",cMsg} )
		EndIf

		nLinIni :=  nLinFim + 1

	next nY

	IIf(lShowLog, FWLogMsg("INFO", /*cTransactionId*/, "RECEIV", /*cCategory*/, /*cStep*/, /*cMsgId*/,"" , /*nMensure*/, /*nElapseTime*/, aMessage), "")

Return aLinhas

/*/{Protheus.doc} nomeFunction
    (long_description)
    @type  Function
    <AUTHOR>
    @since date
    @version version
    @param param, param_type, param_descr
    @return return, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TIRCVM3A(cEmpAnt, cFilAnt, cTabTmpSE1, nLinIni, nLinFim, nQuebra)

	Local cAlTrb		:= CriaTrab(Nil, .f.)
	Local cQry 			:= ''
	Local cJson := ''
	Local aCtr      := {}

	Default cEmpAnt := ''
	Default cFilAnt := ''
	Default cTabTmpSE1 := ''
	Default nLinIni := 0
	Default nLinFim := 0

	If Select("SM0") == 0
		RpcClearEnv()
		RpcSetType(3)
		RpcSetEnv(cEmpAnt, cFilAnt)
	EndIf

	cQry := "SELECT * FROM "+cTabTmpSE1+"  WHERE RECLRT BETWEEN "+cvaltochar(nLinIni)+" AND "+cvaltochar(nLinFim)

	If Select(cAlTrb) > 0
		(cAlTrb)->(dbCloseArea())
	Endif

	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlTrb,.T.,.T.)

	cJson  :=''
	nLinha := 0
	aJson  :={}
	aCtr   := {}
	While !(cAlTrb)->(EOF())

		nLinha ++

		Aadd(aJson,JsonObject():new())
		nPos := Len(aJson)

		aAdd(aCtr,{ Alltrim((cAlTrb)->E1_FILIAL)	,;
			Alltrim((cAlTrb)->E1_TIPO)		,;
			Alltrim( (cAlTrb)->E1_NUM)		,;
			Alltrim((cAlTrb)->E1_PREFIXO)	,;
			(cAlTrb)->E1_PARCELA			,;
			Alltrim((cAlTrb)->E1_CLIENTE)	,;
			Alltrim((cAlTrb)->E1_LOJA)})

		IF nLinha % nQuebra == 0
			If !Empty(aCtr)
				setCtr(aCtr, P37->P37_COD)
				aCtr:={}
			EndIf
		EndIf

		(cAlTrb)->(DbSkip())
	EnDdo

	IF nLinha % nQuebra <> 0
		If !Empty(aCtr)
			setCtr(aCtr, P37->P37_COD)
			aCtr:={}
		EndIf
	EndIf
	(cAlTrb)->(dbCloseArea())

Return Nil

/*/{Protheus.doc} setCtr
	Grava na P37 a requisicao interna para a API de Acordos
	@type Function
	<AUTHOR> Menabue Lima
	@since 27/09/2021
	@version 1.0
/*/
Static Function setCtr(aCtr,cReq)
	Local cBody  := ""
	Local cUrl   := ""
	Local nI     := 0
	Local cCod   := ""
	Local aJsonT := {}

	For nI:= 1 to Len(aCtr)

		Aadd(aJsonT,JsonObject():new())
		nPos := Len(aJsonT)
		aJsonT[nPos]["filial"] 	:=aCtr[nI][1]
		aJsonT[nPos]["tipo"] 	:=aCtr[nI][2]
		aJsonT[nPos]["titulo"] 	:=aCtr[nI][3]
		aJsonT[nPos]["prefixo"] 	:=aCtr[nI][4]
		aJsonT[nPos]["parcela"] 	:=aCtr[nI][5]
		aJsonT[nPos]["cliente"] 	:=aCtr[nI][6]
		aJsonT[nPos]["loja"] 	:=aCtr[nI][7]
		//aJson[nPos]["codreq"  ] :=cReq

		oJson := JsonObject():new()
		oJson:set(aJsonT[nPos])
		cBody += oJson:toJSON()
		cBody+= ','
		If ValType( oJSon ) == "O"
			FreeObj( oJSon )
			oJSon := NIL
		EndIf
	Next nI
	cBody := '{ "titulos": ['+SubStr(cBody,1,Len(cBody)-1)+'], "requisicao": "'+cReq+'" }'
	cBody := EncodeUtf8(cBody)

	If P36->(DbSeek(XFilial("P36")+"000026"))
		cUrl := P36->P36_URL
	EndIf

	cCod := GETSXENUM("P37","P37_COD","P37_COD" + CFILANT)

	Reclock("P37",.T.)
	P37->P37_FILIAL:= xFilial("P37")
	P37->P37_COD   := cCod
	P37->P37_DATA  := DDATABASE
	P37->P37_HORA  := TIME()
	P37->P37_URL   := cUrl
	P37->P37_BODY  := cBody
	P37->P37_METHOD:= "POST"
	P37->P37_CODAPI:= "000027"
	P37->P37_STATUS:= "0"
	P37->P37_CALLBA:= "U_TIRCV015"
	P37->P37_TIPORE:= "2"
	P37->P37_ASYNC1:= "N"
	P37->P37_CODREQ:= cReq
	P37->(MsUnLock())

	ConfirmSX8()

Return

 /*/{Protheus.doc} Funcao auxiliar para apresentar a tela de loading nos menus da Receiv
	@type  Function
	<AUTHOR> Pinheiro
	@since 05/04/2023
	@params
	cFunction - função a ser executada ex: U_TIRCVM3A ( sem parenteses )
	cTrbAre - tabela que será utilizada pela thread do startjob
	aLinhas - Array com os registros iniciais e finais que serão utilizados na distribuição entre as threads
	nQtdReg - Quantidade de registros a serem processados
	nQtdThread - Quantidade de threads utilizadas
	nQuebra - Quantidade de registros do lote json
	@version 1.0
	/*/
User Function TIRCX00B( cFunction, cTrbAre, aLinhas, nQtdReg, nQtdThread, nQuebra  )

	If IsBlind()
		IniciaJob( cFunction, cTrbAre, aLinhas, nQtdReg, nQtdThread, nQuebra )
	Else
		Processa({|| IniciaJob( cFunction, cTrbAre, aLinhas, nQtdReg, nQtdThread, nQuebra )}, "Processando registros...",,.F.)
	EndIf

Return

static Function IniciaJob( cFunction, cTrbAre, aLinhas, nQtdReg, nQtdThread, nQuebra )

	Local nY := 0
	Local nLinIni := 0
	Local nLinFim := 0
	Local lTela :=  !IsBlind()

	IIF( lTela, ProcRegua( nQtdThread - 1 ),"")

	For nY := 1 to nQtdThread
		nLinIni := aLinhas[nY,1]
		nLinFim := aLinhas[nY,2]
		StartJob( cFunction , GetEnvServer(), .F., cEmpAnt, cFilAnt, cTrbAre, nLinIni, nLinFim, nQuebra)
		IF lTela
			IncProc("Processando " + cValToChar(nLinFim) + " de " + cValToChar(nQtdReg) + "...")
			sleep(500)
		EndIf
	Next

return


/*/{Protheus.doc} User Function TIRCVEMP
	Retorna a Empresa da Filial selecionada
	@type  Function
	<AUTHOR> Menabue Lima
	@since 23/08/2023
	@version 12.1.33
	/*/
User Function TIRCVEMP(_cFil)
	Local  aSmoFull :=FWLoadSM0( .F. , .T. )
	Local nPoFil	:=  Ascan(aSmoFull,{ |x| x[2] == _cFil })

Return aSmoFull[nPoFil][1]


/*/{Protheus.doc} User Function TIRCX10A
	TIBACKOP-2617, Identificacao do Acordo referente ao respectivo nosso nmumero e titulo
	@type  Function
	<AUTHOR> Leite
	@since 27/03/2025
	@version 12.1.33
	/*/
User Function TIRCX10A(cNsNum, cOcorr, dDataCred, cBanco, cAgencia, cConta, dDataBX, nJuros, cIdCnab)
	Local cChvSe1   := ""
	Local lReceiv   := .F.
	local cNum      := PadL(Substring(cNsNum,4,8),8,'0')
	local lCarimba  := .F.
	
	DbSelectArea('P38')
	P38->(DbSetOrder(2))	 

	P38->(MsSeek(cNum))
	While !P38->(Eof()) .And. AllTrim(P38->P38_NUMBCO) == cNum
		If Left(P38->P38_FILIAL, Len(AllTrim(SEE->EE_FILIAL))) == AllTrim(SEE->EE_FILIAL)
			lCarimba := .T.		
			Exit
		EndIf
		P38->(DbSkip())
	End
	
	If P38->(!Eof())  .And. P38->P38_TIPO <> 'P' .And. P38->P38_QUEBRA <> '1'  .and. alltrim(cOcorr) $ GetMv('RC_OCORBX', , '06|07|08|09|10|11|36|37|38|39') .and. lCarimba  

		If Empty(cIdCnab) //se não tem idcnab então é de acordo Receiv e precisa carimbar dados bancários sempre
        	lReceiv := .T. 
			If !Empty(SEE->EE_CTAOFI)
				cBanco		:= SEE->EE_CODOFI
				cAgencia	:= SEE->EE_AGEOFI
				cConta		:= SEE->EE_CTAOFI
			Endif
		
			RecLock("P38", .F.)
			P38->P38_BANCO  := cBanco
			P38->P38_AGENCI := cAgencia
			P38->P38_CONTA  := cConta
			P38->P38_OCORRE := cOcorr
			P38->P38_DTCRED := dDataBX
			P38->P38_VLJURO	:= nJuros
			P38->(MsUnLock())
		Else  
			dbselectarea('SE1')
			SE1->(dbSetOrder(19))// e1_idcnab
			SE1->(MsSeek(Substr(cIdCnab,1,10)))

			If SE1->(!Eof()) .And. AllTrim(StrTran(SE1->E1_NUMBCO,"-","")) == AllTrim(cNsNum)
				cChvSe1 := SE1->E1_FILIAL + SE1->E1_PREFIXO + SE1->E1_NUM + SE1->E1_PARCELA + SE1->E1_TIPO
						
				DbSelectArea('P39')
				P39->(DbSetOrder(1))
				P39->(DbSeek( cChvSe1 + P38->P38_NUM )) 
            	If P39->(!Eof()) 
					lReceiv := .T. 
							
					If !Empty(SEE->EE_CTAOFI)
						cBanco		:= SEE->EE_CODOFI
						cAgencia	:= SEE->EE_AGEOFI
						cConta		:= SEE->EE_CTAOFI
					Endif
			
					RecLock("P38", .F.)
					P38->P38_BANCO  := cBanco
					P38->P38_AGENCI := cAgencia
					P38->P38_CONTA  := cConta
					P38->P38_OCORRE := cOcorr
					P38->P38_DTCRED := dDataBX
					P38->P38_VLJURO	:= nJuros
					P38->(MsUnLock())
            	EndIf 
        	EndIf 
		EndIf
	EndIf 

Return lReceiv
