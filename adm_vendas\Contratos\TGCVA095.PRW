#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"

/*
U_GCVA095S()                       // Rotina que solicita contrato e pelo modelo carrega a tela de simulação
U_GCVA095V()                       // Rotina que solicita a competencia e o contrato  para a geração do cronograma financeiro
U_GCVA095A(cContrato, aAMSave)     // Rotina para processar "Inicio do mes de contratos" informando o contrato ?????
U_GCVA095L(cContrato, cAMProc, cMsgErro, cIdProc , lItensVenc, lCancelado, lCarenBoni)  // Registro de Log inicio
U_GCVA095R(cContrato, aAMSave)     // Rotina para recalcular o cronograma financeiro
U_GCVA095X()                       // Rotina para apaga a revisão atual e ativa revisão anterior
*/

/*
===========================================================================================================
==   ROTINA PARA PROCESAR POR CONTRATO 
===========================================================================================================
*/

User Function GCVA095S(cContrato, cRevisa, oModelCtr, cCmpIni, cCmpFim)
    Local oContra
    Local lErro    := .F.
    Local cMsgErro := ""
    Local aPar     := {}
    Local lSai     := IsInCallStack("U_TGC001CA")
    Local aArea    := GetArea()
    Local aAreaSB1 := SB1->(GetArea())
    Default cContrato := ""
    Default cRevisa   := ""
    Default oModelCtr := nil
   
    Private lAbortPrint := .F.

    If oModelCtr <> NIL .and. ( Empty(cContrato) .or. Empty(cRevisa))
        cContrato := oModelCtr:GetModel("CN9MASTER"):GetValue("CN9_NUMERO")
        cRevisa	:= oModelCtr:GetModel("CNBDETAIL"):GetValue("CNB_REVISA")
    EndIf
    
    If Empty(cContrato)
        aPar := PegaPar()
        cContrato := aPar[1]
        cCmpFim   := aPar[2]
        If Empty(cContrato)
            Return 
        EndIf
		If Left(cContrato, 3)  <> "CON"
			cContrato := Padr("CON" + cContrato, 15)
		EndIf
    EndIf


    If Empty(cRevisa)
        cRevisa := LoadCN9(cContrato)
    EndIf
     
    If oModelCtr == NIL
        Processa({|| oModelCtr := CarregaModelo(cContrato, cRevisa, @lErro)},,,.T.)     
        If lErro
            RestArea(aAreaSB1)
            RestArea(aArea)
            Return 
        EndIf
    EndIf
    U_GV002CObj()
    If IsBlind()
        oContra := ProcContrato(cContrato, cRevisa, oModelCtr, @cMsgErro, cCmpIni, cCmpFim)
        U_GV002RCTR(oContra)
        If ! Empty(cMsgErro)
            Help(,,"GCVA095",,cMsgErro,)
        EndIF
    Else
        Processa({|| oContra := ProcContrato(cContrato, cRevisa, oModelCtr, @cMsgErro, cCmpIni, cCmpFim)},,, lSai) 
        U_GV002RCTR(oContra)
        If ! Empty(cMsgErro)
            oModelCtr:SetErrorMessage("","","","","TGCVA095", cMsgErro,"")
        Else
            If Empty(oContra:aAMAtu)
                MsgInfo("Calculo do cronograma financeiro sem alterações!!!")
            Else
                U_GCVC014A(cContrato, cRevisa)  /// atualizar o folder do cronograma financeiro
            EndIf
        EndIf
    EndIf

    

    RestArea(aAreaSB1)
    RestArea(aArea)

Return Empty(cMsgErro)

Static Function CarregaModelo(cContrato, cRevisa, lErro)
    Local oModelCtr
    
    Private INCLUI := .F.
    Private ALTERA := .T.
    CN9->(DBSetOrder(1))
    CN9->(DBSeek(xFilial("CN9") + cContrato + cRevisa))

    ProcRegua(1)


    IncProc("Carregando Modelo -  Contrato [" + cContrato  + "] Revisão [" + cRevisa + "]" )
    ProcessMessage()
    U_A3SATpRv(U_Cn3RetSt("TIPREV"))
	U_GV002S0S("")
    
    oModelCtr := FWLoadModel("TGCVA002")
    oModelCtr:SetOperation(MODEL_OPERATION_UPDATE)
    If ! oModelCtr:Activate() 
        lErro := .T.
        Alert("Não foi possivel ativar o modelo")    
    EndIf

Return oModelCtr

Static Function PegaPar()
    Local aParamBox := {}
    Local cContrato := Space(LEN(CN9->CN9_NUMERO))
    Local cCmpAte   := AMtoCmp(Left(Dtos(Date()), 6))
    Local cAMProc   := ""
    Local aRet      := {}
    Local cTitulo := "Simulação - Cronograma Financeiro"
    Local lCanSave  := .F.

    MV_PAR01:=""

    aAdd(aParamBox,{1,"Contrato",	cContrato, "@!", "NaoVazio()",,, 50, .T.})
    aAdd(aParamBox,{1,"Até a Competencia"        , cCmpAte, "99/9999", "NaoVazio()",,, 30, .T.})
    
    If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
        Return {"",""}
    EndIf
    cContrato := Padr(aRet[1], LEN(CN9->CN9_NUMERO))
    cAMProc   := CmptoAM(Alltrim(aRet[2]))
       

    If cAMProc < Left(Dtos(Date()), 6)
        cAMProc :=Left(Dtos(Date()), 6)
        MsgAlert("Competencia anexada para "  + AMtoCmp(cAMProc))
    EndIf
    

Return {cContrato, AMtoCmp(cAMProc)}

Static Function LoadCN9(cContrato, nRecAtu, nRecAnt)
    Local aAreaCN9 := CN9->(GetArea("CN9"))
    Local cChave   := xFilial("CN9") + cContrato
    Local cRevisa  := ""
    Default nRecAtu := 0
    Default nRecAnt := 0

    CN9->(DbSetOrder(1))
    CN9->(DbSeek(cChave))
    While CN9->(! Eof() .and. CN9_FILIAL + CN9_NUMERO == cChave )
        cRevisa := CN9->CN9_REVISA
        If CN9->CN9_SITUAC == "05" 
            nRecAtu := CN9->(Recno())
            Exit
        EndIf
        nRecAnt := CN9->(Recno())
        CN9->(DbSkip())
    End
    RestArea(aAreaCN9)

Return cRevisa

Static Function ProcContrato(cContrato, cRevisa, oModelCtr, cMsgErro, cCmpIni, cCmpFim)
    Local oContra

    ProcRegua(1)

    oContra:= TGCVXC10():New(cContrato, cRevisa, cCmpIni, cCmpFim)
    oContra:Cria()
    oContra:LoadModel(oModelCtr)   // Carrega ph5 do modelo para classe contrato
    oContra:Calc()                 // Faz o calculo    
    cMsgErro := oContra:cMsgErro

    If lAbortPrint
        cMsgErro := "Simulação interrompida pelo usuario!!!"
    EndIf
    U_GV002SVC(Empty(cMsgErro)) // libera a rotina o modelo para atualização
    
    
Return oContra

/*
###############################################################################################################################
*/


User Function GCVA095V()
    Local cAMProc    := ""
    Local cContrato  := ""
    Local cFile      := ""
    Local lItensVenc := .T.
    Local lCancelado := .T.
    Local lCarenBoni := .T.
    Local lDownGrade := .T.
    
    Private lAbortPrint := .F.

    If ! PegaParams(@cAMProc, @cContrato, @lItensVenc, @lCancelado, @lCarenBoni, @lDownGrade)
        Return
    EndIf

    cFile := MyGetFile("Arquivos Log (*.log) |*.log|","Informe o arquivo", 0, "C:\", .F., GETF_LOCALHARD + GETF_LOCALFLOPPY + GETF_NETWORKDRIVE)
    If Empty(cFile)
        Return .F.
    Endif

    If ! Lower(Right(cFile, 4)) == ".log"
        cFile += ".log"
    EndIf
    If File(cFile) 
        If ! MsgYesNo("Confirma a substituição do arquivo?")
            Return .F.
        EndIf
        FErase(cFile)
    EndIF
    
    If ! MsgYesNo("Confirma a geração do cronograma Financeiro?")
        Return
    EndIf

    Processa({|| GeraCro(cContrato, cAMProc, cFile, lItensVenc, lCancelado, lCarenBoni, lDownGrade)} ,,, .T.) 

    If lAbortPrint
        MsgAlert("Calculo  interrompida!")
    Else
        MsgAlert("Calculo Finalizado!")
        
        AutoGrLog("")
        FErase(NomeAutoLog())
        AutoGrLog(MemoRead(cFile))
        MostraErro()
    EndIf

Return 

Static Function MyGetFile(cMascara, cTitulo, nMascpadrao, cDirinicial, lSalvar, nOpcoes, lArvore, lKeepCase)
    Local cRet := ""
    Local cGF  := "cGetFile"

    cRet := &(cGF)(cMascara, cTitulo, nMascpadrao, cDirinicial, lSalvar, nOpcoes, lArvore, lKeepCase)

Return cRet


Static Function PegaParams(cAMProc, cContrato, lItensVenc, lCancelado, lCarenBoni, lDownGrade)
    Local aParamBox := {}
    Local aRet      := {}
    Local aCombo    := {"Não","Sim"}
    Local lCanSave  := .F.
    
    Local cTitulo := "Cronograma Financeiro"
    
    MV_PAR01:=""
    MV_PAR02:=""
    MV_PAR03:=""
    MV_PAR04:=""
    MV_PAR05:=""
    MV_PAR06:=""


    aAdd(aParamBox,{1,"Competencia"         , Space(TamSx3("PH6_COMPET")[1]), "99/9999", "NaoVazio()",,, 30, .T.})
    aAdd(aParamBox,{1,"Contrato"            , Space(TamSx3("PH6_CONTRA")[1]), "@!"     , "NaoVazio()",,, 50, .T.})
    aAdd(aParamBox,{2,"Itens Vencidos"      , "Não" , aCombo , 50 ,"" ,.F.})
    aAdd(aParamBox,{2,"Cancelados"          , "Não" , aCombo , 50 ,"" ,.F.})
    aAdd(aParamBox,{2,"Bonificação/Carencia", "Não" , aCombo , 50 ,"" ,.F.})
    aAdd(aParamBox,{2,"DownGrade"           , "Não" , aCombo , 50 ,"" ,.F.})

    If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
        Return .F.
    EndIf
    
    cAMProc   := CmptoAM(Alltrim(MV_PAR01))
    cContrato := Padr(Alltrim(MV_PAR02), TamSx3("PH6_CONTRA")[1])
	If Left(cContrato, 3)  <> "CON"
		cContrato := Padr("CON" + cContrato, 15)
	EndIf
    lItensVenc := Left(MV_PAR03, 1) == "S"
    lCancelado := Left(MV_PAR04, 1) == "S"
    lCarenBoni := Left(MV_PAR05, 1) == "S"
    lDownGrade := Left(MV_PAR06, 1) == "S"

Return .t.



Static Function GeraCro(cContrato, cAMProc, cFile, lItensVenc, lCancelado, lCarenBoni, lDownGrade)
    Local cMsgErro := ""
    Local cMsgRet  := ""
    Local cIdProc  := ""

    GrvArq(cFile, "Geração do cronograma financeiro - " + Dtoc(Date()) + " " + Time() )
    GrvArq(cFile, "Competencia: " + AMtoCmp(cAMProc))
    GrvArq(cFile, "Contrato: " + cContrato)

    
    U_GCVA095A(cContrato, {cAMProc}, @cMsgErro, cIdProc, lItensVenc, lCancelado, lCarenBoni, lDownGrade, @cMsgRet ) 
    

    GrvArq(cFile, cMsgRet)
    If ! Empty(cMsgErro) 
        GrvArq(cFile, " ")
        GrvArq(cFile, cMsgErro)
        GrvArq(cFile, " ")
    EndIf
    GrvArq(cFile, "Geração finalizada - " + Dtoc(Date()) + " " + Time() )
Return 

/*
###############################################################################################################################
*/


User Function GCVA095A(cContrato, aAMSave, cMsgErro, cIdProc, lItensVenc, lCancelado, lCarenBoni, lDownGrade, cMsgRet, lDelPH5, lOnlyRev, cUserId, cSituac, aPlaItens) 
    Local aAreaCNB   := CNB->(GetArea("CNB"))
    Local aAreaCN9   := CN9->(GetArea("CN9"))
    Local cChave     := ""
    Local cNumero    := ""
    Local cItem      := ""
    Local cAMAtu     := Left(Dtos(Date()), 6)
	Local lHtml      := .F. // não gera o resultado em html
    Local cHtml      := ""
    Local cFileCSV   := ""
    Local cChaveLock := ""
	Local cAMSX6     := AllTrim(SuperGetMV("TI_CFAMCAN",, ""))
    
    Local aItensVenc := {}
    Local aItensPend := {}
    Local aCancelado := {}
    Local aCarenBoni := {}
    Local aDownGrade := {}
    Local oFoto      := NIL
    Local lCanIntera := .F.
    Local nx         := 0
    Local lRevisa    := .F.
    Local cRevisa    := ""
    Local cMsgLock   := ""
    Local cMsgPHB    := ""
    Local lTotCan    := .F. 
    Local cLimRevisa := Nil
    Local dLimRevisa := Nil 
    Local lDetPh5    := Nil
    Local lSelSave   := Nil
    Local cCmpFim    := Nil
    Local cAMMemCalc := Nil
    Local cHtmMCalc  := Nil 
    Local aDetalhe   := Nil
    Local cAntSit    := ""
    Local aMensal    := {}
    Local aMensalCan := {}
    Local lCalcMens  := .T.

    
    Default cIdProc    := ""
    Default lItensVenc := .F.
    Default lCancelado := .F.
    Default lCarenBoni := .F.
    Default lDownGrade := .F.
    Default cMsgRet    := ""
    Default lOnlyRev   := .F.
    Default cUserId    := __cUserId
    Default cSituac    := '05'
    Default aPlaItens  := {}

    ProcRegua(1)
    
	If ! Empty(cAMSX6) .and. cAMSX6 > cAMAtu
		cAMAtu := cAMSX6
	EndIf


    // Trava o contrato
    CN9->(dbSetOrder(7))// Posiciona na Revisão Atual
    If ! CN9->(dbSeek(xFilial("CN9") + cContrato + cSituac))
        cMsgErro := "Contrato: " + cContrato + " não encontrado na CN9!"
        Return
    EndIf
    cRevisa := CN9->CN9_REVISA

    CNC->(DbSetOrder(5))  
    If ! CNC->(DbSeek(xFilial("CNC") + cContrato + cRevisa + "01"))
        cMsgErro := "Cliente não encontado CNC!"
        Return
    EndIf

    AI0->(DbSetOrder(1))  
    If ! AI0->(DbSeek(xFilial("AI0") +  CNC->(CNC_CLIENT + CNC_LOJACL) )) 
        cMsgErro := "Complemento de Cliente não encontado AI0!"
        Return
    EndIf

    cChaveLock:= "CN9"+xFilial("CN9")+CN9->CN9_NUMERO
    
    VerRevisa(cAMAtu, cContrato, cRevisa, lItensVenc, lCancelado, lCarenBoni, lDownGrade,  aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, @lCanIntera, lCalcMens, aMensalCan)
           
    lRevisa := .F.
    If lCancelado
        If ! Empty(aCancelado)
            lRevisa := .T.
        EndIf
    EndIf
    If lItensVenc 
        If ! Empty(aItensVenc) .or. ! Empty(aItensPend)
            lRevisa := .T.
        EndIf
    EndIf
    If lDownGrade
        If ! Empty(aDownGrade) 
            lRevisa := .T.
        EndIf
    EndIf 

 
    LogCro("1", cContrato, cRevisa, "", cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMAtu, , cUserId)
    If ! lRevisa .and. lOnlyRev
        LogCro("2", cContrato, cRevisa, "Cronograma Financeiro não calculado por não ter revisão!" , cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMAtu, ,cUserId)
        RestArea(aAreaCN9)
        RestArea(aAreaCNB)
        cMsgRet += "Cronograma Financeiro não calculado por não ter revisão!" 
        Return
    EndIf

    If ! U_GVFUNLOC(1, cChaveLock, , .F.)
        cMsgLock := "Contrato: " + cContrato + " bloqueado por outra estação"
        LogCro("5", cContrato, cRevisa, cMsgLock, cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMAtu, , cUserId)
        cMsgErro := cMsgLock
        Return 
    EndIf

    Begin Transaction
        
    	If lDelPH5
			ApagaPH5(cContrato, cRevisa, aAMSave )
    		ApagaPH6(cContrato, cRevisa, aAMSave )
			ApagaPH7(cContrato, cRevisa, aAMSave )

			For nx := 1 to len(aAMSave)
				U_GCVA103T(cContrato, cRevisa, aAMSave[nx])
			Next
		EndIf
 
        If lRevisa
            cRevisa := U_GCVA104R(cContrato) // Gera a revisão do contrato
            cMsgRet += " Nova revisão, " 
        EndIf

         If lCancelado 
            CancelMens(aMensalCan, cRevisa)
        EndIf
        
        
         // Calcula o Cronograma Financeiro dos items do contrato
        CNB->(DBgotop())
        cChave    := FwxFilial("CNB") + cContrato + cRevisa
        CNB->(DbSetOrder(1))  // CNB_REVISA+CNB_NUMERO+CNB_ITEM
        CNB->(DbSeek(cChave))
        While CNB->(! Eof() .and. CNB_FILIAL + CNB_CONTRA + CNB_REVISA == cChave)

            If ! Empty(aPlaItens) .and. Ascan(aPlaItens, {|x| x == CNB->(CNB_NUMERO + CNB_ITEM)}) == 0
                CNB->(DbSkip())
                Loop
            EndIf 
           
            If ! IsBlind()
                IncProc("Processando Item: " + cContrato + " " +CNB->(CNB_NUMERO  + " "+ CNB_ITEM) )
                ProcessMessage()
            EndIf
            cNumero := CNB->CNB_NUMERO
            cItem   := CNB->CNB_ITEM
            cAntSit := CNB->CNB_SITUAC
            
            lTotCan   := .F.
            //Efetiva cancelamento dos itens programados
            If lCancelado 
                lTotCan   := ProcCancela(aCancelado, cAMAtu, lItensVenc, aItensVenc)
            EndIf
         
            //Verifica itens vencidos
            If lItensVenc 
                If !lTotCan
                    ProcItensPend(aItensPend)
                    // Desativar programação futura
                    ProcItensVen(aItensVenc, cAMAtu)
                EndIf
            EndIf

           
            //trata bonificação e carencia
            If lCarenBoni 
                ProcBonCar(aCarenBoni, cAMAtu)
            EndIf
            If ! CNB->CNB_SITUAC $ "APCTOG"   //A=ATIVO,P=PENDENTE,C=CANCELADO,T=TRANSFERENCIA.  A SITUAÃ‡ÃƒO TRONCA, GRATUITO, MANUAL .... DEVE SER IGNORADO
                CNB->(DbSkip())
                Loop
            EndIf
            
            If lDownGrade
                If ProcDownGrade(aDownGrade, cAMAtu, cAntSit)
                    CNB->(DbSkip())
                    Loop
                EndIf 
            EndIf         
                              
            // calcula cronograma financeiro por item
            U_GCVA094A(cContrato, cNumero, cItem, aAMSave, lHtml, @cHtml, cFileCSV, @cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, lSelSave, cCmpFim, cAMMemCalc, cHtmMCalc, aDetalhe, cSituac)
            If ! Empty(cMsgErro)
                cMsgErro := "Contrato :" + cContrato + " " +CNB->(CNB_NUMERO  + " "+ CNB_ITEM) + CRLF + cMsgErro
                DisarmTransaction()
                Break
            EndIF


            CheckMensal(aMensal)
        
            CNB->(DbSkip())
        End

       
        //Trata downgrade
        TrataDownGrade(cContrato, cRevisa, aDownGrade, aAMSave, @cMsgErro, @cMsgPHB)
        ////TIADMVIN-3138
        If lCanIntera .or. ! Empty(aDownGrade)  // tem cancelamento de um item intera, necessario refazer a foto
            oFoto:= Tgcvxc20():New(Subs(cContrato, 4, 6), "00")     
            oFoto:Load()
            oFoto:Reprecifica()
            oFoto:ChkAlt()
            oFoto:lJobIniMes := .t.
            oFoto:aAMSave := aAMSave
            oFoto:Save(.t.)
        Else 
            //Atualiza PH6 e PH7 
            For nx := 1 to len(aAMSave)
                U_GCVA103T(cContrato, cRevisa, aAMSave[nx])
            Next
        EndIf 

        //Verifica Gerenciador Licenças Externas
        If ! Empty(aCancelado) .or. ! Empty(aItensVenc)
            NotificaParceiro(cContrato)
            U_TGVSPN7( cContrato , cRevisa )
        EndIf

        ProcMensal(cContrato , cRevisa, aMensal, aAMSave)

    End Transaction
    
    If ! Empty(cMsgErro)
        LogCro("4", cContrato, cRevisa, cMsgErro, cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMAtu, , cUserId)
    Else
        LogCro("2", cContrato, cRevisa, ""      , cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMAtu, , cUserId)
    EndIf
    U_GVFUNLOC(2, cChaveLock)  // Destrava o contrato



    RestArea(aAreaCN9)
    RestArea(aAreaCNB)
    If ! Empty(aItensVenc)
        cMsgRet += " Itens Vencidos, " 
    EndIf
    If ! Empty(aCancelado)
        cMsgRet += " Efetivação de cancelados," 
    EndIf
    If ! Empty(aCarenBoni)
        cMsgRet += " Bonificação e carencia,"
    EndIf
    If ! Empty(aDownGrade)
        cMsgRet += " Downgrade,"
    EndIf 
    cMsgRet += " Cronograma Financeiro" 
    

Return

Static Function CancelMens(aMensalCan, cRevisa)
    Local ni := 0

    For ni:=1 to Len(aMensalCan)
        aMensalCan[ni, 2]:cRevNew := cRevisa
        aMensalCan[ni, 2]:Calcula()
        aMensalCan[ni, 2]:SaveValorMensalCancelado()
        aMensalCan[ni, 2]:IntPsaMensalCancelado()
        aMensalCan[ni, 2]:Destroy()
    Next
Return

Static Function NotificaParceiro(cContrato)
    Local oContrato 
    
    oContrato  := Tgcvxc13():New(cContrato, "05") 
    oContrato:lCalcCron := .F.  
    oContrato:lRevisa   := .F. 
    oContrato:Save() 
    oContrato:Destroy()
    FreeObj(oContrato)
    oContrato:= NIL

Return 


Static Function CheckMensal(aMensal)
    Local nPosProp := 0
    Local cIniFat  := ""

    Begin Sequence
        If CNB->CNB_TIPREC <> "1"
            Break
        EndIf

        If !IsMensal(CNB->CNB_PROPOS , CNB->CNB_PROITN, CNB->CNB_PRODUT)
            Break
        EndIf

        If Empty(CNB->CNB_XCOMPE)
            cIniFat := Left(DtoS(CNB->CNB_DTSITU), 6)
        Else
            cIniFat := CmpToAM(CNB->CNB_XCOMPE) 
        EndIf

        nPosProp := Ascan(aMensal, { |x| x[1] == CNB->CNB_PROPOS })

        If nPosProp == 0
            AADD(aMensal, {CNB->CNB_PROPOS, cIniFat, {} } )
            nPosProp := Len(aMensal)
        EndIf

        If aMensal[nPosProp, 2] > cIniFat
            aMensal[nPosProp, 2] := cIniFat
        EndIf

        AADD(aMensal[nPosProp, 3], {CNB->CNB_NUMERO, CNB->CNB_ITEM} )
        
    End Sequence
Return

Static Function IsMensal(cPropos, cPropItm, cProduto)

Return U_GCVXC32A(cPropos , cPropItm, cProduto)


Static Function ProcMensal(cContrato, cRevisa, aMensal, aAMSave)
    Local ni      := 0
    Local cPropos := ""
    Local cIniFat := ""
    Local aItensM := {}
    Local oMensal := Nil

    For ni:=1 to Len(aMensal)
        cPropos := aMensal[ni, 1]
        cIniFat := aMensal[ni, 2]
        aItensM := aClone(aMensal[ni, 3])

        AtuaPWF(cPropos, cIniFat)
        
        oMensal := Tgcvxc32():New(cContrato, cRevisa, cPropos, aItensM, aAMSave)
        oMensal:Load()
        oMensal:ProcMensal()
        oMensal:Destroy()

        FreeObj(oMensal)
    
    Next
Return


Static Function AtuaPWF(cPropos, cIniFat)

    PWF->(DbSetOrder(2)) //PWF_FILIAL+PWF_PROPMS

    If PWF->(DbSeek(xFilial("PWF") + cPropos))

        If Empty(PWF->PWF_INIMEN) 

            If PWF->(RecLock("PWF", .F.))
                PWF->PWF_INIMEN := cIniFat
                PWF->PWF_ENDMEN := SomaAM(PWF->PWF_INIMEN , PWF->PWF_REPASS - 1) 
                PWF->(MSUnLock())
            EndIf 
        EndIf
    EndIf

Return




Static Function TrataDownGrade(cContrato, cRevisa, aDownGrade, aAMSave, cMsgErro, cObs)
    Local nx 
    Local aCNBCalc  := {}
    Local cAMIni    := ""
    Local cAMFim    := ""
    Local cAmAux    := ""

    If Len(aDownGrade) = 0
        Return
    EndIF

	For nx:= 1 to len(aDownGrade)
		AtuCNB(cContrato, cRevisa, aDownGrade[nx], aCNBCalc, @cAMIni, @cAMFim)
		AtuPSW(aDownGrade[nx], @cObs)
	Next 

    cAmAux := cAMIni

    While cAmAux <= cAMFim
        If Ascan(aAMSave, cAmAux) <= 0
            AADD(aAMSave, cAmAux)
        EndIf
        cAmAux := SomaAM(cAmAux, 1)
    End
    aSort(aAMSave)

Return     


Static Function SomaAM(cAm, nMes) 
    Local cMes := ""
    Local cAno := ""
    Local ni   := 0

    For ni:= 1 to nMes
        cMes := Right(cAM, 2)
        cAno := Left(cAM, 4)

        If cMes == "12"
            cAM := soma1(cAno) + "01"
        Else 
            cAM := soma1(cAM)
        EndIf
    Next
Return cAM

Static Function AtuCNB(cContrato, cRevisa, aItem, aCNBCalc, cAMIni, cAMFim )
	Local cNumero  := aItem[1]
	Local nQtde    := aItem[3]
	Local nVlrUni  := aItem[4]
	Local nVlrTot  := nQtde * nVlrUni
	Local nNewCnb  := 0
	Local cNewItem := SeqCNB(cContrato, cRevisa, cNumero)
    Local nRecCNB  := aItem[6]
    Local cAntSit  := aItem[7]
    
    //{PSW_NUMERO, PSW_ITEM, PSW_QTDID, PSW_VLUID, PSW->(Recno()), 0 }

    CNB->(DbGoto(nRecCNB)) 
    
    aadd(aCNBCalc, nRecCNB)
	nNewCnb := CNB->(RevCopyRec({ 	{"CNB_ITEM"  , cNewItem },;
	                              	{"CNB_QUANT" , nQtde    },;
                                    {"CNB_QTDLIC", nQtde    },; 
							   		{"CNB_VLUNIT", nVlrUni  },;
									{"CNB_VLTOT" , nVlrTot  },;
									{"CNB_XTPILI", "2"      },;
                                    {"CNB_SITUAC", cAntSit  }}))

	CNB->(RecLock("CNB", .F.))
    CNB->CNB_STATRM := "124" 
	CNB->CNB_SITUAC := "O"
	CNB->(MsUnLock())

    U_TGC07P51(nNewCnb)

	U_TGC07PHN(cContrato, cRevisa, nRecCNB, nNewCnb, @cAMIni, @cAMFim)
	aadd(aCNBCalc, nNewCnb)

Return 

Static Function AtuPSW(aDownGrade, cObs)
	Local nRecno := aDownGrade[5]

	PSW->(Dbgoto(nRecno))
	cObs += CRLF
	cObs += "Ticket : " + AllTrim(PSW->PSW_TICKET) + CRLF
	
	PSW->(RecLock("PSW", .F.))
	PSW->PSW_STATUS := "E"
	PSW->(msunlock())

Return 

Static Function RevCopyRec(aCampos)
	Local nRegOri:= Recno()
	Local nRecno
	Local aDados:={}
	Local nX
	Local cCampo
	Local nPos
	For nX := 1 to FCount()
		cCampo := FieldName(nX)
		nPos := Ascan(aCampos,{|x| Alltrim(x[1]) == Alltrim(cCampo)})
		If Empty(nPos)
			aadd(aDados,FieldGet(nX))
		Else
			aadd(aDados,aCampos[nPos,2])
		EndIf
	Next
	RecLock(Alias(), .T.,  )
	For nX := 1 to FCount()
		FieldPut(nX,aDados[nX])
	Next
    
    If  CNB->CNB_SITUAC == "A"
        CNB->CNB_STATRM := "000"
    EndIf 

	MsUnLock()
	nRecno := Recno()
	Dbgoto(nRegOri)
Return nRecno

Static Function SeqCNB(cContra, cRevisa, cPlanilha)
	Local cSeqCNB:= "000000"
 	
	CNB->(DbSetOrder(1)) //CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM
    CNB->(DbSeek(xFilial("CNB") + cContra + cRevisa + cPlanilha))
    While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO == xFilial("CNB") + cContra + cRevisa + cPlanilha)
        cSeqCNB	:= CNB->CNB_ITEM
		CNB->(dbSkip())
	EndDo

Return Soma1(cSeqCNB)




Static Function AtuItemIntera(oFoto)
    Local np := 0
    Local nCNB_QUANT  := 0
    Local nCNB_VLUNIT := 0
    Local nCNB_VLTOT  := 0
    Local aCNB := oFoto:aCNB

    np:= Ascan(aCNB, { |x| x[2] + x[3]== CNB->(CNB_NUMERO + CNB_ITEM)})
    If Empty(np)
        Return 
    EndIf 

    nCNB_QUANT  := aCNB[np,  8] 
    nCNB_VLUNIT := aCNB[np,  9]
    nCNB_VLTOT  := aCNB[np, 10]

    If Empty(nCNB_QUANT)
        Return
    EndIf
    
    If CNB->CNB_QUANT  == nCNB_QUANT .AND. CNB->CNB_VLUNIT == nCNB_VLUNIT
        Return  
    EndIf 

    CNB->(RecLock("CNB", .F.))
    CNB->CNB_QUANT  := nCNB_QUANT
    CNB->CNB_VLUNIT := nCNB_VLUNIT
    CNB->CNB_VLTOT  := nCNB_VLTOT 
    CNB->(MsUnLock())


Return 



Static Function GetItens(cCampo, aItens)
    Local np :=0
    Local uRet := NIL
    np := Ascan(aItens, {|x| x[1] == cCampo})
    If np > 0
        uRet := aItens[np, 2]
    EndIf 
Return uRet

Static Function ProcCancela(aCancelado, cAMProc, lItensVenc, aItensVenc)
    Local cNumero := CNB->CNB_NUMERO
    Local cItem   := CNB->CNB_ITEM
    Local cChave  := ""
    Local aRecno  := {} 
    Local lCanTotal := .F.
    Local n1        := 0 
    Local lItVncid  := .F.
    Local lProgTot  := .F. 

    If Empty(Ascan(aCancelado, cNumero + cItem))
        Return lCanTotal
    EndIf

    If lItensVenc //se rodou o job ligando os flags CANCELADOS e ITENS VENCIDOS simultaneamente...caso esteja vencido
                  //descarta "programacao parcial" e deixa cancelar por ITENS VENCIDOS
        If Ascan(aItensVenc, cNumero + cItem) > 0 
            lItVncid := .T.
        EndIf 
    EndIf 

    PH3->(DbSetOrder(1)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_PRODUT+PH3_ITEM+PH3_STATUS+PH3_CMPSOL+PH3_CMPCAN+PH3_ITSEQ 
    cChave := xFilial("PH3") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_PRODUT + CNB_ITEM )
    PH3->(DbSeek(cChave))
    While PH3->(! Eof() .and. cChave == PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_PRODUT + PH3_ITEM)
        If ! PH3->PH3_STATUS == "P" 
            PH3->(DbSkip())
            Loop
        EndIf  
        If CmptoAM(PH3->PH3_CMPCAN) > cAMProc
            PH3->(DbSkip())
            Loop
        EndIf

        aadd(aRecno, PH3->(Recno()))
        PH3->(DbSkip())
    End
        
    If lItVncid 
        If Len(aRecno) == 1 
            PH3->( dbgoto(aRecno[1]) )
            If PH3->PH3_QTDCAN == CNB->CNB_QUANT
                lProgTot := .T. //se o item esta vencido mas existe uma programação p/canc total, então executa ela!
            EndIf 
        EndIf 
            
        If !lProgTot 
            For n1 := 1 to Len(aRecno)
                PH3->( dbgoto(aRecno[n1]) )
                PH3->(RecLock("PH3", .F.))
                PH3->PH3_STATUS := "D"
                PH3->(MsUnLock())
            Next
        
            Return lCanTotal
        EndIf
    EndIf 

    lCanTotal := EfetivaCanc(aRecno, cAMProc)
Return lCanTotal


Static Function ProcDownGrade(aDownGrade, cAMProc, cAntSit)
    Local cNumero := CNB->CNB_NUMERO
    Local cItem   := CNB->CNB_ITEM
    Local np      := 0


    np := Ascan(aDownGrade, { |x| x[1] + x[2] == cNumero + cItem})
    If Empty(np)
        Return .F.
    EndIf
    //{PSW_NUMERO, PSW_ITEM, PSW_QTDATU, PSW_VLUID, PSW->(Recno()), 0 }
    aDownGrade[np, 6] := CNB->(Recno())
    aDownGrade[np, 7] := cAntSit
    
Return .T. 


Static Function ProcItensPend(aItensPend) 
    Local cNumero    := CNB->CNB_NUMERO
    Local cItem      := CNB->CNB_ITEM
    Local cStatRM := SuperGETMV("TI_SPENPUB",,"107")

    If Empty(Ascan(aItensPend, cNumero + cItem))
        Return
    EndIf

    // Alterar CNB com status de pendente
    CNB->(RecLock("CNB", .F.))
        CNB->CNB_SITUAC := "P"
        CNB->CNB_STATRM := cStatRM
    CNB->(MsUnLock())
    
Return


Static Function ProcItensVen(aItensVenc, cAMProc)
    Local cNumero    := CNB->CNB_NUMERO
    Local cItem      := CNB->CNB_ITEM
    Local aRecnoPH3  := {}
    Local lCanTotal  := .F. 
    If Empty(Ascan(aItensVenc, cNumero + cItem))
        Return
    EndIf

    // Incluir PH3 p o mes em programação
    ProcIncProgPH3(cAMProc)
    aRecnoPH3 := {PH3->(Recno())}
    lCanTotal := EfetivaCanc(aRecnoPH3, cAMProc)
    
Return

Static Function EfetivaCanc(aRecno, cAMProc)
    Local nx
    Local nQtdCan := 0
    Local cAMVFim := Left(DTOS(CNB->CNB_VIGFIM), 6)
    Local cStatRM := ""
    Local lCanTot := .F.
    Local nNewQtd := 0
    //Local cBematech	:= SupperGetMv("TI_BMCDMST",, "000003") //BEMA - Codigo da empresa de integração que indica se é Master - Bematech
	Local cBematech	:= SuperGetMv("TI_BMCDMST",, "000003") //BEMA - Codigo da empresa de integração que indica se é Master - Bematech
	
    If cAMVFim > cAMProc
        cStatRM := SuperGetMv("TI_CSITCAN")          // cancelamento por programação
    Else
        cStatRM := SuperGetMv("TI_CSETPUB",, "105")   // cancelamento por vencimento
    EndIf
    // ALTERA PH3
    For nx:= 1 to Len(aRecno)
        PH3->(DbGoto(aRecno[nx]))
        PH3->(RecLock("PH3", .F.))
            PH3->PH3_STATUS := "C"
        PH3->(MsUnLock())
        
	// Alteração da PN0 NUM SERIE HARDWARE CONTRATOS  
        If ! Empty(CNB->CNB_XINTAD) .AND. ! Empty(CNB->CNB_XGERLC) .and. cBematech == CNB->CNB_XINTAD .AND. (CNB->CNB_QUANT - PH3->PH3_QTDCAN) > 0
            AltPN0(CNB->CNB_QUANT - PH3->PH3_QTDCAN, PH3->PH3_ITSEQ) // pendente 
        EndIf
        nQtdCan += PH3->PH3_QTDCAN            
    Next

    nNewQtd := CNB->CNB_QUANT - nQtdCan
    lCanTot := Empty(nNewQtd) 
    
    //ALTERA CNB
    CNB->(RecLock("CNB", .F.))
        IF lCanTot   // CANCELAMENTO TOTAL
            CNB->CNB_STATRM := cStatRM
            CNB->CNB_SITUAC := "C"
            CNB->CNB_ATIVO  := "2"   // 1- SIM 2=NÃƒO
        Else //parcial
            CNB->CNB_QUANT  := nNewQtd
            CNB->CNB_VLTOT  := nNewQtd * CNB->CNB_VLUNIT
            CNB->CNB_QTDMED := nNewQtd
        EndIf

        //Atualiza numero de serie, Marca como cancelados ( Tratamento Bemacash ) // Seattle
        If ! Empty(CNB->CNB_XINTAD) .AND. ! Empty(CNB->CNB_XGERLC)
            CNB->CNB_XGERLC := "1"
            CNB->CNB_XLCLIC := "2"
            CNB->CNB_XQTINE := nQtdCan
        EndIf
    CNB->(MsUnLock())

    AltPB0()

    // Altera Bonficação
    If lCanTot
        // Cancela as bonificações futuras
        ProcBonCar(NIL, cAMProc, .T.)  
    Else
        // atualiza bonificações futuras com a nova quantidade
        AltBonCar(cAMProc)  
    EndIf

    If lCanTot
        // itens futuros ph3
        DesativaProg(cAMProc)
    EndIf


Return lCanTot

Static Function AltPB0()

    Local cStatus := "1" //"LIBERADO"
    Local lMktPlace  := cEmpAnt == SuperGetMV('TI_EMPMKTP',, "90")   

		
	If ! lMktPlace
		Return 
	EndIf 
	
	PB0->(DbSetOrder(1))  //PB0_FILIAL+PB0_CONTRA+PB0_NUMERO+PB0_ITEM    
	If ! PB0->(DbSeek(FwxFilial("PB0") + CNB->(CNB_CONTRA+CNB_NUMERO+CNB_ITEM)))
		Return 
	EndIf

	If CNB->CNB_SITUAC $ "APG"    //ATIVO, PENDENTE, GRATUITO
		If PB0->PB0_STATUS == "5" .and. CNB->CNB_SITUAC <> "G"
			cStatus := "5"
		EndIf
	ElseIf CNB->CNB_SITUAC == "S"   // BLOQUEADO
		cStatus := "2"
	ElseIf CNB->CNB_SITUAC $ "COT"  //CANCELADO, Trocado e Transferido
		cStatus := "3"
	EndIf 

	PB0->(RecLock("PB0", .F.))
	PB0->PB0_QUANT  := CNB->CNB_QUANT
	PB0->PB0_DTEVEN := dDataBase 
	PB0->PB0_STATUS := cStatus
	PB0->(MsUnLock())
Return

Static Function AltPN0(nNewQtd, cPH3Seq) 
    Local cChave := ""
    Local aNovo  :=  {}
    Local cNewSeq:= "000000"


    //PN0_FILIAL+PN0_CONTRA+PN0_REVISA+PN0_NUMERO+PN0_ITEM+PN0_HRDLCK    
    PN0->(DbSetOrder(1))                                                                                             
    PN0->(DbSeek(cChave))

    While PN0->(! Eof() .and. cChave == PN0_CONTRA+PN0_REVISA+PN0_NUMERO+PN0_ITEM)
        If cNewSeq < PN0->PN0_SEQ
            cNewSeq := PN0->PN0_SEQ
        EndIF
        PN0->(DbSkip())
    End
    cNewSeq := Soma1(cNewSeq)

    PN0->(DbSeek(cChave))
    While PN0->(! Eof() .and. cChave == PN0_CONTRA+PN0_REVISA+PN0_NUMERO+PN0_ITEM)
        If ! PN0->PN0_STATUS == "4"
            PN0->(DbSkip())
            Loop
        EndIf  
        If ! PN0->PN0_PH3SEQ == cPH3Seq
            PN0->(DbSkip())
            Loop
        EndIf  
        aNovo := {  {"PN0_SEQ"   , cNewSeq},;
                    {"PN0_QTINT" , 0      },;
                    {"PN0_QUANT" , nNewQtd},;
                    {"PN0_STATUS", "5"    }}

        PN0->(CBCopyRec(aNovo))   
        PN0->(DbSkip())
    End


Return 

Static Function DesativaProg(cAMProc)
    Local nx
    Local aRecno := {}
	Local cChave := ""

    PH3->(DbSetOrder(1)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_PRODUT+PH3_ITEM+PH3_STATUS+PH3_CMPSOL+PH3_CMPCAN+PH3_ITSEQ 
    cChave := xFilial("PH3") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_PRODUT + CNB_ITEM )
    PH3->(DbSeek(cChave))
    While PH3->(! Eof() .and. cChave == PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_PRODUT + PH3_ITEM)
        If ! PH3->PH3_STATUS == "P"
            PH3->(DbSkip())
            Loop
        EndIf  

        If CmptoAM(PH3->PH3_CMPCAN) > cAMProc
            aadd(aRecno, PH3->(Recno()))
        EndIf
        PH3->(DbSkip())
    End

    For nx:= 1 to Len(aRecno)
        PH3->(DbGoto(aRecno[nx]))
        PH3->(RecLock("PH3", .F.))
            PH3->PH3_STATUS := "D"
        PH3->(MsUnLock())
    Next


Return

Static Function ProcIncProgPH3(cAMProc)
    Local cSeq      := "000000"
    Local cMotBC    := ""
    Local cChave    := ""

    cChave := xFilial("PH3") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + AMtoCmp(cAMProc)) 

    PH3->(DbSetOrder(5))
    PH3->(DbSeek(cChave))
    While PH3->(! Eof () .and. PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_ITEM + PH3_CMPCAN == cChave)
        cSeq := PH3->PH3_ITSEQ
        PH3->(DbSkip())
    End
    cSeq := Soma1(cSeq)
    
    If AI0->AI0_SETPUB == '1'   // setor Publico
        cMotBC := SuperGETMV("TI_MCANPUB",,"232") 
    Else
        cMotBC := SuperGETMV("TI_MCANVNC",,"216") 
    EndIf


    PH3->(RecLock("PH3", .T.))
        PH3->PH3_FILIAL := xFilial("PH3")
        PH3->PH3_CONTRA := CNB->CNB_CONTRA
        PH3->PH3_REVISA := CNB->CNB_REVISA
        PH3->PH3_NUMERO := CNB->CNB_NUMERO

        PH3->PH3_ITEM   := CNB->CNB_ITEM     
        PH3->PH3_ITSEQ  := cSeq
        PH3->PH3_PRODUT := CNB->CNB_PRODUT
        PH3->PH3_QUANT  := CNB->CNB_QUANT
        PH3->PH3_QTDCAN := CNB->CNB_QUANT
        PH3->PH3_QTDSLD := CNB->CNB_QUANT
        PH3->PH3_OBS    := "Itens Vencidos"
        PH3->PH3_CMPCAN := AMtoCmp(cAMProc)  
        PH3->PH3_CMPSOL := AMtoCmp(cAMProc)    
        PH3->PH3_STATUS := "P"  
        PH3->PH3_SITUAC := CNB->CNB_SITUAC  
        PH3->PH3_STATRM := CNB->CNB_STATRM  // PEGAR O STATRM DA CNB PARA FICAR COMO HISTORIO NA PH3  
        PH3->PH3_MOTBC  := cMotBC
        PH3->PH3_GRUPO  := CNB->CNB_GRUPO
        PH3->PH3_UNINEG := CNB->CNB_UNINEG  
        PH3->PH3_PROPOS := CNB->CNB_PROPOS  
        PH3->PH3_VERPRO := CNB->CNB_PROREV
        PH3->PH3_ITMPRO := CNB->CNB_ITMPRO  
        PH3->PH3_FLDPRP := CNB->CNB_XFLDPR  
        PH3->PH3_DATA   := MsDate()
        PH3->PH3_CMPINI := AMtoCmp(cAMProc) 
        PH3->PH3_CMPSOL := AMtoCmp(cAMProc) 
    PH3->(MsUnLock())

    // ATUALIZAR PN0

Return

Static Function ProcBonCar(aCarenBoni, cAMProc, lIsCancela)
    Local cNumero       := CNB->CNB_NUMERO
    Local cItem         := CNB->CNB_ITEM
    Local cChave        := ""
    Local cCmpAnt       := ""
    Local cObsCancel    := ""
    Local nx            := 0
	Local aRecPH4       := {}
	
    Default lIsCancela := .F.

    If ! lIsCancela .and. Empty(Ascan(aCarenBoni, cNumero + cItem))
        Return
    EndIf

    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT)   //B=BonificOper;C=Carencia
    PH4->(DbSeek(cChave))
    While PH4->(! Eof() .and. PH4_FILIAL + PH4_CONTRA + PH4_REVISA + PH4_NUMERO + PH4_ITEM + PH4_PRODUT == cChave)
        cObsCancel := ""
		If ! PH4->PH4_STATUS == "A"
            PH4->(DbSkip())
            Loop
        EndIf
        If ! lIsCancela  // muda para elimnado os ativo
            If CmptoAM(PH4->PH4_CMPFIM) >= cAMProc
                PH4->(DbSkip())
                Loop
            EndIf
            cObsCancel += CRLF + DtoC(MsDate()) + " - Eliminado via sistema."
            AADD(aRecPH4,{PH4->(Recno()), "E" , cObsCancel})
        Else // muda para inativo os futuros
            If CmptoAM(PH4->PH4_CMPINI) < cAMProc
                cCmpAnt := PH4->PH4_CMPFIM
				cObsCancel  += CRLF + DtoC(MsDate()) + " - Eliminado via sistema devido o cancelamento total do Item."
				cObsCancel  += CRLF + " - Alterado competencia final de " + cCmpAnt + "  para " + PH4->PH4_CMPFIM + "!"
				AADD(aRecPH4,{PH4->(Recno()), "E" , cObsCancel, AdicMes(AMtoCmp(cAMProc), -1)})
            Else
                cObsCancel   += CRLF + DtoC(MsDate()) + " - Inativado via sistema devido o cancelamento total do Item."
                AADD(aRecPH4,{PH4->(Recno()), "I" , cObsCancel})
            EndIf
        EndIf
        PH4->(DbSkip())
    End
	
	For nx := 1 to Len(aRecPH4)
		PH4->(DbGoto(aRecPh4[nx,1])) 
		If RecLock("PH4" , .F.)
			If Len(aRecPh4[nx]) > 3 
				PH4->PH4_CMPFIM := aRecPh4[nx,4]
			EndIF
			PH4->PH4_STATUS := aRecPh4[nx,2]
			PH4->PH4_OBS  := aRecPh4[nx,3]
			PH4->(MsUnLock())
		EndIf
	Next
Return

Static Function AltBonCar(cAMProc)
    Local cChave  := ""
    Local nVlrTot := 0
    
    
    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT)   //B=BonificOper;C=Carencia
    PH4->(DbSeek(cChave))
    While PH4->(! Eof() .and. PH4_FILIAL + PH4_CONTRA + PH4_REVISA + PH4_NUMERO + PH4_ITEM + PH4_PRODUT == cChave)
        If ! PH4->PH4_STATUS == "A"
            PH4->(DbSkip())
            Loop
        EndIf
        If  CmptoAM(PH4->PH4_CMPFIM) > cAMProc
            PH4->(DbSkip())
            Loop
        EndIf

        PH4->(RecLock("PH4", .F.))
            nVlrTot := CNB->CNB_QUANT * CNB->CNB_VLUNIT
            If PH4->PH4_TPDESC == "P"  // PORCENTAGEM
                PH4->PH4_VLRDES := nVlrTot * PH4->PH4_PERDES / 100
            Else
                PH4->PH4_PERDES := PH4->PH4_VLRDES / nVlrTot * 100
            EndIf            
        PH4->(MsUnLock())

        PH4->(DbSkip())
    End

Return



/*
###############################################################################################################################
*/


User Function GCVA095L(cContrato, cAMProc, cMsgErro, cIdProc , lItensVenc, lCancelado, lCarenBoni, lDownGrade, cLote, cUserId) 
    Local aAreaCNB   := CNB->(GetArea("CNB"))
    Local aAreaCN9   := CN9->(GetArea("CN9"))
    Local aItensVenc := {}
    Local aItensPend := {}
    Local aCancelado := {}
    Local aCarenBoni := {}
    Local aDownGrade := {}
    Local lCanIntera := .F.
    Local lCalcMens  := .F.

    Local cAMAtu     := Left(Dtos(Date()), 6)
	
    Default lItensVenc := .T.
    Default lCancelado := .T.
    Default lCarenBoni := .T.
    Default lDownGrade := .T. 

    // Trava o contrato
    CN9->(dbSetOrder(7))// Posiciona na Revisão Atual
  	If ! CN9->(dbSeek(xFilial("CN9") + cContrato + "05"))
        cMsgErro := "Contrato: " + cContrato + " não encontrado na CN9!"
        Return
    EndIf
    cRevisa := CN9->CN9_REVISA

    CNC->(DbSetOrder(5))  
    If ! CNC->(DbSeek(xFilial("CNC") + cContrato + cRevisa + "01"))
        cMsgErro := "Cliente não encontado CNC!"
        Return
    EndIf

    AI0->(DbSetOrder(1))  
    If ! AI0->(DbSeek(xFilial("AI0") +  CNC->(CNC_CLIENT + CNC_LOJACL) )) 
        cMsgErro := "Complemento de Cliente não encontado AI0!"
        Return
    EndIf
    
    VerRevisa(cAMAtu, cContrato, cRevisa, lItensVenc, lCancelado, lCarenBoni, lDownGrade,  aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, lCanIntera, lCalcMens )
    
    Begin Transaction
        LogCro(" ", cContrato, cRevisa, "", cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMProc, cLote, cUserId)
    End Transaction

    RestArea(aAreaCN9)
    RestArea(aAreaCNB)

Return






Static Function VerRevisa(cAMAtu, cContrato, cRevisa, lItensVenc, lCancelado, lCarenBoni, lDownGrade, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, lCanIntera, lCalcMens, aMensalCan)
    Local cChave      := ""
    Local lSetorPub   := AI0->AI0_SETPUB == '1'
    Local cAMVFim     := ""     // Ano Mes Virgencia Final Contrato
    Local cAMVFimSP   := ""     // Ano Mes Virgencia Final Setor Publico
    Local nDiasTolera := 4 * 365 //SuperGETMV("TI_CPENPUB", , 2)  foi alterado para 4 anos 
	Local cStatRM     := SuperGETMV("TI_SPENPUB",,"107")

    Default lCanIntera := .F. 
    Default aMensalCan := {}

    // percorre a CNB // 
    aItensVenc := {}
    If lItensVenc
        cChave    := xFilial("CN9") + cContrato + cRevisa
        CNB->(DbSetOrder(1))  // CNB_REVISA+CNB_NUMERO+CNB_ITEM
        CNB->(DbSeek(cChave))
        While CNB->(! Eof() .and. CNB_FILIAL + CNB_CONTRA + CNB_REVISA == cChave)
            If CNB->CNB_SITUAC $ "CTO"   //A=ATIVO,P=PENDENTE,C=CANCELADO,T=TRANSFERENCIA,O=TROCA, M=MANUAL, S=SUSPENSO
                CNB->(DbSkip())
                Loop
            EndIf
            cAMVFim := Left(DTOS(CNB->CNB_VIGFIM), 6)

            If lSetorPub
                cAMVFimSP := Left(DTOS(CNB->CNB_VIGFIM + nDiasTolera), 6) 
            EndIf

            If cAMAtu <= cAMVFim   
                CNB->(DbSkip())
                Loop
            EndIf

            If lSetorPub 
                If cAMAtu <= cAMVFimSP 
                    If CNB->CNB_SITUAC == "A" .OR. (CNB->CNB_SITUAC == "P" .AND. CNB->CNB_STATRM <> cStatRM)
                        AADD(aItensPend, CNB->(CNB_NUMERO + CNB_ITEM) )
                    EndIf
                Else
                    AADD(aItensVenc, CNB->(CNB_NUMERO + CNB_ITEM) )  
                    If Isfoto(cContrato, cRevisa, CNB->CNB_NUMERO, CNB->CNB_ITEM)
                        lCanIntera := .T. 
                    EndIf  
                EndIf
            Else                
                AADD(aItensVenc, CNB->(CNB_NUMERO + CNB_ITEM) )
                If Isfoto(cContrato, cRevisa, CNB->CNB_NUMERO, CNB->CNB_ITEM)
                    lCanIntera := .T. 
                EndIf 
            EndIf
            CNB->(DbSkip())
        End
    EndIf
    // percorre a ph3
    aCancelado := {}  
    If lCancelado
        PH3->(DbSetOrder(4)) //PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_STATUS + PH3_CMPCAN
        cChave := xFilial("PH3") + cContrato + cRevisa + "P" //+ AMtoCmp(cAMAtu)
        
        PH3->(DbSeek(cChave))
        While PH3->(! Eof() .and. PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_STATUS  == cChave )
  
            If CMPtoAM(PH3->(PH3_CMPCAN)) <= cAMAtu
                aadd(aCancelado,  PH3->(PH3_NUMERO + PH3_ITEM))

                If Isfoto(cContrato, cRevisa, PH3->PH3_NUMERO, PH3->PH3_ITEM)
                    lCanIntera := .T. 
                EndIf

                AddItemMensal(lCalcMens, aMensalCan)
            EndIf
            PH3->(DbSkip())
        End
    EndIf

    // percorre a ph4
    aCarenBoni := {}   
    If lCarenBoni
        PH4->(DbSetOrder(4)) //PH4_FILIAL + PH4_CONTRA + PH4_REVISA + PH4_STATUS
        cChave := xFilial("PH4") + cContrato + cRevisa + "A" 
        PH4->(DbSeek(cChave))
        While PH4->(! Eof() .and. PH4_FILIAL + PH4_CONTRA + PH4_REVISA + PH4_STATUS  == cChave )
            If CmptoAM(PH4->PH4_CMPFIM) < cAMAtu
                aadd(aCarenBoni,  PH4->(PH4_NUMERO + PH4_ITEM))
            EndIf
            PH4->(DbSkip())
        End
    EndIf

    aDownGrade := {}
    If lDownGrade  
        PSW->(dbsetorder(1))
        PSW->(dbseek(FWxFilial("PSW") + cContrato))
        While PSW->(!EOF()) .AND. cContrato == PSW->PSW_CONTRA 
            If PSW->PSW_AMEXE == cAMAtu .AND. PSW->PSW_STATUS == "P"
                PSW->(aadd(aDownGrade,  {PSW_NUMERO, PSW_ITEM, PSW_QTDID, PSW_VLUID, PSW->(Recno()), 0, '' }))
            EndIf
            PSW->(dbskip())
        End 
    EndIf 

   
    
Return 


Static Function Isfoto(cContrato, cRevisa, cNumero, cItem)
    Local cChave       := xFilial("CNB") + cContrato + cRevisa + cNumero + cItem
    Local lReprecifica := .F.

    If cChave <> CNB->(CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM)
        CNB->(DbSetOrder(1))  // CNB_REVISA+CNB_NUMERO+CNB_ITEM
        CNB->(DbSeek(cChave))
    EndIf
    ADY->(dbSetOrder(1))
    If  ADY->(dbSeek(FWxFilial("ADY") + CNB->CNB_PROPOS)) .and. ;
        u_GcxF2Fot(ADY->ADY_XCODAG, ADY->ADY_XMODAL) // essa função posiciona a PT6  
        lReprecifica := .T. 
    EndIf
Return lReprecifica

Static Function AddItemMensal(lCalcMens, aMensalCan)
    Local nPosMensal := 0

    If ! lCalcMens
        Return
    EndIf
    
    If GetMV("TI_INT201",, "N" ) == "N" //S-Sim;N-Não;P-Pergunta
        Return 
    EndIf 

    If IsMensal(PH3->PH3_PROPOS , PH3->PH3_ITMPRO, PH3->PH3_PRODUT)
        nPosMensal := Ascan(aMensalCan,{ |x| x[1] == PH3->PH3_PROPOS })
        If nPosMensal == 0
            AADD(aMensalCan, {PH3->PH3_PROPOS, CancelCarenBoniMensal():New(PH3->PH3_PROPOS, PH3->PH3_CONTRA , PH3->PH3_REVISA)})
            nPosMensal := Len(aMensalCan)
            aMensalCan[nPosMensal, 2]:LoadCNB()
            nPosMensal := Len(aMensalCan)
        EndIf

        PH3->(aMensalCan[nPosMensal, 2]:AddPH3itens(PH3_NUMERO, PH3_ITEM, PH3_QTDCAN, PH3_MOTBC, CmpToAm(PH3_CMPCAN), PH3_ITMPRO, PH3_ITSEQ))

    EndIf

Return
 


Static Function LogCro(cStatus, cContrato, cRevisa, cObs, cIdProc, aItensVenc, aItensPend, aCancelado, aCarenBoni, aDownGrade, cAMProc, cLote, cUserId)
    Local cTpOper := ""
    Local cCompet := AMtoCmp(cAMProc)
    Local cParams := ""
	Local lHlCloud := .F.
    
    If ! Empty(aItensVenc) .or. ! Empty(aItensPend)
		lHlCloud  := .T.
        cTpOper   := "000014"
        cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
        cParams   += "Processos: Itens Vencidos" + CRLF
        If ! Empty(cIDProc)
            LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, , cUserId)
            LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
            LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cParams, cCompet)
        EndIf
        
    EndIf
    // cancelados 
    If ! Empty(aCancelado)
		lHlCloud  := .T.
        cTpOper   := "000002"
        cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
        cParams   += "Processos: Cancelados" + CRLF        
        If ! Empty(cIDProc)
            LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, , cUserId)
            LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
            LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cParams, cCompet)
        EndIf
        
    EndIf
    // bonificação e carencia
    If ! Empty(aCarenBoni)
        cTpOper   := "000003"
        cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
        cParams   += "Processo: Bonificação e Carencia" + CRLF
        If ! Empty(cIDProc)
            LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, , cUserId)
            LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
            LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cParams, cCompet)
        EndIf
    EndIf
    If ! Empty(aDownGrade)
        cTpOper   := "000031"
        cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
        cParams   += "Processo: DownGrade" + CRLF
        If ! Empty(cIDProc)
            LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, , cUserId)
            LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
            LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cParams, cCompet)
        EndIf
    EndIf 

    // cronograma financeiro
    cTpOper   := "000001"
    cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
    cParams   += "Processo: Cronograma financeiro" + CRLF
    If ! Empty(cIDProc)
        LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, lHlCloud, cUserId)
        LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
        LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cParams, cCompet)
    EndIf

Return

Static Function LogPHSOper(cIDProc, cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet, lHlCloud, cUserId)
    Local cChave := ""
    Local cBranco:= Space(6)
	Local aAreaPHS := PHS->(GetArea())
	Default lHlCloud := .F.

    If cStatus == " "
       cChave := xFilial('PHS') + cIDProc + cContrato + cBranco + cTpOper    //PHS_FILIAL + PHS_IDPROC + PHS_CONTRA + PHS_REVISA + PHS_TPOPER
        PHS->(DbSetOrder(6))
        If ! PHS->(DbSeek(cChave))
            PHS->(RecLock("PHS", .T.))
            PHS->PHS_FILIAL := xFilial('PHS')
            PHS->PHS_IDPROC := cIDProc
            PHS->PHS_GRUPO	:= cEmpAnt
            PHS->PHS_UNINEG := FWxFilial("CN9")
            PHS->PHS_CONTRA := cContrato
            PHS->PHS_REVISA := cBranco
            PHS->PHS_TPOPER := cTpOper
            PHS->PHS_NUMERO := ""
            PHS->PHS_NUMMED := ""
            PHS->PHS_COMPET := cCompet
            PHS->PHS_HRINI	:= Time()
            PHS->PHS_DTINI	:= MsDate()
            PHS->PHS_CUSER	:= cUserId
            PHS->PHS_NUSER	:= UsrRetName(cUserId) 
            PHS->PHS_STATUS := " "   
			If lHlCloud    
				PHS->PHS_HLCLOU := "P"
			EndIf
            PHS->(MsUnLock())
        EndIf
    Else
        cChave := xFilial('PHS') + cIDProc + cContrato  + cBranco + cTpOper  //PHS_FILIAL + PHS_IDPROC + PHS_CONTRA + PHS_REVISA + PHS_TPOPER
        PHS->(DbSetOrder(6))
        
        While ! PHS->(DbSeek(cChave))
            // aguardando a outra threas criar o registro de status
            PHS->(DbSkip(0))
            Sleep(100)
        End
        If PHS->PHS_STATUS == "3" // ERRO DE PROGRAMA
            Return
        EndIf
        While ! PHS->(rLock())
            // aguardar o resgistro se destravado por outra thread
            PHS->(DbSkip(0))
            Sleep(100)
        End 

        If PHS->PHS_STATUS == "5" .or. cStatus > PHS->PHS_STATUS
            PHS->PHS_STATUS := cStatus
        EndIf
        If ! Empty(cObs) 
            cObs += CRLF
        EndIf
        PHS->PHS_OBS	:= PHS->PHS_OBS + cObs 
        If ! cStatus $ " 15"
            PHS->PHS_REVISA := cRevisa
            PHS->PHS_HRFIM	:= Time()
            PHS->PHS_DTFIM	:= MsDate()
        EndIf
        PHS->(MsUnLock())
    EndIf
    PHS->(DbCommit())
	RestArea(aAreaPHS)
Return


Static Function LogPHTOper(cIDProc, cTpOper, cParams, cStatus, cLote, cUserId)
    Local cChave    := ""
    Default cLote := ""

    If ! IsInCallStack("U_GCVA102V")
        Return
    EndIf

    If cStatus == " "
        cChave := xFilial("PHT") + cIdProc + cTpOper
        PHT->(DbSetOrder(1))
        If ! PHT->(DbSeek(cChave))
            PHT->(RecLock("PHT", .T.))
            PHT->PHT_FILIAL := FWxFilial('PHT')
            PHT->PHT_IDPROC := cIDProc
            PHT->PHT_TPOPER := cTpOper
            PHT->PHT_ROTINA := "TGCVA102V"
            PHT->PHT_PARAMS := cParams
            PHT->PHT_STATUS := "1"
            PHT->PHT_DTINI	:= MsDate()
            PHT->PHT_HRINI	:= Time()
            PHT->PHT_OBS	:= "Iniciado" + CRLF
            PHT->PHT_QREGQR := 1
            PHT->PHT_CUSER	:= cUserId
            PHT->PHT_NUSER	:= UsrRetName(cUserId)
            PHT->PHT_IDREPR := cLote
            PHT->(MsUnLock())
        Else
            While ! PHT->(RLock())
                // aguardar o resgistro se destravado por outra thread
                PHT->(DbSkip(0))
                Sleep(100)
            End 
            PHT->PHT_QREGQR += 1
            PHT->(MsUnLock())
        EndIf
    Else
        
        cChave := xFilial("PHT") + cIdProc + cTpOper
        PHT->(DbSetOrder(1))
        While ! PHT->(DbSeek(cChave))
            PHT->(DbSkip(0))
            // aguardando a outra threas criar o registro de status
            Sleep(100)
        End

        While ! PHT->(RLock())
            // aguardar o resgistro se destravado por outra thread
            PHT->(DbSkip(0))
            Sleep(100)
        End 

            //PHT->(RecLock("PHT",.F.))
            If cStatus == " "
                PHT->PHT_QREGQR += 1
            ElseIf cStatus == "1"
                PHT->PHT_QINICI += 1
            ElseIf cStatus == "2" 
                PHT->PHT_QINICI -= 1
                PHT->PHT_QCONCL += 1
            ElseIf cStatus == "4"
                PHT->PHT_QINICI -= 1
                PHT->PHT_QINVAV += 1
            EndIf
            
            If PHT->PHT_QREGQR == (PHT->PHT_QCONCL + PHT->PHT_QINVAV)  // CHEGOU NO FIM
                If ! Empty(PHT->PHT_QINVAV + PHT->PHT_QERRO)
                    PHT->PHT_STATUS := "3" // com ocorrencias
                    PHT->PHT_OBS	+= "Finalizado com ocorrencias" + CRLF
                Else
                    PHT->PHT_STATUS := "2" // com sucesso
                    PHT->PHT_OBS	+= "Finalizado" + CRLF
                EndIF
                PHT->PHT_DTFIM	:= MsDate()		 
                PHT->PHT_HRFIM	:= Time()
                PHT->PHT_TIMEPR := ElapTime( PHT->PHT_HRINI, PHT->PHT_HRFIM ) 
            EndIf
        PHT->(MsUnLock())
    EndIf
    PHT->(DbCommit())
Return


Static Function LogPH9Oper(cContrato, cRevisa, cTpOper, cStatus, cObs, cCompet)
    Local cChave     := ""
    Local cFim       := "ZZZZZZ"
    Local cStatusPH9 := ""
    Local cSeq       := "001"
    Local dDtOper    := MsDate()
    LOcal cHrOper    := Time()
    
    If cStatus == " "
        //limpar log anterior lixo
        PH9->(DbSetOrder(1))
        While PH9->(DbSeek(xFilial('PH9') + cContrato + cFim + cTpOper))
            PH9->(RecLock("PH9", .F.))
                PH9->(DbDelete())
            PH9->(MsUnlock())
            PH9->(DbSkip())
        End

        cSeq  := "001"
        PH9->(DbSetOrder(3))
        PH9->(DbSeek(xFilial('PH9') + Soma1(cContrato), .T.))
        PH9->(DbSkip(-1))
        If PH9->PH9_CONTRA == cContrato
           cSeq := Soma1(PH9->PH9_SEQ)
        EndIf
        
    EndIf

    If cStatus $ " 1"
        cStatusPH9 := "P"
    ElseIf cStatus == "2"
        cStatusPH9 := "C"
    ElseIf cStatus == "4"
        cStatusPH9 := "I"
    EndIf

    PH9->(DbSetOrder(1))
    cChave := xFilial('PH9') + cContrato + cFim + cTpOper //PH9_FILIAL+PH9_CONTRA+PH9_REVISA+PH9_TPOPER+DTOS(PH9_DTOPER)+PH9_SEQ     
    If cStatus == " "
        If PH9->(DbSeek(cChave))
            PH9->(RecLock("PH9", .F.))
        Else
            PH9->(RecLock("PH9", .T.))
            PH9->PH9_FILIAL := xFilial('PH9')
            PH9->PH9_CONTRA := cContrato
            PH9->PH9_REVISA := cFim
            PH9->PH9_SEQ    := cSeq 
            PH9->PH9_TPOPER := cTpOper
            PH9->PH9_OBS    := cObs
        EndIf
        If cTpOper $ "000002|000003|000006|000011|000012|000031"			// Tratamento especifico para Cancelamento e Bonificação
            PH9->PH9_COMPET := cCompet
        Else
            PH9->PH9_HORAOP := cHrOper
            PH9->PH9_DTOPER := dDtOper
        EndIf
        PH9->PH9_HRINI	:= cHrOper
        PH9->PH9_DTINI	:= dDtOper
    Else
        PH9->(DbSeek(cChave)) 
        PH9->(RecLock("PH9", .F.))
    EndIf
    PH9->PH9_STATUS := cStatusPH9

    If ! cStatus $ " 15"
        PH9->PH9_REVISA := cRevisa
        PH9->PH9_HRFIM	:= Time()
	    PH9->PH9_DTFIM	:= MsDate()
    EndIf
    PH9->(MsUnLock())
    PH9->(DbCommit())
Return

User Function GCVA095R(cContrato, aAMSave)
    Local cRevisa    := ""
	Local lDelPH5    := .T.
	Local cMsgErro   := ""
    
    Default aAMSave    := {}
    Default cContrato  := Space(len(CN9->CN9_NUMERO))
    
    If IsInCallStack("U_TGCVA001")
        cContrato := (cAliasTrb)->CN9_NUMERO
        cRevisa   := (cAliasTrb)->CN9_REVISA
    EndIf
    Private lAbortPrint := .F.

    If Empty(aAMSave) .or. Empty(cContrato)
        If ! PegaAnoMeses(aAMSave, @cContrato )
            Return
        EndIf
        If ! MsgYesNo("Confirma a recalculo do cronograma Financeiro?")
            Return
        EndIf
    EndIf
   
    cRevisa := LoadCN9(cContrato)
    //             GCVA095A(cContrato, aAMSave, cMsgErro, cIdProc, lItensVenc, lCancelado, lCarenBoni, lDownGrade, cMsgRet, lDelPH5, lOnlyRev, cUserId) 
	Processa({|| U_GCVA095A(cContrato, aAMSave, @cMsgErro, NIL   , NIL       , NIL       , NIL       , NIL       , NIL    , lDelPH5 ) } ,,, .T.) 

    If ! Empty(cMsgErro)
        MsgAlert(cMsgErro)
    EndIf

    If lAbortPrint
        MsgAlert("Calculo  interrompida!")
    Else
        MsgAlert("Recalculo Finalizado!")
    EndIf
Return 

Static Function PegaAnoMeses(aAMSave, cContrato)
    Local aParamBox := {}
    Local cCmpDe    := AMtoCmp(Left(Dtos(Date()), 6))
    Local cCmpAte   := AMtoCmp(Left(Dtos(Date()), 6))
    Local cAMDe     := ""
    Local cAMAte    := ""
    Local aRet      := {}
    Local cTitulo   := "Recalculo - Cronograma Financeiro"
    Local lCanSave  := .F.

    MV_PAR01:=""
    MV_PAR02:=""
    MV_PAR03:=""
   
    aAdd(aParamBox,{1,"Contrato"       , cContrato, "@!", "NaoVazio()",,, 50, .T.})
    aAdd(aParamBox,{1,"Competencia de" , cCmpDe   , "99/9999", "NaoVazio()",,, 30, .T.})
    aAdd(aParamBox,{1,"Competencia Até", cCmpAte  , "99/9999", "NaoVazio()",,, 30, .T.})

    If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
        Return .F.
    EndIf
    cContrato := Padr(aRet[1], LEN(CN9->CN9_NUMERO))
	If Left(cContrato, 3)  <> "CON" 			
		cContrato := Padr("CON" + cContrato, 15)
	EndIf	
    cAMDe  := CmptoAM(Alltrim(aRet[2]))
    cAMAte := CmptoAM(Alltrim(aRet[3]))
       
    While cAMDe <= cAMAte
        aadd(aAMSave, cAMDe)
        cAMDe := CmpToAM(AdicMes(AMtoCmp(cAMDe), 1))
    End 

Return .T.



Static Function ApagaPH5(cContra, cRevisa, aAMSave)
    Local cTMP     := ""
    Local aArea    := GetArea()
    Local aAreaPH5 := PH5->(GetArea())
    Local cLstAM   := ""
    Local nx       := 0

    For nx:= 1 to len(aAMSave)
        cLstAM += aAMSave[nx] 
        If nx < len(aAMSave)
            cLstAM += ","
        EndIf
    Next

    cTMP := MtaQryPH5(cContra, cRevisa, cLstAM)
    If Empty(cTMP)
        Return
    EndIf

    PWH->(DbSetOrder(3)) //PWH_FILIAL+PWH_CONTRA+PWH_NUMERO+PWH_ITEM+PWH_AMCRON+PWH_SEQ+PWH_CLIENT+PWH_LOJA+PWH_ANOMES                                                                     

    While  (cTMP)->(! Eof())

        PH5->(DbGoto((cTMP)->RECNO)) 
        DeletePWH()

        PH5->(RecLock("PH5", .F.))
        PH5->(DbDelete())
        PH5->(MsUnLock())

        (cTMP)->(DbSkip())
    End
    (cTMP)->(DbCloseArea())
    RestArea(aAreaPH5)
    RestArea(aArea)    

Return 

Static Function DeletePWH()
    Local cChvPWH    := FwxFilial("PWH") + PH5->(PH5_CONTRA + PH5_NUMERO + PH5_ITEM + PH5_ANOMES + PH5_SEQ + PH5_CLIENT + PH5_LOJA)
    Local aRecDelPWH := {}
    Local ni         := 0
    
    PWH->(DbSetOrder(3)) //PWH_FILIAL+PWH_CONTRA+PWH_NUMERO+PWH_ITEM+PWH_AMCRON+PWH_SEQ+PWH_CLIENT+PWH_LOJA+PWH_ANOMES                                                                     
    PWH->(DbSeek(cChvPWH))

    While PWH->(!Eof()) .and. cChvPWH == PWH->(PWH_FILIAL + PWH_CONTRA + PWH_NUMERO + PWH_ITEM + PWH_AMCRON + PWH_SEQ + PWH_CLIENT + PWH_LOJA)
        If PWH->PWH_TIPO == "4"    
            If Empty(PWH->PWH_PEDVEN)
                AADD(aRecDelPWH, PWH->(Recno()))
            EndIf
        EndIf
        PWH->(DbSkip())
    End

    For ni:= 1 to Len(aRecDelPWH)
        PWH->(DbGoTo(aRecDelPWH[ni]))

        PWH->(RecLock("PWH", .F.))
            PWH->(DbDelete())
        PWH->(MsUnLock())
    Next

    aSize(aRecDelPWH, 0)
    aRecDelPWH := Nil
Return


Static Function MtaQryPH5(cContra, cRevisa, cLstAM)
    Local cQuery    := ""
    Local cTMP      := GetNextAlias()
        
    cQuery := " "

    cQuery += " SELECT R_E_C_N_O_ RECNO FROM " + RetSqlName("PH5") + " " 
    cQuery += " WHERE PH5_FILIAL = '" + FwxFilial("PH5") + "' " 
    cQuery += "    AND PH5_CONTRA = '" + cContra + "' " 
    cQuery += "    AND PH5_REVISA = '" + cRevisa + "' " 
    cQuery +=  "   AND PH5_ANOMES IN "+ FormatIn(cLstAM, ",") + " "    //Status do item
    cQuery += "    AND PH5_NUMMED = ' ' " 
    cQuery += "    AND PH5_PEDVEN = ' ' " 
    cQuery += "    AND PH5_NOTA = ' ' " 
    cQuery += "    AND D_E_L_E_T_ = ' ' " 
 
    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return ""
    EndIf

Return cTMP

Static Function ApagaPH6(cContra, cRevisa, aAMSave)
    Local cTMP     := ""
    Local aArea    := GetArea()
    Local aAreaPH6 := PH6->(GetArea())
    Local cLstAM   := ""
    Local nx       := 0

    For nx:= 1 to len(aAMSave)
        cLstAM += aAMSave[nx] 
        If nx < len(aAMSave)
            cLstAM += ","
        EndIf
    Next

    cTMP := MtaQryPH6(cContra, cRevisa, cLstAM)
    If Empty(cTMP)
        Return
    EndIf

    While  (cTMP)->(! Eof())

        PH6->(DbGoto((cTMP)->RECNO)) 
        PH6->(RecLock("PH6", .F.))
        PH6->(DbDelete())
        PH6->(MsUnLock())

        (cTMP)->(DbSkip())
    End
    (cTMP)->(DbCloseArea())
    RestArea(aAreaPH6)
    RestArea(aArea)    

Return 

Static Function MtaQryPH6(cContra, cRevisa, cLstAM)
    Local cQuery    := ""
    Local cTMP      := GetNextAlias()
        
    cQuery := " "
    cQuery += " SELECT R_E_C_N_O_ RECNO FROM " + RetSqlName("PH6") + " " 
    cQuery += " WHERE PH6_FILIAL = '" + FwxFilial("PH6") + "' " 
    cQuery += "    AND PH6_CONTRA = '" + cContra + "' " 
    cQuery += "    AND PH6_REVISA = '" + cRevisa + "' " 
    cQuery +=  "   AND PH6_ANOMES IN "+ FormatIn(cLstAM, ",") + " "    //Status do item
    cQuery += "    AND PH6_NUMMED = ' ' " 
    cQuery += "    AND PH6_PEDVEN = ' ' " 
    cQuery += "    AND PH6_NOTA = ' ' " 
    cQuery += "    AND D_E_L_E_T_ = ' ' " 
 
    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return ""
    EndIf

Return cTMP

Static Function ApagaPH7(cContra, cRevisa, aAMSave)
    Local cTMP     := ""
    Local aArea    := GetArea()
    Local aAreaPH7 := PH7->(GetArea())
    Local cLstAM   := ""
    Local nx       := 0

    For nx:= 1 to len(aAMSave)
        cLstAM += aAMSave[nx] 
        If nx < len(aAMSave)
            cLstAM += ","
        EndIf
    Next

    cTMP := MtaQryPH7(cContra, cRevisa, cLstAM)
    If Empty(cTMP)
        Return
    EndIf

    While  (cTMP)->(! Eof())

        PH7->(DbGoto((cTMP)->RECNO)) 
        PH7->(RecLock("PH7", .F.))
        PH7->(DbDelete())
        PH7->(MsUnLock())

        (cTMP)->(DbSkip())
    End
    (cTMP)->(DbCloseArea())
    RestArea(aAreaPH7)
    RestArea(aArea)    

Return 

Static Function MtaQryPH7(cContra, cRevisa, cLstAM)
    Local cQuery    := ""
    Local cTMP      := GetNextAlias()
        
    cQuery := " "
    cQuery += " SELECT R_E_C_N_O_ RECNO FROM " + RetSqlName("PH7") + " " 
    cQuery += " WHERE PH7_FILIAL = '" + FwxFilial("PH7") + "' " 
    cQuery += "    AND PH7_CONTRA = '" + cContra + "' " 
    cQuery += "    AND PH7_REVISA = '" + cRevisa + "' " 
    cQuery +=  "   AND PH7_ANOMES IN "+ FormatIn(cLstAM, ",") + " "    //Status do item
    cQuery += "    AND D_E_L_E_T_ = ' ' " 
 
    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return ""
    EndIf

Return cTMP
/*
===========================================================================================================
==   funcoes auxiliares
===========================================================================================================
*/

Static Function AMtoCmp(cAM)
    Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp) 
    Local cAM := Right(cCmp, 4) + Left(cCmp, 2)  

Return cAM

Static Function GrvArq(cArquivo, cLinha, lCRLF)
    Local nHandle
    Default lCRLF:= .T.

    If ! File(cArquivo)
        If (nHandle := MSFCreate(cArquivo, 0)) == -1
            Return
        EndIf
    Else
        If (nHandle := FOpen(cArquivo, 2)) == -1
            Return
        EndIf
    EndIf
    FSeek(nHandle, 0, 2)
    FWrite(nHandle, cLinha + If(lCRLF, CRLF, ""))
    FClose(nHandle)
Return

Static Function AdicMes(cMesAno, nMes)
    Local cMes := ""
    Local cAno := ""
    Local cMAAux := cMesAno
    Local nx

    If nMes > 0
        For nx := 1 to nMes
            cMes:= Left(cMAAux, 2)
            cAno:= Right(cMAAux, 4)
            If cMes == '12'
                cMAAux := '01/' + Soma1(cAno)
            Else
                cMAAux := Soma1(cMes) + '/' + cAno
            EndIf
        Next
    ElseIf nMes < 0
        For nx := nMes to -1 
            cMes:= Left(cMAAux, 2)
            cAno:= Right(cMAAux, 4)
            If cMes == '01'
                cMAAux := '12/' + Tira1(cAno)
            Else
                cMAAux := Tira1(cMes) + '/' + cAno
            EndIf
        Next
    EndIf

Return cMAAux


User Function GCVA095X(cContrato) 
    Local aParamBox  := {}
    Local aRet       := {}
    Local cTitulo    := "Roolback Contrato"
    Local lCanSave   := .F.
    Local cRet       := ""
    
    Default cContrato  := Space(LEN(CN9->CN9_NUMERO))

    If Empty(cContrato)
        MV_PAR01:=""

        aAdd(aParamBox,{1,"Contrato",	cContrato, "@!", "NaoVazio()",,, 50, .T.})

        If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
            Return ""
        EndIf
        cContrato := Padr(aRet[1], LEN(CN9->CN9_NUMERO))

        If Left(cContrato, 3)  <> "CON"
            cContrato := Padr("CON" + cContrato, 15)
        EndIf

        If ! MsgYesNO("Confirma o RoolBack do contrato " + AllTrim(cContrato) + "?")
            Return ""
        EndIf
        CN9->(dbSetOrder(7))// Posiciona na Revisão Atual
  	    If ! CN9->(dbSeek(xFilial("CN9") + cContrato + "05"))
            MsgAlert("Contrato: " + cContrato + " não encontrado na CN9!")
            Return 
        EndIf
        
        Processa({|| cRet := RollBack(cContrato) } ,,, .T.) 

        If cRet <> "OK" 
            MsgAlert(cRet)
        EndIf 

    Else 
    
        cContrato := Padr(cContrato, LEN(CN9->CN9_NUMERO))

        CN9->(dbSetOrder(7)) // Posiciona na Revisão Atual
        If ! CN9->(dbSeek(xFilial("CN9") + cContrato + "05"))
            Return "Contrato: " + cContrato + " não encontrado na CN9!"
        EndIf

        cRet := RollBack(cContrato)

    EndIf 


Return cRet 

Static Function RollBack(cContrato) 
    Local cRevisa    := ""
    Local cRevAnt    := ""
    Local nRecAtu    := 0
    Local nRecAnt    := 0
    Local cChaveLock := ""
    Local cMsgLock   := ""

    
    CN9->(dbSetOrder(1))

    LoadCN9(cContrato, @nRecAtu, @nRecAnt)
    
    cChaveLock:= "CN9"+xFilial("CN9")+CN9->CN9_NUMERO

    Begin Transaction
        
        If ! U_GVFUNLOC(1, cChaveLock, , .F.)
            cMsgLock := "Contrato: " + cContrato + " bloqueado por outra estação"
            Break
        EndIf

        CN9->(DbGoto(nRecAtu))
        CN9->(RecLock("CN9", .F.))
        CN9->CN9_SITUAC := "09"
        CN9->CN9_REVATU := ""
        CN9->(MsUnlock())
        cRevisa := CN9->CN9_REVISA

        If nRecAnt > 0
            CN9->(DbGoto(nRecAnt))
            CN9->(RecLock("CN9", .F.))
            CN9->CN9_SITUAC := "05"
            CN9->CN9_REVATU := ""
            CN9->(MsUnlock())
            cRevAnt := CN9->CN9_REVISA
        EndIf 

        DelContrato(cContrato, cRevisa, cRevAnt)

    End Transaction

    
    If ! Empty(cMsgLock)            
        Return cMsgLock
    Else
        U_GVFUNLOC(2, cChaveLock)  // Destrava o contrato
    EndIf
    

Return "OK"


Static Function  DelContrato(cContrato, cRevisa, cRevAnt)
    Local aArqs   := {}
    Local aArqsAtu:= {}
    Local aUniNeg := {}
    Local nx

    
    aadd(aArqs, {"CN9", "CN9_FILIAL + CN9_NUMERO + CN9_REVISA", "CN9_REVISA", 1}) 
    aadd(aArqs, {"CNA", "CNA_FILIAL + CNA_CONTRA + CNA_REVISA", "CNA_REVISA", 1}) 
    aadd(aArqs, {"CNB", "CNB_FILIAL + CNB_CONTRA + CNB_REVISA", "CNB_REVISA", 1}) 
    aadd(aArqs, {"CNC", "CNC_FILIAL + CNC_NUMERO + CNC_REVISA", "CNC_REVISA", 1}) 
    aadd(aArqs, {"PH3", "PH3_FILIAL + PH3_CONTRA + PH3_REVISA", "PH3_REVISA", 1}) 
    aadd(aArqs, {"PH4", "PH4_FILIAL + PH4_CONTRA + PH4_REVISA", "PH4_REVISA", 1}) 
    aadd(aArqs, {"PHG", "PHG_FILIAL + PHG_CONTRA + PHG_REVISA", "PHG_REVISA", 1}) 
    aadd(aArqs, {"PHN", "PHN_FILIAL + PHN_CONTRA + PHN_REVISA", "PHN_REVISA", 1}) 
    aadd(aArqs, {"PH9", "PH9_FILIAL + PH9_CONTRA + PH9_REVISA", "PH9_REVISA", 1}) 
    aadd(aArqs, {"PN0", "PN0_FILIAL + PN0_CONTRA + PN0_REVISA", "PN0_REVISA", 1})
    aadd(aArqs, {"PHA", "PHA_FILIAL + PHA_CONTRA + PHA_REVISA", "PHA_REVISA", 1}) 
    aadd(aArqs, {"PHV", "PHV_FILIAL + PHV_CONTRA + PHV_REVISA", "PHV_REVISA", 1}) 
    aadd(aArqs, {"PHH", "PHH_FILIAL + PHH_CONTRT + PHH_REVCON", "PHH_REVCON", 4}) 
    aadd(aArqs, {"PHD", "PHD_FILIAL + PHD_CONTRA + PHD_REVISA", "PHD_REVISA", 2}) 
    aadd(aArqs, {"PH5", "PH5_FILIAL + PH5_CONTRA + PH5_REVISA", "PH5_REVISA", 1}) 
    aadd(aArqs, {"PH6", "PH6_FILIAL + PH6_CONTRA + PH6_REVISA", "PH6_REVISA", 1}) 
    aadd(aArqs, {"PH7", "PH7_FILIAL + PH7_CONTRA + PH7_REVISA", "PH7_REVISA", 1})  

    //PHB NÃƒO REVISA
    //PHP NÃƒO REVISA
    //PHO NÃƒO REVISA
    //PH8 NÃƒO REVISA

    aadd(aArqsAtu, {"CND", "CND_FILIAL + CND_CONTRA", "CND_REVISA", 1 }) //CND_FILIAL+CND_CONTRA+CND_REVISA+CND_NUMERO+CND_NUMMED           INDICE 1
    aadd(aArqsAtu, {"CNE", "CNE_FILIAL + CNE_CONTRA", "CNE_REVISA", 1 }) //CNE_FILIAL+CNE_CONTRA+CNE_REVISA+CNE_NUMERO+CNE_NUMMED+CNE_ITEM  INDICE 1
    aadd(aArqsAtu, {"CXN", "CXN_FILIAL + CXN_CONTRA", "CXN_REVISA", 1 }) //CXN_FILIAL+CXN_CONTRA+CXN_REVISA+CXN_NUMMED+CXN_NUMPLA           INDICE 1 
    aadd(aArqsAtu, {"SC5", "C5_FILIAL  + C5_MDCONTR", "C5_XMDREV" , 8 }) //C5_FILIAL+C5_MDCONTR+C5_XMDREV+C5_MDPLANI+C5_XCOMPET+C5_CLIENTE+C5_LOJACLI+C5_CONDPAG  INDICE 8Â´
       
	aUniNeg := {}
	CNB->(DbSetOrder(1))
	CNB->(Dbseek(xFilial("CNB") + cContrato +  cRevisa))
	While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA + CNB_REVISA == xFilial("CNB") + cContrato  + cRevisa )
		If Ascan(aUniNeg, CNB->CNB_UNINEG) == 0
 			Aadd(aUniNeg, CNB->CNB_UNINEG)
		EndIf
		CNB->(DbSkip())
	End 

    If ! IsBlind()
        ProcRegua(1)
    EndIf 
    For nx := 1 to len(aArqs)
        cAlias   := aArqs[nx, 1]
        nOrdem   := aArqs[nx, 4]

        (cAlias)->(DelRevisa(cAlias, nOrdem, cContrato, cRevisa))
    Next

    For nx := 1 to len(aArqsAtu)
        cAlias   := aArqsAtu[nx, 1]
        cNameKey := aArqsAtu[nx, 2]
        cNameRev := aArqsAtu[nx, 3]
        nOrdem   := aArqsAtu[nx, 4]

        If ! IsBlind()
           IncProc("Atualizando documentos Tabela: " + cAlias )
        EndIf
        
        (cAlias)->(AtuRevisa(cAlias, cNameKey, nOrdem, aUniNeg, cContrato, cNameRev, cRevAnt ))
    Next


Return 

Static Function DelRevisa(cAlias, nOrdem, cContrato, cRevisa)
    Local aArea   := GetArea()
    Local cChave  := xFilial(cAlias) + cContrato + cRevisa
    Local nLimite := 100

    DbSetOrder(nOrdem)
    DbSeek(cChave)
    While DbSeek(cChave)
        If ! IsBlind()
            If nLimite == 100
                IncProc("Deletando revisaão contrato: " + cContrato + " Tabela: " + cAlias + ":" + Alltrim(Str((cAlias)->(Recno()))))
                ProcessMessage()
                nLimite := 0
            EndIf
            nLimite++
        EndIf
        RecLock(cAlias, .F.)
        DbDelete()
        MsUnLock()
    End
    RestArea(aArea)

Return 

Static Function AtuRevisa(cAlias, cNameKey, nOrdem, aUniNeg, cContrato, cNameRev, cRevAnt )
	Local nx      := 0
	Local cChave  := ""
	Local aArea   := GetArea()
	Local aRec    := {}

	For nx := 1 to len(aUniNeg)
		cChave := aUniNeg[nx] + cContrato
		DbSetOrder(nOrdem)
		Dbseek(cChave)
		While ! Eof() .and. &cNameKey == cChave 
			aadd(aRec, Recno())
			DbSkip()
		End
	Next

	For nx := 1 to len(aRec)
		DbGoto(aRec[nx])
		RecLock(cAlias, .F.)
		FieldPut(FieldPos(cNameRev), cRevAnt)
		MsUnLock()
	Next

	RestArea(aArea)
Return

