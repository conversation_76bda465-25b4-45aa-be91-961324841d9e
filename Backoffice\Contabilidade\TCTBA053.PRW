#INCLUDE 'TOTVS.ch'
#INCLUDE 'FWMVCDef.ch'
#INCLUDE 'FWEditPanel.CH'
#INCLUDE "TRYEXCEPTION.CH"


/*/{Protheus.doc} User Function TCTBA053
	Funcao da querie de entrada ronaldo oTIIntercompany,::cProcesso,::cCompet,cRule,cRuleEntry,cUserName
	@type  Function
	<AUTHOR> Menabue Lima
	@since 10/08/2022
	@version 12.1.33
	
	/*/
User Function TCTBA053(c_EmpAnt, c_FilAnt,aParame,cThreNa)


	Local oInter
	Local cQuery 	:= " "
	Local aRetParam	:= {}
	Local cCnpjs    := ""
	Local cLpSeqent := ""
	Local cLpSeqSAI := ""
	Local cAlsCTK   := GetNextAlias()
	Local cFilRMV 	:= ""
	Local lSaida    := .F.
	Local lJob 		:= isBlind()
	Local oError
	Local cMsgFault := ""
	Local oBulkEnt
	Local aStrFld 	:= {}
	Local cContaNot := ""
	Local aEmpCT2   := {}
	Local nI		:= 0
	Private aCgcDre := {}
	Private aParam	:= aParame
	Private cDataIni:= Stod(Substr(aParam[3],3,4)+Substr(aParam[3],1,2)+"01")
	Private cThread := cThreNa
	Private cParam  := aParame[8]
	If  lJob
		RpcSetEnv(c_EmpAnt, c_FilAnt)
		RpcSetType(3)
		oInter    :=  TIINTERCOMPANY():New(aParame[2],aParame[3])
		PutGlbValue(cThread, "Em Execução")
	EndIF
	TRYEXCEPTION
	//Empresas e Suas Dre's
	aCgcDre   := oInter:GetEmpCgcDre()
	oBulkEnt  := FWBulk():New(RetSqlName("P57"))
	aEmpCT2   := SEPARA(GetMV("TI_INTEMP",.F.,"000;600") ,  ";", .T. )
	cLpSeqent := FormatIn(GetMV("TI_LPSEQEN",.F.,"660002;660003;660004;660005;665005;665004;665003"),";")
	cContaNot := FormatIn(GetMV("TI_INTCTAN",.F.,"420901010102"),";")
	cLpSeqSAI := FormatIn(GetMV("TI_CTBA53P",.F.,"610002;610003;610004;610005;610006;630002;630003;630004;630006;650007;650005;650006") ,";")
	cParalel  := " " // GetMV("TI_LPSEQSA",.F.," /*PARALLEL(8)*/ ")
	cAlsCTK   := GetNextAlias()
	cFilRMV   := FormatIn( GetMv("TI_INTRMVF",,"93601000100"),";")
	FWMonitorMsg( cThread+" RODANDO QUERIE " )
	cCnpjs :=oInter:GetCompany(14)
	cCnpjs +=oInter:GetCompany(11)
	cInCod:= oInter:GetCompany(6)
	Aadd(aRetParam,cDataIni)
	Aadd(aRetParam,LastDay(cDataIni,0))



	IF("RECEITA" $ cParam)
		cBetween:=getPer(aParam)
		cColumns := " E1_FILIAL FILIAL,  "
		cColumns += " D2_CLIENTE CODIGO,
		cColumns += " A1_CGC CGC,
		cColumns += " E1_NOMCLI NOME, "
		cColumns += " 'R' TIPO_MOV, "
		cColumns += " D2_EMISSAO EMISSAO, "
		cColumns += " D2_SERIE SERIE, "
		cColumns += " E1_TIPO TIPO, "
		cColumns += " F2_NFELETR NFELETR, "
		cColumns += " D2_DOC DOC,"
		cColumns += " E1_VENCREA VENCTO , "
		cColumns += " E1_BAIXA BAIXA, "
		cColumns += " E1_VALOR  VALOR, "
		cColumns += " CT2_FILIAL, "
		cColumns += " CT2_DATA, "
		cColumns += " CT2_LOTE  LOTE, "
		cColumns += " CT2_HIST, "
		cColumns += " CT2_CCD, "
		cColumns += " CT2_CCC, "
		cColumns += " CT2_ITEMD, "
		cColumns += " CT2_ITEMC, "
		cColumns += " CT2_CLVLDB, "
		cColumns += " CT2_CLVLCR, "
		cColumns += " CASE WHEN CT2_DC= '1' THEN 'DEBITO' WHEN CT2_DC= '2' THEN 'CREDITO' WHEN CT2_DC= '3' THEN 'PARTIDA_DOBRADA' ELSE 'DESCONHECIDO' END TIPO_LANCAMENTO, "
		cColumns += " CT2_DEBITO, "
		cColumns += " CT2_CREDIT, "
		cColumns += " CT2_VALOR,   "
		cColumns += " CT2_DC ,   "
		cColumns += " CT2_SBLOTE,   "
		cColumns += " CT2_DOC,   "
		cColumns += " CT2_LINHA,  "
		cColumns += " CT2_LP  ,"
		cColumns += " ' ' FLEX05 "
		For nI:= 1 to len(aEmpCT2)
			If(nI > 1 )
				cQuery += " UNION "
			EndIF
			cQuery += " SELECT "+cParalel+" DISTINCT   "+cColumns
			cQuery += " FROM   CTK" + aEmpCT2[nI] + " A   "
			cQuery += " INNER JOIN SD2" + aEmpCT2[nI] + " B ON TO_NUMBER(A.CTK_RECORI) = B.R_E_C_N_O_   "
			cQuery += "        AND A.CTK_FILIAL = B.D2_FILIAL   "
			cQuery += "        AND A.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN SF2" +aEmpCT2[nI] + " C ON C.F2_FILIAL = B.D2_FILIAL   "
			cQuery += "        AND C.F2_DOC = B.D2_DOC   "
			cQuery += "        AND C.F2_SERIE = B.D2_SERIE   "
			cQuery += "        AND C.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN SE1" + aEmpCT2[nI] + " D ON D.E1_FILIAL = C.F2_FILIAL   "
			cQuery += "        AND D.E1_NUM = C.F2_DOC   "
			cQuery += "        AND C.F2_SERIE = D.E1_PREFIXO   "
			cQuery += "        AND D.E1_TIPO = 'NF'   "
			cQuery += "        AND D.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN CT2" + aEmpCT2[nI] + " E ON E.CT2_FILIAL = A.CTK_FILIAL   "
			cQuery += "        AND TO_NUMBER(A.CTK_RECDES) = E.R_E_C_N_O_   "
			cQuery += "        AND E.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN SA1" + aEmpCT2[nI] + " G ON A1_COD = E1_CLIENTE   "
			cQuery += "        AND A1_LOJA = E1_LOJA   "
			cQuery += "        AND G.D_E_L_E_T_ <> '*'   "
			cQuery += " WHERE  A.CTK_TABORI IN ('SD2', 'SE1')   "
			cQuery += "        AND A.D_E_L_E_T_ <> '*'   "
			cQuery += "        AND A.CTK_DATA BETWEEN "+cBetween
			//	cQuery += "        AND A.CTK_DATA <= '"+DTOS(aRetParam[2])+"'   "
			cQuery += "        AND CTK_FILIAL <> ' '   "
			cQuery += "        AND C.F2_FILIAL <> ' '   "
			cQuery += "        AND D.E1_FILIAL <> ' '  "
			cQuery += "        AND E.CT2_FILIAL <> ' '   "
			cQuery += "        AND B.D2_FILIAL <> ' '   and A.CTK_RECDES <> ' '  and CTK_RECORI <> ' '  "
			cQuery += "   AND (
			cQuery += " 	A1_CGC IN (" + cCnpjs + ")
			if(!Empty(Alltrim(cInCod)))
				cQuery += "   OR  A1_COD IN (" + cInCod + ")
			EndIF
			cQuery += "       ) AND CTK_LP || CTK_LPSEQ  NOT IN "+cLpSeqSAI+" "
			IF(!Empty(Alltrim(cContaNot)))
				cQuery += "  AND ( CT2_DEBITO  NOT IN "+cContaNot+" AND  CT2_CREDIT  NOT IN "+cContaNot+" ) "
			EndIF
		Next nI
		//cQuery += "UNION "
	ElseIF("DESPESA" $ cParam)
		cBetween:=getPer(aParam)

		cColumns := " F1_FILIAL FILIAL,  "
		cColumns += " A2_COD CODIGO,
		cColumns += " A2_CGC CGC,
		cColumns += " A2_NOME NOME, "
		cColumns += " 'D' TIPO_MOV, "
		cColumns += " F1_EMISSAO EMISSAO, "
		cColumns += " F1_SERIE SERIE, "
		cColumns += " E2_TIPO TIPO, "
		cColumns += " F1_DOC DOC, "
		cColumns += " F1_NFELETR NFELETR, "
		cColumns += " E2_VENCREA VENCTO , "
		cColumns += " E2_BAIXA BAIXA, "
		cColumns += " E2_VALOR VALOR , "
		cColumns += " CT2_FILIAL, "
		cColumns += " CT2_DATA, "
		cColumns += " CT2_LOTE LOTE, "
		cColumns += " CT2_HIST, "
		cColumns += " CT2_CCD, "
		cColumns += " CT2_CCC, "
		cColumns += " CT2_ITEMD, "
		cColumns += " CT2_ITEMC, "
		cColumns += " CT2_CLVLDB, "
		cColumns += " CT2_CLVLCR, "
		cColumns += " CASE WHEN CT2_DC= '1' THEN 'DEBITO' WHEN CT2_DC= '2' THEN 'CREDITO' WHEN CT2_DC= '3' THEN 'PARTIDA_DOBRADA' ELSE 'DESCONHECIDO' END TIPO_LANCAMENTO, "
		cColumns += " CT2_DEBITO, "
		cColumns += " CT2_CREDIT, "
		cColumns += " CT2_VALOR,   "
		cColumns += " CT2_DC ,   "
		cColumns += " CT2_SBLOTE,   "
		cColumns += " CT2_DOC,   "
		cColumns += " CT2_LINHA,   "
		cColumns += " CT2_LP  ,"
		cColumns += " ' ' FLEX05 "
		For nI:= 1 to len(aEmpCT2)
			If(nI > 1 )
				cQuery += " UNION "
			EndIF
			cQuery += " SELECT "+cParalel+" DISTINCT  "+cColumns

			cQuery += " FROM   CTK" + aEmpCT2[nI] + " A   "
			cQuery += " INNER JOIN SF1" + aEmpCT2[nI] + " C ON C.F1_FILIAL = A.CTK_FILIAL   "
			cQuery += "        AND TO_NUMBER(A.CTK_RECORI) = C.R_E_C_N_O_  and C.D_E_L_E_T_ <> '*'   "
			cQuery += " LEFT JOIN SE2" + aEmpCT2[nI] + " B ON B.E2_FILIAL = C.F1_FILIAL AND B.E2_NUM = C.F1_DOC AND B.E2_PREFIXO = C.F1_SERIE AND B.E2_FORNECE = C.F1_FORNECE AND B.E2_LOJA = F1_LOJA AND E2_PARCELA IN ('   ','001') AND E2_TIPO = 'NF' and   B.D_E_L_E_T_ <> '*'    "
			cQuery += " INNER JOIN CT2" + aEmpCT2[nI] + " E ON E.CT2_FILIAL = A.CTK_FILIAL   "
			cQuery += "        AND A.CTK_RECDES = E.R_E_C_N_O_   "
			cQuery += "        AND E.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN SA2" + aEmpCT2[nI] + " G ON A2_COD = F1_FORNECE   "
			cQuery += "        AND A2_LOJA = F1_LOJA   "
			cQuery += "        AND G.D_E_L_E_T_ <> '*'   "
			cQuery += " WHERE  A.CTK_TABORI IN ('SF1')   "
			cQuery += "        AND A.D_E_L_E_T_ <> '*'   "
			cQuery += "        AND A.CTK_DATA BETWEEN " + cBetween
			//	cQuery += "        AND A.CTK_DATA <= '"+DTOS(aRetParam[2])+"'   "
			cQuery += "        AND A.CTK_HIST   "
			cQuery += "        NOT LIKE '%LAB%'   "
			cQuery += " AND CTK_FILIAL <> ' ' AND C.F1_FILIAL <> ' '  AND E.CT2_FILIAL <> ' '  and A.CTK_RECDES <> ' ' and CTK_RECORI <> ' ' "
			cQuery += "        AND  (
			cQuery += " 	A2_CGC IN (" + cCnpjs + ")
			if(!Empty(Alltrim(cInCod)))
				cQuery += "   OR  A2_COD IN (" + cInCod + ")
			EndIF
			cQuery += "            ) AND CTK_LP || CTK_LPSEQ  NOT IN "+cLpSeqEnt+"  and CT2_HIST NOT LIKE 'EST%'"
			IF(!Empty(Alltrim(cContaNot)))
				cQuery += "  AND ( CT2_DEBITO  NOT IN "+cContaNot+" AND  CT2_CREDIT  NOT IN "+cContaNot+" ) "
			EndIF
			cQuery += " UNION  "

			cQuery += " SELECT "+cParalel+" DISTINCT  " +cColumns
			cQuery += " FROM   CTK" +aEmpCT2[nI] + " A   "
			cQuery += " INNER JOIN SD1" + aEmpCT2[nI] + " L ON L.D1_FILIAL = A.CTK_FILIAL    "
			cQuery += "       AND TO_NUMBER(A.CTK_RECORI) = L.R_E_C_N_O_  and L.D_E_L_E_T_ <> '*'  "
			cQuery += " INNER JOIN SF1" + aEmpCT2[nI] + " C ON L.D1_FILIAL = C.F1_FILIAL and L.D1_DOC = C.F1_DOC and L.D1_SERIE = C.F1_SERIE and C.F1_FORNECE = L.D1_FORNECE and C.D_E_L_E_T_ <> '*'
			cQuery += " LEFT JOIN SE2" + aEmpCT2[nI] + " B ON B.E2_FILIAL = C.F1_FILIAL AND B.E2_NUM = C.F1_DOC AND B.E2_PREFIXO = C.F1_SERIE AND B.E2_FORNECE = C.F1_FORNECE AND B.E2_LOJA = F1_LOJA AND E2_PARCELA IN ('   ','001') AND E2_TIPO = 'NF' and B.D_E_L_E_T_ <> '*'  "
			cQuery += " INNER JOIN CT2" + aEmpCT2[nI] + " E ON E.CT2_FILIAL = A.CTK_FILIAL   "
			cQuery += "        AND A.CTK_RECDES = E.R_E_C_N_O_   "
			cQuery += "        AND E.D_E_L_E_T_ <> '*'   "
			cQuery += " INNER JOIN SA2" + aEmpCT2[nI] + " G ON A2_COD = F1_FORNECE   "
			cQuery += "        AND A2_LOJA = F1_LOJA   "
			cQuery += "        AND G.D_E_L_E_T_ <> '*'   "
			cQuery += " WHERE  A.CTK_TABORI IN ('SD1')   "
			cQuery += "        AND A.D_E_L_E_T_ <> '*'   "
			cQuery += "        AND A.CTK_DATA BETWEEN " + cBetween
			//cQuery += "        AND A.CTK_DATA <= '"+DTOS(aRetParam[2])+"'   "
			cQuery += "        AND A.CTK_HIST   "
			cQuery += "        NOT LIKE '%LAB%'   "
			cQuery += " AND CTK_FILIAL <> ' ' AND C.F1_FILIAL <> ' '  AND E.CT2_FILIAL <> ' '  and A.CTK_RECDES <> ' ' and CTK_RECORI <> ' ' "
			cQuery += "        AND  (
			cQuery += " 	A2_CGC IN (" + cCnpjs + ")
			if(!Empty(Alltrim(cInCod)))
				cQuery += "   OR  A2_COD IN (" + cInCod + ") "
			EndIF
			cQuery += " ) AND CTK_LP || CTK_LPSEQ  NOT IN "+cLpSeqEnt+"  and CT2_HIST NOT LIKE 'EST%'"
			IF(!Empty(Alltrim(cContaNot)))
				cQuery += "  AND ( CT2_DEBITO  NOT IN "+cContaNot+" AND  CT2_CREDIT  NOT IN "+cContaNot+" ) "
			EndIF
		Next nI
	ElseIF("ATIVOS" $ cParam)
		lSaida:=.T.
		cAlsPa := GetNextAlias()
		cAls2Fil := GetNextAlias()
		cQuery := " "
		cQuery += " SELECT P57_FLEX02,P57_FLEX01 FROM " + RetSQLName("P57") + " WHERE  SUBSTR(P57_DEBITO,1,2) ='12' AND D_E_L_E_T_ = ' '  "
		If SELECT(cAlsPa) > 0
			(cAlsPa)->(dbCloseArea())
		EndIF

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsPa,.T.,.T.)

		(cAlsPa)->(DbGoTop())
		WHILE !(cAlsPa)->(EOF())
			cQuery := " "
			cQuery += " SELECT P57_ID FROM " + RetSQLName("P57") + " P571 WHERE  (P57_FLEX01 = '"+(cAlsPa)->P57_FLEX01+" ' OR P57_FLEX02 = '"+(cAlsPa)->P57_FLEX02+" ') AND D_E_L_E_T_ = ' '   "
			If SELECT(cAls2Fil) > 0
				(cAls2Fil)->(dbCloseArea())
			EndIF
			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAls2Fil,.T.,.T.)

			nLinha:=0
			(cAls2Fil)->(DbGoTop())
			WHILE !(cAls2Fil)->(EOF())

				cScript := " UPDATE   " + RetSQLName("P57") + " SET P57_STATUS ='4' ,P57_TRACKE = 'ELIMINADO LANC ATIVO'"
				cScript += " WHERE  SUBSTR(P57_DEBITO,1,2) ='12'  AND D_E_L_E_T_ =' ' "
				cScript += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
				cScript += " AND P57_ID ='"+(cAls2Fil)->P57_ID+"'   AND D_E_L_E_T_ = ' ' "
				IF TCSQLEXEC(cScript) <> 0
					FWMonitorMsg( cThread+ " error: " +CRLF + TCSqlError() )
				EndIF
				(cAls2Fil)->(DbSkip())
			EndDo
			(cAlsPa)->(DbSkip())
		EndDo
	ElseIF("SAIDA1" $ cParam)

		lSaida:=.t.
		/* // Despesas ORIGEM  BUSCANDO SUA RECEITA NO DESTINO ( O QUE NAO BATEU LANCA COMO RECEITA)*/


		cQuery := " SELECT SUM(VALOR) VALOR,CONTA,ORIGEM,DESTINO "
		cQuery += " FROM   ( "
		cQuery += " SELECT SUM(P57_VALOR)*-1 VALOR,P57_DEBITO CONTA,P57_CGCORI ORIGEM,P57_CGCDES DESTINO"
		cQuery += " 	FROM   " + RetSQLName("P57") + " D"
		cQuery += " WHERE  P57_TIPO ='D'  AND D_E_L_E_T_ =' ' AND P57_STATUS IN ('1','2') "
		cQuery += " 	AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cQuery += " 	AND P57_DC ='1' GROUP BY P57_DEBITO,P57_CGCORI,P57_CGCDES "
		cQuery += " 	UNION ALL"
		cQuery += " SELECT SUM(P57_VALOR) VALOR,P57_CREDIT CONTA,P57_CGCDES ORIGEM,P57_CGCORI DESTINO"
		cQuery += " 	FROM   " + RetSQLName("P57") + "  R"
		cQuery += " WHERE  P57_TIPO ='R' AND D_E_L_E_T_ =' ' AND P57_STATUS IN ('1','2') "
		cQuery += " 	AND P57_PROC='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cQuery += " 	AND P57_DC ='3' GROUP BY P57_CREDIT,P57_CGCORI,P57_CGCDES  )"
		cQuery += " GROUP BY CONTA,ORIGEM,DESTINO"
		cQuery += " ORDER BY  ORIGEM,DESTINO,CONTA"

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsCTK,.T.,.T.)
		Count To nTotal
		(cAlsCTK)->(DbGoTop())
		nLinha:=0
		While (cAlsCTK)->(!Eof())
			cStatus:="1"
			IF( (cAlsCTK)->VALOR ==0)
				cStatus:="3"
			Else
				cStatus:="2"
			EndIF
			//Atualiza as despesas
			cWhere := " P57_CGCORI='"+(cAlsCTK)->ORIGEM+"' AND P57_CGCDES ='"+(cAlsCTK)->DESTINO+"'"
			cWhere+= "  AND  P57_DEBITO ='"+ALLTRIM((cAlsCTK)->CONTA)+"'    "
			cWhere+= "  AND  P57_TIPO = 'D'     "
			cWhere+= "  AND  P57_DC ='1' AND P57_STATUS  IN ('1','2') AND D_E_L_E_T_ = ' ' "
			setStatus(cThread,cStatus,cWhere,' ',' ',' ')
			//Atualiza as receitas
			cWhere := " P57_CGCORI='"+(cAlsCTK)->DESTINO+"' AND P57_CGCDES ='"+(cAlsCTK)->ORIGEM+"'"
			cWhere+= "  AND  P57_CREDIT ='"+ALLTRIM((cAlsCTK)->CONTA)+"'    "
			cWhere+= "  AND  P57_TIPO = 'R'     "
			cWhere+= "  AND  P57_DC ='3'  AND P57_STATUS  IN ('1','2') AND D_E_L_E_T_ = ' '  "
			setStatus(cThread,cStatus,cWhere,' ',' ',' ')
			(cAlsCTK)->(DbSkip())
		EndDo

	ElseIF("SAIDA2" $ cParam)//Segunda regra de Saida apos executar a primeira
		cOverR:="R.P57_CGCORI,R.P57_FLEX01,R.P57_FLEX02,R.P57_CREDIT,R.P57_FILORI,R.P57_CCC,R.P57_ITEMC,R.P57_CLVLCR"
		cOverP:="P.P57_CGCORI,P.P57_FLEX01,P.P57_FLEX02,P.P57_CREDIT,P.P57_FILORI,P.P57_CCC,P.P57_ITEMC,P.P57_CLVLCR"

		lSaida:=.t.
		//Pega os registros de despesas que deram divergencia e compara o F1_DOC X F2_NFELETR PARA ACHAR AS NFS E GERAR O PARTIDA DOBRADA DA FILIAL CONCILIADORA
		//AMARRAR OS DOIS LANCAMENTOS NO PARTIDA DOBRADA PARA RASTREIO
		cQuery := "SELECT DISTINCT 'RPS '||LPAD(TRIM(R.P57_FLEX02),9,'0')||' NF '||TRIM(P.P57_FLEX02)  HISTORICO, "
		cQuery += " 'I' TIPO_MOV, "
		cQuery += " R.P57_CGCORI ORIGEM, " //Sempre a Origem sera a Receita
		cQuery += " P.P57_CGCORI DESTINO, "//Sempre o Destino sera a Despesa
		cQuery += " P.P57_FLEX02 F1_DOC, "
		cQuery += " R.P57_FLEX01 F2_NFELETR, "
		cQuery += " R.P57_FLEX02 F2_DOC, "
		cQuery += " P.P57_DEBITO CREDITO, "
		cQuery += " R.P57_CREDIT DEBITO, "
		cQuery += " LPAD(TRIM(R.P57_FLEX02),9,'0') RPS, "
		cQuery += " TRIM(P.P57_FLEX02) NF, "
		cQuery += " R.P57_FILORI FILREC,"
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " P.P57_VALOR VALOR ,"
		cQuery += " R.P57_VALOR VALORREC ,"
		cQuery += " P.P57_CCD,"
		cQuery += " R.P57_CCC,"
		cQuery += " P.P57_ITEMD,"
		cQuery += " R.P57_ITEMC,"
		cQuery += " P.P57_CLVLDB,"
		cQuery += " R.P57_CLVLCR, "
		cQuery += " P.P57_KEY KEYDESP, "
		cQuery += " R.P57_KEY KEYREC , "
		cQuery += " ' ' IDDESP, "
		cQuery += " ' ' IDREC,  "
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " R.P57_FILORI FILREC,  "
		cQuery += " R.R_E_C_N_O_  P57RECR,  "
		cQuery += "COUNT(*) OVER (PARTITION BY "+cOverR+") RECLIN,"
		cQuery += "SUM(R.P57_VALOR) OVER (PARTITION BY "+cOverR+") RECSUM,"
		cQuery += "COUNT(*) OVER (PARTITION BY "+cOverP+") DESPLIN,"
		cQuery += "SUM(R.P57_VALOR) OVER (PARTITION BY "+cOverP+") DESPSUM "
		cQuery += " 	FROM   " + RetSQLName("P57") + " P"
		cQuery += " LEFT JOIN " + RetSQLName("P57") + "  R "
		cQuery += "  ON R.P57_TIPO = 'R' "
		cQuery += " 	AND R.P57_DC IN ('3','2')  " //Alem de pegar a receita na partida dobrada pega o lancamento perneta do tipo 2=Receita TICONTIN-3030
		cQuery += " 	AND R.P57_STATUS  IN ('1','2')  "
		cQuery += " 	AND (CASE WHEN LENGTH(TRIM(R.P57_FLEX01)) < LENGTH(TRIM(P.P57_FLEX02)) THEN LPAD(TRIM(R.P57_FLEX01) , LENGTH(TRIM(P.P57_FLEX02)) , '0')  "
		cQuery += " 	ELSE LPAD(TRIM(R.P57_FLEX01) , LENGTH(TRIM(P.P57_FLEX02)) , '0') END ) = TRIM(P.P57_FLEX02) "
		cQuery += " 	AND R.P57_CGCDES =P.P57_CGCORI  "
		cQuery += "  	AND R.P57_PROC   =P.P57_PROC "
		cQuery += " 	AND R.P57_COMPET =P.P57_COMPET AND R.D_E_L_E_T_ = ' ' "
		cQuery += " WHERE  P.P57_TIPO = 'D' "
		cQuery += " AND P.P57_DC ='1' "
		cQuery += " AND P.P57_STATUS  IN ('1','2') "
		cQuery += " AND R.P57_CGCORI IS	NOT NULL "
		cQuery += " AND P.P57_PROC='"+aParam[2]+"' AND P.P57_COMPET  ='"+aParam[3]+"'  AND P.D_E_L_E_T_ = ' '"
		BulkSaida(cQuery,cThread,'1',oInter)
		//SegunDa etapa procura Notas de entrada vs Notas de saida sem NFELTR COM ENTIDADES CC,ITEM E CLASSE IGUAIS
		cQuery := "SELECT DISTINCT 'RPS '||LPAD(TRIM(R.P57_FLEX02),9,'0')||' NF '||TRIM(P.P57_FLEX02)  HISTORICO,"
		cQuery += " 'I' TIPO_MOV, "
		cQuery += " R.P57_CGCORI ORIGEM, " //Sempre a Origem sera a Receita
		cQuery += " P.P57_CGCORI DESTINO, "//Sempre o Destino sera a Despesa
		cQuery += " P.P57_FLEX02 F1_DOC, "
		cQuery += " R.P57_FLEX01 F2_NFELETR, "
		cQuery += " R.P57_FLEX02 F2_DOC, "
		cQuery += " P.P57_DEBITO CREDITO, "
		cQuery += " R.P57_CREDIT DEBITO, "
		cQuery += " LPAD(TRIM(R.P57_FLEX02),9,'0') RPS, "
		cQuery += " TRIM(P.P57_FLEX02) NF, "
		cQuery += " R.P57_FILORI FILREC,"
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " P.P57_VALOR VALOR ,"
		cQuery += " R.P57_VALOR VALORREC ,"
		cQuery += " P.P57_CCD,"
		cQuery += " R.P57_CCC,"
		cQuery += " P.P57_ITEMD,"
		cQuery += " R.P57_ITEMC,"
		cQuery += " P.P57_CLVLDB,"
		cQuery += " R.P57_CLVLCR, "
		cQuery += " P.P57_KEY KEYDESP, "
		cQuery += " R.P57_KEY KEYREC,  "
		cQuery += " ' ' IDDESP, "
		cQuery += " ' ' IDREC  ,"
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " R.P57_FILORI FILREC,  "
		cQuery += " R.R_E_C_N_O_ P57RECR,  "
		cQuery += "COUNT(*) OVER (PARTITION BY "+cOverR+") RECLIN,"
		cQuery += "SUM(R.P57_VALOR) OVER (PARTITION BY "+cOverR+") RECSUM,"
		cQuery += "COUNT(*) OVER (PARTITION BY "+cOverP+") DESPLIN,"
		cQuery += "SUM(R.P57_VALOR) OVER (PARTITION BY "+cOverP+") DESPSUM "
		cQuery += " 	FROM   " + RetSQLName("P57") + " P"
		cQuery += " LEFT JOIN " + RetSQLName("P57") + "  R "
		cQuery += "  ON R.P57_TIPO = 'R' "
		cQuery += " 	AND R.P57_DC IN ('3','2')  " //Alem de pegar a receita na partida dobrada pega o lancamento perneta do tipo 2=Receita TICONTIN-3030
		cQuery += " 	AND R.P57_STATUS  IN ('1','2')  "
		cQuery += " 	AND TRIM(R.P57_FLEX02) = TRIM(P.P57_FLEX02) "
		cQuery += " 	AND R.P57_CGCDES =P.P57_CGCORI  "
		cQuery += "  	AND R.P57_PROC   =P.P57_PROC   AND R.D_E_L_E_T_ = ' '"
		cQry1 := cQuery
		cQry2 := cQuery
		cQry1 += " 	AND R.P57_COMPET =P.P57_COMPET "
		cQry1 += " 	AND R.P57_CCC	 =P.P57_CCD
		cQry1 += " 	AND R.P57_ITEMC	 =P.P57_ITEMD
		cQry1 += " 	AND R.P57_CLVLCR =P.P57_CLVLDB
		cWhere := " WHERE  P.P57_TIPO = 'D' "
		cWhere += " AND P.P57_DC ='1' "
		cWhere += " AND P.P57_STATUS  IN ('1','2')  "
		cWhere += " AND R.P57_CGCORI IS	NOT NULL "
		cWhere += " AND P.P57_PROC='"+aParam[2]+"' AND P.P57_COMPET  ='"+aParam[3]+"'  AND P.D_E_L_E_T_ = ' '"
		cQry1  += cWhere
		cQry2  += cWhere
		//Entidades Iguais
		BulkSaida(cQry1,cThread,'2',oInter)
		//doc iguais mas nao olha Entidades
		BulkSaida(cQry2,cThread,'2',oInter)


	ElseIF("CREDITO1" $ cParam)//Primeira regra de saida olhando para as despesas parte credito que tem data de baixa
		lSaida:=.t.
		cScript := " UPDATE   " + RetSQLName("P57") + " SET P57_STATUS ='4', P57_TRACKE ='LANCAMENTO DO TIPO CREDITO COM DATA DE BAIXA, REGISTRO DESCONSIDERADO' "
		cScript += " WHERE  P57_TIPO ='D'  AND D_E_L_E_T_ =' ' "
		cScript += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cScript += " AND P57_DC ='2' AND P57_BAIXA <> ' '  AND D_E_L_E_T_ = ' ' "
		IF TCSQLEXEC(cScript) <> 0
			FWMonitorMsg( cThread+ " error: " +CRLF + TCSqlError() )
		EndIF
	ElseIF("CREDITO2" $ cParam)//Segunda regra de credito saida pega as despesas que nao tem data de baixa e cria um lancamento intercompany nao na empresa centralizadora mas na empresa que falta pagar a receita
		lSaida:=.t.
		//Pega os registros de despesas que deram divergencia e compara o F1_DOC X F2_NFELETR PARA ACHAR AS NFS E GERAR O PARTIDA DOBRADA DA FILIAL CONCILIADORA
		//AMARRAR OS DOIS LANCAMENTOS NO PARTIDA DOBRADA PARA RASTREIO
		cQuery := "SELECT DISTINCT 'RPS '||LPAD(TRIM(R.P57_FLEX02),9,'0')||' NF '||TRIM(P.P57_FLEX02)  HISTORICO, "
		cQuery += " 'I' TIPO_MOV, "
		cQuery += " R.P57_CGCORI ORIGEM, " //Sempre a Origem sera a Receita
		cQuery += " P.P57_CGCORI DESTINO, "//Sempre o Destino sera a Despesa
		cQuery += " P.P57_FLEX02 F1_DOC, "
		cQuery += " R.P57_FLEX01 F2_NFELETR, "
		cQuery += " R.P57_FLEX02 F2_DOC, "
		cQuery += " R.P57_CREDIT CREDITO, "
		cQuery += " P.P57_DEBITO DEBITO, "
		cQuery += " LPAD(TRIM(R.P57_FLEX02),9,'0') RPS, "
		cQuery += " TRIM(P.P57_FLEX02) NF, "
		cQuery += " R.P57_FILORI FILREC,"
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " P.P57_VALOR VALOR ,"
		cQuery += " R.P57_VALOR VALORREC ,"
		cQuery += " P.P57_CCD,"
		cQuery += " R.P57_CCC,"
		cQuery += " P.P57_ITEMD,"
		cQuery += " R.P57_ITEMC,"
		cQuery += " P.P57_CLVLDB,"
		cQuery += " R.P57_CLVLCR, "
		cQuery += " P.P57_KEY KEYDESP, "
		cQuery += " R.P57_KEY KEYREC , "
		cQuery += " ' ' IDDESP, "
		cQuery += " ' ' IDREC , "
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " R.P57_FILORI FILREC,  "
		cQuery += " P.R_E_C_N_O_ RCNODE, "
		cQuery += " R.R_E_C_N_O_ RCNOREC  "
		cQuery += " 	FROM   " + RetSQLName("P57") + " P"
		cQuery += " LEFT JOIN " + RetSQLName("P57") + "  R "
		cQuery += "  ON R.P57_TIPO = 'R' "
		cQuery += " 	AND R.P57_DC ='3' "
		//cQuery += " 	AND R.P57_STATUS ='2' "
		cQuery += " 	AND (CASE WHEN LENGTH(TRIM(R.P57_FLEX01)) < LENGTH(TRIM(P.P57_FLEX02)) THEN LPAD(TRIM(R.P57_FLEX01) , LENGTH(TRIM(P.P57_FLEX02)) , '0')  "
		cQuery += " 	ELSE LPAD(TRIM(R.P57_FLEX01) , LENGTH(TRIM(P.P57_FLEX02)) , '0') END ) = TRIM(P.P57_FLEX02) "
		cQuery += " 	AND R.P57_CGCDES =P.P57_CGCORI  "
		cQuery += "  	AND R.P57_PROC   =P.P57_PROC "
		cQuery += " 	AND R.P57_COMPET =P.P57_COMPET AND R.D_E_L_E_T_ = ' ' "
		cQuery += " WHERE  P.P57_TIPO = 'D' "
		cQuery += " AND P.P57_DC ='2' "
		cQuery += " AND P.P57_STATUS  IN ('1','2')  "
		cQuery += " AND R.P57_CGCORI IS	NOT NULL "
		cQuery += " AND P.P57_PROC='"+aParam[2]+"' AND P.P57_COMPET  ='"+aParam[3]+"'  AND P.D_E_L_E_T_ = ' '"
		BulkSCre(cQuery,cThread,'1',oInter)
		//SegunDa etapa procura Notas de entrada vs Notas de saida sem NFELTR COM ENTIDADES CC,ITEM E CLASSE IGUAIS
		cQuery := "SELECT  DISTINCT 'RPS '||LPAD(TRIM(R.P57_FLEX02),9,'0')||' NF '||TRIM(P.P57_FLEX02)  HISTORICO,"
		cQuery += " 'I' TIPO_MOV, "
		cQuery += " R.P57_CGCORI ORIGEM, " //Sempre a Origem sera a Receita
		cQuery += " P.P57_CGCORI DESTINO, "//Sempre o Destino sera a Despesa
		cQuery += " P.P57_FLEX02 F1_DOC, "
		cQuery += " R.P57_FLEX01 F2_NFELETR, "
		cQuery += " R.P57_FLEX02 F2_DOC, "
		cQuery += " P.P57_CREDIT CREDITO, "
		cQuery += " R.P57_DEBITO DEBITO, "
		cQuery += " LPAD(TRIM(R.P57_FLEX02),9,'0') RPS, "
		cQuery += " TRIM(P.P57_FLEX02) NF, "
		cQuery += " R.P57_FILORI FILREC,"
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " P.P57_VALOR VALOR ,"
		cQuery += " R.P57_VALOR VALORREC ,"
		cQuery += " R.P57_CCD,"
		cQuery += " P.P57_CCC,"
		cQuery += " R.P57_ITEMD,"
		cQuery += " P.P57_ITEMC,"
		cQuery += " R.P57_CLVLDB,"
		cQuery += " P.P57_CLVLCR, "
		cQuery += " R.P57_KEY KEYDESP, "
		cQuery += " P.P57_KEY KEYREC,  "
		cQuery += " ' ' IDDESP, "
		cQuery += " ' ' IDREC , "
		cQuery += " P.P57_FILORI FILDESP, "
		cQuery += " R.P57_FILORI FILREC,  "
		cQuery += " P.R_E_C_N_O_ RCNODE, "
		cQuery += " R.R_E_C_N_O_ RCNOREC  "
		cQuery += " 	FROM   " + RetSQLName("P57") + " P"
		cQuery += " LEFT JOIN " + RetSQLName("P57") + "  R "
		cQuery += "  ON R.P57_TIPO = 'R' "
		cQuery += " 	AND R.P57_DC ='3' "
		//cQuery += " 	AND R.P57_STATUS  IN ('1','2')  "
		cQuery += " 	AND TRIM(R.P57_FLEX02) = TRIM(P.P57_FLEX02) "
		cQuery += " 	AND R.P57_CGCDES =P.P57_CGCORI  "
		cQuery += "  	AND R.P57_PROC   =P.P57_PROC   AND R.D_E_L_E_T_ = ' '"
		cQry1 := cQuery
		cQry2 := cQuery
		cQry1 += " 	AND R.P57_COMPET =P.P57_COMPET "
		cQry1 += " 	AND R.P57_CCC	 =P.P57_CCD
		cQry1 += " 	AND R.P57_ITEMC	 =P.P57_ITEMD
		cQry1 += " 	AND R.P57_CLVLCR =P.P57_CLVLDB
		cWhere := " WHERE  P.P57_TIPO = 'D' "
		cWhere += " AND P.P57_DC ='2' "
		cWhere += " AND P.P57_STATUS  IN ('1','2')  "
		cWhere += " AND R.P57_CGCORI IS	NOT NULL "
		cWhere += " AND P.P57_PROC='"+aParam[2]+"' AND P.P57_COMPET  ='"+aParam[3]+"'  AND P.D_E_L_E_T_ = ' '"
		cQry1  += cWhere
		cQry2  += cWhere
		//Entidades Iguais
		BulkSCre(cQry1,cThread,'2',oInter)
		//doc iguais mas nao olha Entidades
		BulkSCre(cQry2,cThread,'2',oInter)

	ElseIF("ELIMINACAO" $ cParam)//Primeira regra de saida DE ELIMINACAO E ENTIDADES
		lSaida:=.t.
		//Destino receita
		cQuery := "SELECT DISTINCT 'CREDITO' TIPO,P.R_E_C_N_O_ RECNO,SUBSTR(P57_CREDIT,2,3) DRE,TRIM(ZX5CMP.ZX5_DESENG) EMPRESA,
		cQuery += " CASE WHEN ZX5.ZX5_DESESP <> ' ' THEN TRIM(ZX5.ZX5_DESESP) ELSE TRIM(ZX5A.ZX5_DESESP) END CCUSTO,
		cQuery += " CASE WHEN ZX5.ZX5_DESENG <> ' ' THEN TRIM(ZX5.ZX5_DESENG) ELSE TRIM(ZX5A.ZX5_DESENG) END ITEM,
		cQuery += " CASE WHEN ZX5.ZX5_COMPL  <> ' ' THEN TRIM(ZX5.ZX5_COMPL ) ELSE TRIM(ZX5A.ZX5_COMPL)  END CLASSE
		cQuery += "  FROM   " + RetSQLName("P57") + " P"
		cQuery += " 	INNER JOIN " + RetSQLName("ZX5") + " ZX5CMP ON ZX5CMP.ZX5_TABELA = 'INTCMP'"
		cQuery += " 		AND ZX5CMP.ZX5_CHAVE = '000001'"
		cQuery += " 		AND ZX5CMP.ZX5_DESCRI = P57_CGCDES"
		cQuery += " 		AND ZX5CMP.D_E_L_E_T_ =' '"
		cQuery += " 	LEFT JOIN " + RetSQLName("ZX5") + " ZX5 ON ZX5.ZX5_TABELA ='CTBA54'"
		cQuery += " 		AND TRIM(ZX5.ZX5_CHAVE) = TRIM(ZX5CMP.ZX5_DESENG )"
		cQuery += " 		AND (CASE WHEN SUBSTR(P57_CREDIT , 1 , 1) ='3'"
		cQuery += " 		OR SUBSTR(P57_CREDIT , 1 , 4) ='4209' THEN '205' ELSE SUBSTR(P57_CREDIT , 2 , 3) END ) = TRIM(ZX5.ZX5_DESCRI)"
		cQuery += " 		AND ZX5.D_E_L_E_T_ =' '"

		// caso nao achar, pegar da matriz
		cQuery += " LEFT JOIN " + RetSQLName("ZX5") + " ZX5A ON ZX5A.ZX5_TABELA ='CTBA54' "
		cQuery += " AND TRIM(ZX5A.ZX5_CHAVE) = '000' "
		cQuery += " AND (CASE WHEN SUBSTR(P57_CREDIT , 1 , 1) ='3'"
		cQuery += " 		OR SUBSTR(P57_CREDIT , 1 , 4) ='4209' THEN '205' ELSE SUBSTR(P57_CREDIT , 2 , 3) END ) = TRIM(ZX5A.ZX5_DESCRI)"
		cQuery += " 		AND ZX5A.D_E_L_E_T_ =' '"

		cQuery += " WHERE   P.P57_STATUS IN('1','2')"
		cQuery += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cQuery += "  AND P57_TIPO ='I' AND P.D_E_L_E_T_ =' '  "
		cQuery += " UNION ALL"
		cQuery += " SELECT DISTINCT 'DEBITO' TIPO,P.R_E_C_N_O_ RECNO,SUBSTR(P57_DEBITO,2,3) DRE,TRIM(ZX5CMP.ZX5_DESENG) EMPRESA,
		cQuery += " CASE WHEN ZX5.ZX5_DESESP <> ' ' THEN TRIM(ZX5.ZX5_DESESP) ELSE TRIM(ZX5A.ZX5_DESESP) END CCUSTO,
		cQuery += " CASE WHEN ZX5.ZX5_DESENG <> ' ' THEN TRIM(ZX5.ZX5_DESENG) ELSE TRIM(ZX5A.ZX5_DESENG) END ITEM,
		cQuery += " CASE WHEN ZX5.ZX5_COMPL  <> ' ' THEN TRIM(ZX5.ZX5_COMPL ) ELSE TRIM(ZX5A.ZX5_COMPL)  END CLASSE
		cQuery += " 	FROM   " + RetSQLName("P57") + " P"
		cQuery += " 		INNER JOIN " + RetSQLName("ZX5") + " ZX5CMP ON ZX5CMP.ZX5_TABELA = 'INTCMP'"
		cQuery += " 		AND ZX5CMP.ZX5_CHAVE = '000001'"
		cQuery += " 		AND ZX5CMP.ZX5_DESCRI = P57_CGCORI"
		cQuery += " 		AND ZX5CMP.D_E_L_E_T_ =' '"
		cQuery += " 	LEFT JOIN " + RetSQLName("ZX5") + " ZX5 ON ZX5.ZX5_TABELA ='CTBA54'"
		cQuery += " 		AND TRIM(ZX5.ZX5_CHAVE) = TRIM(ZX5CMP.ZX5_DESENG )"
		cQuery += " 		AND (CASE WHEN SUBSTR(P57_DEBITO , 1 , 1) ='3'"
		cQuery += " 		OR SUBSTR(P57_DEBITO , 1 , 4) ='4209' THEN '205' ELSE SUBSTR(P57_DEBITO , 2 , 3) END ) = TRIM(ZX5.ZX5_DESCRI)"
		cQuery += " 		AND ZX5.D_E_L_E_T_ =' '"

		// caso nao achar, pegar da matriz
		cQuery += " LEFT JOIN " + RetSQLName("ZX5") + " ZX5A ON ZX5A.ZX5_TABELA ='CTBA54' "
		cQuery += " AND TRIM(ZX5A.ZX5_CHAVE) = '000' "
		cQuery += " AND (CASE WHEN SUBSTR(P57_DEBITO , 1 , 1) ='3'"
		cQuery += " 		OR SUBSTR(P57_DEBITO , 1 , 4) ='4209' THEN '205' ELSE SUBSTR(P57_DEBITO , 2 , 3) END ) = TRIM(ZX5A.ZX5_DESCRI)"
		cQuery += " 		AND ZX5A.D_E_L_E_T_ =' '"

		cQuery += " WHERE  P.P57_STATUS IN('1','2')"
		cQuery += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cQuery += "  AND P57_TIPO ='I' AND P.D_E_L_E_T_ =' '  "
		cQuery += "  AND P.D_E_L_E_T_ =' '"
		cQuery += " ORDER BY RECNO,TIPO  "

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsCTK,.T.,.T.)
		Count To nTotal
		(cAlsCTK)->(DbGoTop())
		nLinha:=0
		DbSelectArea("P57")
		DbSetOrder(1)
		While (cAlsCTK)->(!Eof())
			P57->(DbGoTo((cAlsCTK)->RECNO))
			//processa as
			RecLock("P57", .F.)
			//Origem
			IF(Alltrim((cAlsCTK)->TIPO) =="DEBITO")
				P57->P57_CCD	:= (cAlsCTK)->CCUSTO
				P57->P57_ITEMD	:= (cAlsCTK)->ITEM
				P57->P57_CLVLDB	:= (cAlsCTK)->CLASSE
			Else
				//Destino
				P57->P57_CCC	:=  (cAlsCTK)->CCUSTO
				P57->P57_ITEMC	:=  (cAlsCTK)->ITEM
				P57->P57_CLVLCR	:=  (cAlsCTK)->CLASSE
			EndIF
			P57->P57_STATUS := '3'
			P57->(MsUnlock())

			(cAlsCTK)->(DbSkip())
		EndDo

		If Select(cAlsCTK) > 0
			(cAlsCTK)->(dbCloseArea())
		EndIf
	ElseIF("IGUAIS" $ cParam)//Pega os lancamentos intercompanies que tem contas iguais e desconsidera no status
		lSaida:=.t.
		cScript := " UPDATE   " + RetSQLName("P57") + " SET P57_STATUS ='4', P57_TRACKE ='CONTA DEBITO E CREDITO IGUAIS, REGISTRO DESCARTADO' "
		cScript += " WHERE P57_TIPO ='I' AND D_E_L_E_T_ =' ' AND P57_CREDIT = P57_DEBITO
		cScript += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"

		IF TCSQLEXEC(cScript) <> 0
			FWMonitorMsg( cThread+ " error: " +CRLF + TCSqlError() )
		EndIF
	ElseIF("FINAL" $ cParam)
		//Pega os lancamentos  que nao foram possiveis concilia-los nas regras anteriores, esta regra tem que ser sempre a ultima
		lSaida:=.t.
		cQuery := "SELECT * "
		cQuery += " FROM     " + RetSQLName("P57") + " D "
		cQuery += " WHERE  P57_TIPO ='D' "
		cQuery += " AND D_E_L_E_T_ =' ' "
		cQuery += " AND P57_STATUS IN ('1','2') "
		cQuery += " AND P57_DC ='1'  "
		cQuery += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		//cQuery += " UNION ALL "
		//cQuery += "SELECT * "
		//cQuery += " FROM     " + RetSQLName("P57") + " D "
		//cQuery += " WHERE  P57_TIPO ='D' "
		//cQuery += " AND D_E_L_E_T_ =' ' "
		//cQuery += " AND P57_STATUS IN ('1','2') "
		//cQuery += " AND P57_DC ='2'  "
		//cQuery += "  AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"
		cQuery += " UNION ALL "
		cQuery += " SELECT * "
		cQuery += " FROM    " + RetSQLName("P57") + " R "
		cQuery += " WHERE  P57_TIPO ='R' "
		cQuery += " AND D_E_L_E_T_ =' ' "
		cQuery += " AND P57_STATUS IN ('1','2') "
		cQuery += " AND P57_DC ='3' "
		cQuery += " AND P57_PROC	='"+aParam[2]+"' AND P57_COMPET  ='"+aParam[3]+"'"

	Else
		cBetween:=getPer(aParam)
		cColumns := " CT2_FILIAL FILIAL,  "
		cColumns += " ' ' CODIGO,
		cColumns += " ' ' CGC,
		cColumns += " ' ' NOME, "
		cColumns += " 'I' TIPO_MOV, "
		cColumns += " CT2_DATA EMISSAO, "
		cColumns += " ' ' SERIE, "
		cColumns += " ' ' TIPO, "
		cColumns += " ' ' DOC, "
		cColumns += " ' ' NFELETR, "
		cColumns += " ' ' VENCTO , "
		cColumns += " ' ' BAIXA, "
		cColumns += " CT2_VALOR  VALOR , "
		cColumns += " CT2_FILIAL, "
		cColumns += " CT2_DATA, "
		cColumns += " CT2_LOTE LOTE, "
		cColumns += " CT2_HIST, "
		cColumns += " CT2_CCD, "
		cColumns += " CT2_CCC, "
		cColumns += " CT2_ITEMD, "
		cColumns += " CT2_ITEMC, "
		cColumns += " CT2_CLVLDB, "
		cColumns += " CT2_CLVLCR, "
		cColumns += " CASE WHEN CT2_DC= '1' THEN 'DEBITO' WHEN CT2_DC= '2' THEN 'CREDITO' WHEN CT2_DC= '3' THEN 'PARTIDA_DOBRADA' ELSE 'DESCONHECIDO' END TIPO_LANCAMENTO, "
		cColumns += " CT2_DEBITO, "
		cColumns += " CT2_CREDIT, "
		cColumns += " CT2_VALOR,   "
		cColumns += " CT2_DC ,   "
		cColumns += " CT2_SBLOTE,   "
		cColumns += " CT2_DOC,   "
		cColumns += " CT2_LINHA   "
		cQuery := " SELECT "+cParalel+" " +cColumns
		cQuery += " FROM   " + RetSQLName("CT2") + " A   "
		cQuery += " WHERE    "
		cQuery += "         A.D_E_L_E_T_ <> '*'  and CT2_INTERC = '1'   and CT2_FILIAL NOT IN ("+cFilRMV+")"
		cQuery += "        AND A.CT2_DATA BETWEEN " + cBetween

		//cQuery += "        AND A.CT2_DATA <= '"+DTOS(aRetParam[2])+"'  "
		IF(!Empty(Alltrim(cContaNot)))
			cQuery += "  AND ( CT2_DEBITO  NOT IN "+cContaNot+" AND  CT2_CREDIT  NOT IN "+cContaNot+" ) "
		EndIF


	EndIF



	if  FwBulk():CanBulk() .And. !lSaida
		aStrFld  :=   P57->(DbStruct())
		oBulkEnt:setFields(aStrFld)

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsCTK,.T.,.T.)
		Count To nTotal
		(cAlsCTK)->(DbGoTop())
		nLinha:=0

		While (cAlsCTK)->(!Eof())

			aLinha:={}
			nLinha++
			FWMonitorMsg( cThread+" Gravando dados de Entrada "+cValToChar(nLinha)  +" de "+ cValToChar(nTotal) )
			cFilAnt:=(cAlsCTK)->FILIAL
			cEmpBkp := cEmpAnt
			cEmpAnt := U_TIRCVEMP(cFilAnt)
			aCgc:=FWSM0Util():GetSM0Data(cEmpAnt,cFilAnt, { "M0_CGC" } )
			cEmpAnt := cEmpBkp
			cCgcOri:= iif(Len(aCgc)>=1,Alltrim(aCgC[01][02]),"")


			cKey:=(cAlsCTK)->CODIGO+"|"+(cAlsCTK)->DOC+"|"+ (cAlsCTK)->NFELETR +"|"+(cAlsCTK)->SERIE+(cAlsCTK)->TIPO+"|"+(cAlsCTK)->CT2_LINHA
			cId:= oInter:GetUUID()


			Aadd(aLinha,{"P57_FILIAL"	,xFilial("P57")			}) //P57_FILIAL
			Aadd(aLinha,{"P57_PROC"		,aParam[2]				})//P57_PROC
			Aadd(aLinha,{"P57_REGSAI"	,aParam[4]				})//P57_REGSAI
			Aadd(aLinha,{"P57_REGENT"	,aParam[5]				})//P57_REGENT
			Aadd(aLinha,{"P57_COMPET"	,aParam[3]				})//P57_COMPET
			Aadd(aLinha,{"P57_DTPROC"	,dDataBase				})//P57_DTPROC
			Aadd(aLinha,{"P57_USR"		,aParam[6]				})//P57_USR
			Aadd(aLinha,{"P57_CGCORI"	,cCgcOri				})//P57_CGCORI
			Aadd(aLinha,{"P57_CGCDES"	,(cAlsCTK)->CGC			})//P57_CGCDES
			Aadd(aLinha,{"P57_TIPO"		,(cAlsCTK)->TIPO_MOV	})//P57_TIPO
			Aadd(aLinha,{"P57_KEY"		,cKey					})//P57_KEY
			Aadd(aLinha,{"P57_STATUS"	,'1'					})//P57_STATUS
			Aadd(aLinha,{"P57_DTLCTO"	,sTod((cAlsCTK)->CT2_DATA)})//P57_DTLCTO
			Aadd(aLinha,{"P57_LOTE"		,(cAlsCTK)->LOTE		})//P57_LOTE
			Aadd(aLinha,{"P57_SBLOTE"	,(cAlsCTK)->CT2_SBLOTE	})//P57_SBLOTE
			Aadd(aLinha,{"P57_DOC"		,(cAlsCTK)->CT2_DOC		})//P57_DOC
			Aadd(aLinha,{"P57_LINHA"	,(cAlsCTK)->CT2_LINHA	})//P57_LINHA
			Aadd(aLinha,{"P57_MOEDLC"	,''						})//P57_MOEDLC
			Aadd(aLinha,{"P57_DC"		,(cAlsCTK)->CT2_DC		})//P57_DC
			Aadd(aLinha,{"P57_DEBITO"	,(cAlsCTK)->CT2_DEBITO	})//P57_DEBITO
			Aadd(aLinha,{"P57_CREDIT"	,(cAlsCTK)->CT2_CREDIT	})//P57_CREDIT
			Aadd(aLinha,{"P57_DCD"		,""						})//P57_DCD
			Aadd(aLinha,{"P57_DCC"		,""						})//P57_DCC
			Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->CT2_VALOR	})//P57_VALOR
			Aadd(aLinha,{"P57_HIST"		,(cAlsCTK)->CT2_HIST	})//P57_HIST
			Aadd(aLinha,{"P57_CCD"		,(cAlsCTK)->CT2_CCD		})//P57_CCD
			Aadd(aLinha,{"P57_CCC"		,(cAlsCTK)->CT2_CCC		})//P57_CCC
			Aadd(aLinha,{"P57_ITEMD"	,(cAlsCTK)->CT2_ITEMD	})//P57_ITEMD
			Aadd(aLinha,{"P57_ITEMC"	,(cAlsCTK)->CT2_ITEMC	})//P57_ITEMC
			Aadd(aLinha,{"P57_CLVLDB"	,(cAlsCTK)->CT2_CLVLDB	})//P57_CLVLDB
			Aadd(aLinha,{"P57_SEQUEN"	,''						})//P57_SEQUEN
			Aadd(aLinha,{"P57_CLVLCR"	,(cAlsCTK)->CT2_CLVLCR	})//P57_CLVLCR
			Aadd(aLinha,{"P57_EMPORI"	,''						})//P57_EMPORI
			Aadd(aLinha,{"P57_FILORI"	,(cAlsCTK)->FILIAL		})//P57_FILORI
			Aadd(aLinha,{"P57_DATATX"	,dDataBase				})//P57_DATATX
			Aadd(aLinha,{"P57_TAXA"		,0						})//P57_TAXA
			Aadd(aLinha,{"P57_INTERC"	,'1'					})//P57_INTERC
			Aadd(aLinha,{"P57_ORIGEM"	,cParam+" "+cBetween	})//P57_ORIGEM
			Aadd(aLinha,{"P57_ROTINA"	,'TCTBA053'				})//P57_ROTINA
			Aadd(aLinha,{"P57_LP"		,''						})//P57_LP
			Aadd(aLinha,{"P57_VLR01"	,(cAlsCTK)->VALOR		})//P57_VLR01
			Aadd(aLinha,{"P57_VLR02"	,0						})//P57_VLR02
			cNfElet := If(Len(alltrim((cAlsCTK)->NFELETR)) 	> 9 ,substr(alltrim((cAlsCTK)->NFELETR),(Len(alltrim((cAlsCTK)->NFELETR))-8),Len(alltrim((cAlsCTK)->NFELETR))),alltrim((cAlsCTK)->NFELETR))
			cDOC 	:= If(Len(alltrim((cAlsCTK)->DOC)) 		> 9 ,substr(alltrim((cAlsCTK)->DOC),(Len(alltrim((cAlsCTK)->DOC))-8),Len(alltrim((cAlsCTK)->DOC))),alltrim((cAlsCTK)->DOC))
			//Insere zeros a esquerda
			//cNfElet:=PADL(cNfElet,9,"0")
			//cDOC   :=PADL(cDOC,9,"0")
			Aadd(aLinha,{"P57_FLEX01"	,	cNfElet        	})//P57_FLEX01
			Aadd(aLinha,{"P57_FLEX02"	,cDOC           		})//P57_FLEX02
			Aadd(aLinha,{"P57_FLEX03"	,' '					})//P57_FLEX03
			Aadd(aLinha,{"P57_FLEX04"	,' '					})//P57_FLEX04
			Aadd(aLinha,{"P57_FLEX05"	,' '					})//P57_FLEX05
			Aadd(aLinha,{"P57_BAIXA"	, sTod((cAlsCTK)->BAIXA)})//P57_BAIXA
			Aadd(aLinha,{"P57_TRACKE"	, ' '					})//P57_TRACKE
			Aadd(aLinha,{"P57_ID"		, cId					})//P57_ID
			Aadd(aLinha,{"P57_IDINT"	, ' '					})//P57_IDINT
			nPosOri := aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim(cCgcOri) })
			nPosDes	:= aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim((cAlsCTK)->CGC) })
			cOriEmp	:=""
			cDesEmp	:=""
			If(nPosOri>0)
				cOriEmp:=aCgcDre[nPosOri][2]
			EndIF
			If(nPosDes>0)
				cDesEmp:=aCgcDre[nPosDes][2]
			EndIF
			Aadd(aLinha,{"P57_ORIEMP"	,cOriEmp			})//P57_ORIEMP
			Aadd(aLinha,{"P57_DESTEMP"	,cDesEmp			})//P57_DESTEMP
			Aadd(aLinha,{"P57_IDDESP"	,' '				})//P57_IDDESP
			Aadd(aLinha,{"P57_IDREC"	,' '				})//P57_IDREC
			aLinha:=oInter:ReorderBulk(aStrFld,aLinha)
			if(Len(aLinha)>0)
				oBulkEnt:AddData(aLinha)
			EndIF

			(cAlsCTK)->(DbSkip())
		EndDo

		If Select(cAlsCTK) > 0
			(cAlsCTK)->(dbCloseArea())
		EndIf


		if !oBulkEnt:Flush()
			Alert(oBulkEnt:GetError())
		EndIF
		if oBulkEnt:Close()

		EndIF
		oBulkEnt:Destroy()
		oBulkEnt := nil
		FREEOBJ( oBulkEnt )


	EndIF
	CATCHEXCEPTION USING oError
	IF ( ValType( oError ) == "O" )
		cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
		cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )
		FWMonitorMsg( "TIINTERCOMPANY ERRO: "+cMsgFault )
	EndIF

	ENDEXCEPTION
	PutGlbValue(cThread, " ")
Return


/*/{Protheus.doc} setStatus
	Atualiza o status da entidade na p57
	@type    Function
	<AUTHOR> Menabue Lima
	@since 24/08/2022
	@version 12.1.33

/*/
Static Function setStatus(cThread,cStatus,cWhere,cId,cIdDesp,cIdRec)
	Local	cScript := " UPDATE "+ RetSqlName("P57") +" SET P57_STATUS = '"+cStatus+"', P57_IDINT = '"+cId+"', " +CRLF
	cScript+=" P57_IDDESP = '"+cIdDesp+"' , P57_IDREC ='"+cIdRec+"' "+CRLF
	cScript+=" WHERE " + cWhere+CRLF


	IF TCSQLEXEC(cScript) <> 0
		FWMonitorMsg( cThread+ " error: " +CRLF + TCSqlError() )
	EndIF
Return



/*/{Protheus.doc} BulkSaida
	Inseri linhas de lancamentos intercompany na centralizadora
	@type    Function
	<AUTHOR> Menabue Lima
	@since 26/08/2022
	@version 12.1.33
/*/
Static Function BulkSaida(cQuerie,cThread,cPar,oInter)
	Local cAlsCTK 	:= GetNextAlias()
	Local 	oBulkSai 	:= FWBulk():New(RetSqlName("P57"))
	Local cId 	  	:= ''
	Local oError	:= nil
	Local cMsgFault	:=""
	Local aLinha	:={}
	Local  aStrFld  :=P57->(DbStruct())
	Local lTOTVSXRD := .F.
	Local cCNPJORI  :=GetMV("TI_INTCGO",.F.,"53113791")
	Local cCNPJDEST :=GetMV("TI_INTCGD",.F.,"13021784000186")
	Local lValDif   := .F., nRecP57D := 0
	if  FwBulk():CanBulk()
		TRYEXCEPTION

		oBulkSai:setFields(aStrFld)
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuerie),cAlsCTK,.T.,.T.)

		Count To nTotal
		(cAlsCTK)->(DbGoTop())
		nLinha:=0
		While (cAlsCTK)->(!Eof())
			aLinha:={}
			nLinha++

			FWMonitorMsg( cThread+" Gravando dados de Saida "+cValToChar(nLinha)  +" de "+ cValToChar(nTotal) )
			cKey:=(cAlsCTK)->F2_DOC+'|'+(cAlsCTK)->F1_DOC
			cId:= oInter:GetUUID()
			cFilAnt:=(cAlsCTK)->FILDESP
			cEmpBkp := cEmpAnt
			IF !Empty(cFilAnt)
				cEmpAnt:= U_TIRCVEMP(cFilAnt)
			EndIF
			cNomeOri:= Alltrim(FWSM0Util():getSM0FullName())
			cFilAnt:=(cAlsCTK)->FILREC
			IF !Empty(cFilAnt)
				cEmpAnt:= U_TIRCVEMP(cFilAnt)
			EndIF
			cNomeDest:=Alltrim(FWSM0Util():getSM0FullName())
			cApeOri:=Alltrim(oInter:GetEmpCgcNome((cAlsCTK)->ORIGEM))
			cApeDes:=Alltrim(oInter:GetEmpCgcNome((cAlsCTK)->DESTINO))
			cEmpAnt:=cEmpBkp
			iF(Empty(cApeOri))
				cApeOri:=cNomeOri
			EndIF
			iF(Empty(cApeDes))
				cApeDes:=cNomeDest
			EndIF
			cHist:= Alltrim((cAlsCTK)->HISTORICO)+ " "+SubStr(cApeOri,1,6) +" X "+SubStr(cApeDes,1,6)

			Aadd(aLinha,{"P57_FILIAL"	,xFilial("P57")			}) //P57_FILIAL
			Aadd(aLinha,{"P57_PROC"		,aParam[2]				})//P57_PROC
			Aadd(aLinha,{"P57_REGSAI"	,aParam[4]				})//P57_REGSAI
			Aadd(aLinha,{"P57_REGENT"	,aParam[5]				})//P57_REGENT
			Aadd(aLinha,{"P57_COMPET"	,aParam[3]				})//P57_COMPET
			Aadd(aLinha,{"P57_DTPROC"	,dDataBase				})//P57_DTPROC
			Aadd(aLinha,{"P57_USR"		,aParam[6]				})//P57_USR
			Aadd(aLinha,{"P57_CGCORI"	,(cAlsCTK)->ORIGEM		})//P57_CGCORI
			Aadd(aLinha,{"P57_CGCDES"	,(cAlsCTK)->DESTINO		})//P57_CGCDES
			Aadd(aLinha,{"P57_TIPO"		,(cAlsCTK)->TIPO_MOV	})//P57_TIPO
			Aadd(aLinha,{"P57_KEY"		,cKey					})//P57_KEY
			Aadd(aLinha,{"P57_STATUS"	,'1'					})//P57_STATUS
			Aadd(aLinha,{"P57_DTLCTO"	,dDataBase				})//P57_DTLCTO
			Aadd(aLinha,{"P57_LOTE"		,''						})//P57_LOTE
			Aadd(aLinha,{"P57_SBLOTE"	,''						})//P57_SBLOTE
			Aadd(aLinha,{"P57_DOC"		,''						})//P57_DOC
			Aadd(aLinha,{"P57_LINHA"	,''						})//P57_LINHA
			Aadd(aLinha,{"P57_MOEDLC"	,''						})//P57_MOEDLC
			Aadd(aLinha,{"P57_DC"		,'3'					})//P57_DC
			Aadd(aLinha,{"P57_DEBITO"	,(cAlsCTK)->DEBITO		})//P57_DEBITO
			Aadd(aLinha,{"P57_CREDIT"	,(cAlsCTK)->CREDITO		})//P57_CREDIT
			Aadd(aLinha,{"P57_DCD"		,""						})//P57_DCD
			Aadd(aLinha,{"P57_DCC"		,""						})//P57_DCC

			if(Alltrim((cAlsCTK)->TIPO_MOV)=='I' .And. Empty(Alltrim((cAlsCTK)->FILDESP)))//  TICONTIN-3035 se nao tiver filial veio de algum arquivo
				lValDif  := (cAlsCTK)->VALOR < (cAlsCTK)->VALORREC
				lTOTVSXRD:= cCNPJORI  $ (cAlsCTK)->ORIGEM  .And. (cAlsCTK)->DESTINO $ cCNPJDEST//Tratativa se for Totvs e RD pegar sempre o menor valor
				If(lTOTVSXRD)
					if lValDif
						Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
					Else
						Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALORREC		})//P57_VALOR DA RECEITA
					EndIF
				Else
					if lValDif
						IF((cAlsCTK)->DESPLIN >= (cAlsCTK)->RECLIN .And. ( (cAlsCTK)->RECSUM - (cAlsCTK)->DESPSUM ==0 ))
							Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
						Else
							Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALORREC		})//P57_VALOR DA RECEITA
						EndIF
					Else
						IF((cAlsCTK)->DESPLIN <= (cAlsCTK)->RECLIN .And. ( (cAlsCTK)->RECSUM - (cAlsCTK)->DESPSUM ==0 ))
							Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
						Else
							Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALORREC		})//P57_VALOR DA RECEITA
						EndIF
					EndIF

				EndIF

			Else
				Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
			EndIf
			Aadd(aLinha,{"P57_HIST"		,cHist					})//P57_HIST
			Aadd(aLinha,{"P57_CCD"		,(cAlsCTK)->P57_CCD		})//P57_CCD
			Aadd(aLinha,{"P57_CCC"		,(cAlsCTK)->P57_CCC		})//P57_CCC
			Aadd(aLinha,{"P57_ITEMD"	,(cAlsCTK)->P57_ITEMD	})//P57_ITEMD
			Aadd(aLinha,{"P57_ITEMC"	,(cAlsCTK)->P57_ITEMC	})//P57_ITEMC
			Aadd(aLinha,{"P57_CLVLDB"	,(cAlsCTK)->P57_CLVLDB	})//P57_CLVLDB
			Aadd(aLinha,{"P57_SEQUEN"	,''						})//P57_SEQUEN
			Aadd(aLinha,{"P57_CLVLCR"	,(cAlsCTK)->P57_CLVLCR	})//P57_CLVLCR
			Aadd(aLinha,{"P57_EMPORI"	,''						})//P57_EMPORI
			Aadd(aLinha,{"P57_FILORI"	,'93601000100'			})//P57_FILORI
			Aadd(aLinha,{"P57_DATATX"	,dDataBase				})//P57_DATATX
			Aadd(aLinha,{"P57_TAXA"		,0						})//P57_TAXA
			Aadd(aLinha,{"P57_INTERC"	,'1'					})//P57_INTERC
			Aadd(aLinha,{"P57_ORIGEM"	,'SAIDAS2'				})//P57_ORIGEM
			Aadd(aLinha,{"P57_ROTINA"	,'TCTBA053'				})//P57_ROTINA
			Aadd(aLinha,{"P57_LP"		,''						})//P57_LP
			Aadd(aLinha,{"P57_VLR01"	,(cAlsCTK)->VALOR		})//P57_VLR01
			Aadd(aLinha,{"P57_VLR02"	,0						})//P57_VLR02
			Aadd(aLinha,{"P57_FLEX01"	,(cAlsCTK)->F2_DOC		})//P57_FLEX01
			Aadd(aLinha,{"P57_FLEX02"	,(cAlsCTK)->F1_DOC		})//P57_FLEX02
			Aadd(aLinha,{"P57_FLEX03"	,' '					})//P57_FLEX03
			Aadd(aLinha,{"P57_FLEX04"	,' '					})//P57_FLEX04
			Aadd(aLinha,{"P57_FLEX05"	, ' '					})//P57_FLEX05
			Aadd(aLinha,{"P57_BAIXA"	, dDataBase				})//P57_BAIXA
			Aadd(aLinha,{"P57_TRACKE"	, ' '					})//P57_TRACKE
			Aadd(aLinha,{"P57_ID"		, cId					})//P57_ID
			Aadd(aLinha,{"P57_IDINT"	, ' '					})//P57_IDINT
			nPosOri := aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim((cAlsCTK)->ORIGEM) })
			nPosDes	:= aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim((cAlsCTK)->DESTINO) })
			cOriEmp	:=""
			cDesEmp	:=""
			If(nPosOri>0)
				cOriEmp:=aCgcDre[nPosOri][2]
			EndIF
			If(nPosDes>0)
				cDesEmp:=aCgcDre[nPosDes][2]
			EndIF
			Aadd(aLinha,{"P57_ORIEMP"	,cOriEmp			})//P57_ORIEMP
			Aadd(aLinha,{"P57_DESTEMP"	,cDesEmp			})//P57_DESTEMP
			Aadd(aLinha,{"P57_IDDESP"	,' '				})//P57_IDDESP
			Aadd(aLinha,{"P57_IDREC"	,' '				})//P57_IDREC
			aLinha:=oInter:ReorderBulk(aStrFld,aLinha)
			if(Len(aLinha)>0)
				oBulkSai:AddData(aLinha)
			EndIF

			//Atualiza as despesas
			cWhere:= " P57_CGCORI='"+(cAlsCTK)->DESTINO+"' AND P57_CGCDES ='"+(cAlsCTK)->ORIGEM+"'"
			cWhere+= "  AND  P57_DEBITO ='"+ALLTRIM((cAlsCTK)->CREDITO)+"'    "
			cWhere+= "  AND  P57_TIPO = 'D' AND D_E_L_E_T_ = ' '     "
			cWhere+= "  AND  P57_KEY  = '"+alltrim((cAlsCTK)->KEYDESP)+"'"
			cWhere+= "  AND P57_VALOR = " + cValToChar((cAlsCTK)->VALOR)+""
			if(cPar=='2')
				cWhere+= "  AND P57_CCD   ='"+(cAlsCTK)->P57_CCD    +"'"
				cWhere+= "  AND P57_ITEMD ='"+(cAlsCTK)->P57_ITEMD  +"'"
				cWhere+= "  AND P57_CLVLDB='"+(cAlsCTK)->P57_CLVLDB +"'"
			EndIF
			cAuxTra := cWhere
			cWhere  += "  AND  P57_DC   = '1'  "
			cWhere  += " AND P57_STATUS ='2'

			setStatus(cThread,"3",cWhere,cId,' ',(cAlsCTK)->IDREC)
			//Atualiza as receitas
			cWhere:= " P57_CGCORI='"+(cAlsCTK)->ORIGEM+"' AND P57_CGCDES ='"+(cAlsCTK)->DESTINO+"'"
			cWhere+= "  AND  P57_CREDIT ='"+ALLTRIM((cAlsCTK)->DEBITO)+"'    "
			cWhere+= "  AND  P57_TIPO = 'R'  AND D_E_L_E_T_ = ' '    "
			cWhere+= "  AND  P57_KEY  = '"+alltrim((cAlsCTK)->KEYREC)+"'"
			cWhere+= "  AND  P57_DC   IN ('3','2')    "
			cWhere+= " 	AND P57_STATUS IN ('1','2')"
			//cWhere+= "  AND P57_VALOR = " + cValToChar((cAlsCTK)->VALOR)+""
			if(cPar=='2')
				cWhere+= "  AND P57_CCC   ='"+(cAlsCTK)->P57_CCC   +"'"
				cWhere+= "  AND P57_ITEMC ='"+(cAlsCTK)->P57_ITEMC +"'"
				cWhere+= "  AND P57_CLVLCR='"+(cAlsCTK)->P57_CLVLCR+"'"
			EndIF

			setStatus(cThread,"3",cWhere,cId,(cAlsCTK)->IDDESP,' ')
			//Grva dados para rastreio

			cAlsTra := GetNextAlias()
			cQryTr := " SELECT "+cParalel+" R_E_C_N_O_ RCNOREC "
			cQryTr += " FROM   " + RetSQLName("P57") + " A "
			cQryTr += " WHERE  P57_FILIAL = '" + xFilial("P57") + "' AND "
			cQryTr += "        P57_PROC   = '"+aParam[2]  +"' AND   "
			cQryTr += "        P57_COMPET = '"+aParam[3]+"' AND "
			cQryTr += "        P57_ROTINA = 'TCTBA055' AND "
			cQryTr += cAuxTra

			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQryTr),cAlsTra,.T.,.T.)

			If !Empty((cAlsTra)->RCNOREC)
				nRecP57D := (cAlsTra)->RCNOREC
			EndIf

			If Select(cAlsTra) > 0
				(cAlsTra)->(dbCloseArea())
			EndIf

			BulkTre(oInter,(cAlsCTK)->P57RECR, nRecP57D, cId,'D')
			// SAIDA

			(cAlsCTK)->(DbSkip())
		EndDo
		If Select(cAlsCTK) > 0
			(cAlsCTK)->(dbCloseArea())
		EndIf
		if !oBulkSai:Flush()
			Alert(oBulkSai:GetError())
		EndIF
		if oBulkSai:Close()

		EndIF
		CATCHEXCEPTION USING oError
		IF ( ValType( oError ) == "O" )
			cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
			cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )
			FWMonitorMsg( "TIINTERCOMPANY ERRO: "+cMsgFault )
			cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
			cDir:="\system\tctba053\"
			nDir 		:= MakeDir( cDir )
			nHandle 	:= MsfCreate(cDir+"BulkSaida_"+cTime+".log",0)
			if (nHandle > 0)
				FWrite(nHandle,cMsgFault)
				FClose(nHandle)
			EndIF
		EndIF

		ENDEXCEPTION
		oBulkSai:Destroy()
		oBulkSai := nil
		FREEOBJ( oBulkSai )
	EndIF
Return
/*/{Protheus.doc} BulkSCre
	Inseri linhas de lancamentos intercompany na centralizadora de credito despess x Receita
	@type    Function
	<AUTHOR> Menabue Lima
	@since 26/08/2022
	@version 12.1.33
/*/
Static Function BulkSCre(cQuerie,cThread,cPar,oInter)
	Local cAlsCTK := GetNextAlias()
	Local 	oBulkCre := FWBulk():New(RetSqlName("P57"))
	Local cId 	  := ''

	Local oError	:= nil
	Local cMsgFault	:=""
	Local aLinha	:={}
	Local aStrFld  :=   P57->(DbStruct())
	Local cCNPJORI  :=GetMV("TI_INTCGO",.F.,"13021784000186")
	Local cCNPJDEST :=GetMV("TI_INTCGD",.F.,"53113791001285")
	if  FwBulk():CanBulk()
		TRYEXCEPTION

		oBulkCre:setFields(aStrFld)
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuerie),cAlsCTK,.T.,.T.)

		Count To nTotal
		(cAlsCTK)->(DbGoTop())
		nLinha:=0
		While (cAlsCTK)->(!Eof())
			aLinha:={}
			nLinha++

			FWMonitorMsg( cThread+" Gravando dados de Entrada "+cValToChar(nLinha)  +" de "+ cValToChar(nTotal) )
			cKey:=(cAlsCTK)->F2_DOC+'|'+(cAlsCTK)->F1_DOC
			cId:= oInter:GetUUID()
			cFilAnt:=(cAlsCTK)->FILREC
			cEmpBkp := cEmpAnt
			cEmpAnt := U_TIRCVEMP(cFilAnt)
			cNomeOri:= Alltrim(FWSM0Util():getSM0FullName())
			cFilAnt:=(cAlsCTK)->FILDESP
			cEmpAnt := U_TIRCVEMP(cFilAnt)
			cNomeDest:=Alltrim(FWSM0Util():getSM0FullName())
			cApeOri:=Alltrim(oInter:GetEmpCgcNome((cAlsCTK)->ORIGEM))
			cApeDes:=Alltrim(oInter:GetEmpCgcNome((cAlsCTK)->DESTINO))
			cEmpAnt:=cEmpBkp
			iF(Empty(cApeOri))
				cApeOri:=cNomeOri
			EndIF
			iF(Empty(cApeDes))
				cApeDes:=cNomeDest
			EndIF
			cHist:= Alltrim((cAlsCTK)->HISTORICO)+ " "+SubStr(cApeDes,1,6) +" X "+SubStr(cApeOri,1,6)

			Aadd(aLinha,{"P57_FILIAL"	,xFilial("P57")			}) //P57_FILIAL
			Aadd(aLinha,{"P57_PROC"		,aParam[2]				})//P57_PROC
			Aadd(aLinha,{"P57_REGSAI"	,aParam[4]				})//P57_REGSAI
			Aadd(aLinha,{"P57_REGENT"	,aParam[5]				})//P57_REGENT
			Aadd(aLinha,{"P57_COMPET"	,aParam[3]				})//P57_COMPET
			Aadd(aLinha,{"P57_DTPROC"	,dDataBase				})//P57_DTPROC
			Aadd(aLinha,{"P57_USR"		,aParam[6]				})//P57_USR
			Aadd(aLinha,{"P57_CGCORI"	,(cAlsCTK)->ORIGEM		})//P57_CGCORI
			Aadd(aLinha,{"P57_CGCDES"	,(cAlsCTK)->DESTINO		})//P57_CGCDES
			Aadd(aLinha,{"P57_TIPO"		,(cAlsCTK)->TIPO_MOV	})//P57_TIPO
			Aadd(aLinha,{"P57_KEY"		,cKey					})//P57_KEY
			Aadd(aLinha,{"P57_STATUS"	,'1'					})//P57_STATUS
			Aadd(aLinha,{"P57_DTLCTO"	,dDataBase				})//P57_DTLCTO
			Aadd(aLinha,{"P57_LOTE"		,''						})//P57_LOTE
			Aadd(aLinha,{"P57_SBLOTE"	,''						})//P57_SBLOTE
			Aadd(aLinha,{"P57_DOC"		,''						})//P57_DOC
			Aadd(aLinha,{"P57_LINHA"	,''						})//P57_LINHA
			Aadd(aLinha,{"P57_MOEDLC"	,''						})//P57_MOEDLC
			Aadd(aLinha,{"P57_DC"		,'3'					})//P57_DC
			Aadd(aLinha,{"P57_DEBITO"	,(cAlsCTK)->DEBITO		})//P57_DEBITO
			Aadd(aLinha,{"P57_CREDIT"	,(cAlsCTK)->CREDITO		})//P57_CREDIT
			Aadd(aLinha,{"P57_DCD"		,""						})//P57_DCD
			Aadd(aLinha,{"P57_DCC"		,""						})//P57_DCC
			if(Alltrim((cAlsCTK)->TIPO_MOV)=='I' .And. Empty(Alltrim((cAlsCTK)->FILDESP)))//  TICONTIN-3035 se nao tiver filial veio de algum arquivo
				lTOTVSXRD:= (cAlsCTK)->ORIGEM $ cCNPJORI .And. (cAlsCTK)->DESTINO $ cCNPJDEST//Tratativa se for Totvs e RD pegar sempre o menor valor
				If(lTOTVSXRD)
					if ((cAlsCTK)->VALOR < (cAlsCTK)->VALORREC)
						Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
					Else
						Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALORREC		})//P57_VALOR DA RECEITA
					EndIF
				Else
					Aadd(aLinha,{"P57_VALOR"	,(cAlsCTK)->VALORREC		})//P57_VALOR DA RECEITA
				EndIF
			Else
				Aadd(aLinha,{"P57_VALOR",(cAlsCTK)->VALOR		})//P57_VALOR DA DESPESA
			EndIf
			Aadd(aLinha,{"P57_HIST"		,cHist					})//P57_HIST
			Aadd(aLinha,{"P57_CCD"		,(cAlsCTK)->P57_CCD		})//P57_CCD
			Aadd(aLinha,{"P57_CCC"		,(cAlsCTK)->P57_CCC		})//P57_CCC
			Aadd(aLinha,{"P57_ITEMD"	,(cAlsCTK)->P57_ITEMD	})//P57_ITEMD
			Aadd(aLinha,{"P57_ITEMC"	,(cAlsCTK)->P57_ITEMC	})//P57_ITEMC
			Aadd(aLinha,{"P57_CLVLDB"	,(cAlsCTK)->P57_CLVLDB	})//P57_CLVLDB
			Aadd(aLinha,{"P57_SEQUEN"	,''						})//P57_SEQUEN
			Aadd(aLinha,{"P57_CLVLCR"	,(cAlsCTK)->P57_CLVLCR	})//P57_CLVLCR
			Aadd(aLinha,{"P57_EMPORI"	,''						})//P57_EMPORI
			Aadd(aLinha,{"P57_FILORI"	,(cAlsCTK)->FILREC		})//P57_FILORI
			Aadd(aLinha,{"P57_DATATX"	,dDataBase				})//P57_DATATX
			Aadd(aLinha,{"P57_TAXA"		,0						})//P57_TAXA
			Aadd(aLinha,{"P57_INTERC"	,'1'					})//P57_INTERC
			Aadd(aLinha,{"P57_ORIGEM"	,'SAIDAS2'				})//P57_ORIGEM
			Aadd(aLinha,{"P57_ROTINA"	,'TCTBA053'				})//P57_ROTINA
			Aadd(aLinha,{"P57_LP"		,''						})//P57_LP
			Aadd(aLinha,{"P57_VLR01"	,(cAlsCTK)->VALOR		})//P57_VLR01
			Aadd(aLinha,{"P57_VLR02"	,0						})//P57_VLR02
			Aadd(aLinha,{"P57_FLEX01"	,(cAlsCTK)->F2_DOC		})//P57_FLEX01
			Aadd(aLinha,{"P57_FLEX02"	,(cAlsCTK)->F1_DOC		})//P57_FLEX02
			Aadd(aLinha,{"P57_FLEX03"	,' '					})//P57_FLEX03
			Aadd(aLinha,{"P57_FLEX04"	,' '					})//P57_FLEX04
			Aadd(aLinha,{"P57_FLEX05"	, ' '					})//P57_FLEX05
			Aadd(aLinha,{"P57_BAIXA"	, CtOd("  /  /    ")	})//P57_BAIXA
			Aadd(aLinha,{"P57_TRACKE"	, ' '					})//P57_TRACKE
			Aadd(aLinha,{"P57_ID"		, cId					})//P57_ID
			Aadd(aLinha,{"P57_IDINT"	, ' '					})//P57_IDINT
			nPosOri := aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim((cAlsCTK)->ORIGEM) })
			nPosDes	:= aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim((cAlsCTK)->DESTINO) })
			cOriEmp	:=""
			cDesEmp	:=""
			If(nPosOri>0)
				cOriEmp:=aCgcDre[nPosOri][2]
			EndIF
			If(nPosDes>0)
				cDesEmp:=aCgcDre[nPosDes][2]
			EndIF
			Aadd(aLinha,{"P57_ORIEMP"	,cOriEmp			})//P57_ORIEMP
			Aadd(aLinha,{"P57_DESTEMP"	,cDesEmp			})//P57_DESTEMP
			Aadd(aLinha,{"P57_IDDESP"	,' '				})//P57_IDDESP
			Aadd(aLinha,{"P57_IDREC"	,' '				})//P57_IDREC
			aLinha:=oInter:ReorderBulk(aStrFld,aLinha)
			if(Len(aLinha)>0)
				oBulkCre:AddData(aLinha)
			EndIF

			//Atualiza as despesas
			P57->(DbGoTo((cAlsCTK)->RCNODE))
			RecLock("P57", .F.)
			P57->P57_STATUS ='3'
			P57->(MsUnlock())

			//Grva dados para rastreio
			BulkTre(oInter,(cAlsCTK)->RCNODE, (cAlsCTK)->RCNOREC, cId,'D')
			(cAlsCTK)->(DbSkip())
		EndDo
		If Select(cAlsCTK) > 0
			(cAlsCTK)->(dbCloseArea())
		EndIf
		if !oBulkCre:Flush()
			Alert(oBulkCre:GetError())
		EndIF
		CATCHEXCEPTION USING oError
		IF ( ValType( oError ) == "O" )
			cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
			cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )
			FWMonitorMsg( "TIINTERCOMPANY ERRO: "+cMsgFault )
		EndIF

		ENDEXCEPTION
		if oBulkCre:Close()

		EndIF
		oBulkCre:Destroy()
		oBulkCre := nil
		FREEOBJ( oBulkCre )
	EndIF
Return
/*/{Protheus.doc} BulkTre
	Inseri linhas dos lancamentos na P58 Tabela de rastreio de lancamentos
	@type    Function
	<AUTHOR> Menabue Lima
	@since 26/08/2022
	@version 12.1.33
/*/
Static Function BulkTre(oInter, nRecOri, nRecDest, cIDInt,cTipo)

	Local 	oBulkTre := FWBulk():New(RetSqlName("P58"))

	Local cIdOri 	:= ''
	Local cIdDest 	:= ''
	Local cCgcOri 	:= ''
	Local cCgcDest 	:= ''
	Local nValor 	:= 0
	Local cContaC 	:= ''
	Local cCCC 		:= ''
	Local cItemC	:= ''
	Local cClasC 	:= ''
	Local cDebito 	:= ''
	Local cCCD 		:= ''
	Local cItemD 	:= ''
	Local cClasD 	:= ''
	Local oError
	Local cMsgFault := ""
	Local aLinha    := {}
	Local aStrFld  :=   P58->(DbStruct())
	if  FwBulk():CanBulk()
		TRYEXCEPTION

		oBulkTre:setFields(aStrFld)

		aLinha:={}
		FWMonitorMsg( cThread+" Gravando dados de tracker "+cValToChar(nLinha)  +" de "+ cValToChar(nTotal) )
		//Posicionando na P57 DA Origem
		P57->(DbGoTo(nRecOri))
		cIdOri 	:=	P57->P57_ID

		cCgcOri :=	P57->P57_CGCORI
		cCgcDest:=	P57->P57_CGCDES
		nValor 	:=	P57->P57_VALOR
		IF (cTipo=="D")
			cDebito :=	P57->P57_DEBITO
			cCCD 	:= 	P57->P57_CCD
			cItemD 	:=	P57->P57_ITEMD
			cClasD 	:=	P57->P57_CLVLDB
			P57->(DbGoTo(nRecDest))
			cIdDest :=	P57->P57_ID
			cContaC :=	P57->P57_CREDIT
			cCCC 	:= 	P57->P57_CCC
			cItemC	:= 	P57->P57_ITEMC
			cClasC 	:=	P57->P57_CLVLCR
		Else
			cContaC :=	P57->P57_CREDIT
			cCCC 	:= 	P57->P57_CCC
			cItemC	:= 	P57->P57_ITEMC
			cClasC 	:=	P57->P57_CLVLCR
			P57->(DbGoTo(nRecDest))
			cIdDest :=	P57->P57_ID
			cDebito :=	P57->P57_DEBITO
			cCCD 	:= 	P57->P57_CCD
			cItemD 	:=	P57->P57_ITEMD
			cClasD 	:=	P57->P57_CLVLDB
		EndIF


		Aadd(aLinha,{"P58_FILIAL"	,xFilial("P58")	}) //P58_FILIAL
		Aadd(aLinha,{"P58_PROC"		,aParam[2]		})//P58_PROC
		Aadd(aLinha,{"P58_COMPET"	,aParam[3]		})//P58_COMPET
		Aadd(aLinha,{"P58_REGSAI"	,aParam[4]		})//P58_REGSAI
		Aadd(aLinha,{"P58_REGENT"	,aParam[5]		})//P58_REGENT
		Aadd(aLinha,{"P58_TIPO"		,cTipo			})//P58_TIPO
		Aadd(aLinha,{"P58_IDORI"	,cIdOri			})//P58_IDORI
		Aadd(aLinha,{"P58_IDDEST"	,cIdDest		})//P58_IDDEST
		Aadd(aLinha,{"P58_CGCORI"	,cCgcOri		})//P58_CGCORI
		Aadd(aLinha,{"P58_CGCDES"	,cCgcDest		})//P58_CGCDES
		Aadd(aLinha,{"P58_VALOR"	,nValor			})//P58_VALOR
		Aadd(aLinha,{"P58_CREDIT"	,cContaC		})//P58_CREDIT
		Aadd(aLinha,{"P58_CCC"		,cCCC			})//P58_CCC
		Aadd(aLinha,{"P58_ITEMC"	,cItemC			})//P58_ITEMC
		Aadd(aLinha,{"P58_CLVLCR"	,cClasC			})//P58_CLVLCR
		Aadd(aLinha,{"P58_DEBITO"	,cDebito		})//P58_DEBITO
		Aadd(aLinha,{"P58_CCD"		,cCCD			})//P58_CCD
		Aadd(aLinha,{"P58_ITEMD"	,cItemD			})//P58_ITEMD
		Aadd(aLinha,{"P58_CLVLDB"	,cClasD			})//P58_CLVLDB
		Aadd(aLinha,{"P58_IDINT"	,cIDInt			})//P58_IDINT
		nPosOri := aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim(cCgcOri) })
		nPosDes	:= aScan(aCgcDre, {|x| Alltrim(x[1]) == Alltrim(cCgcDest) })
		cOriEmp	:=""
		cDesEmp	:=""
		If(nPosOri>0)
			cOriEmp:=aCgcDre[nPosOri][2]
		EndIF
		If(nPosDes>0)
			cDesEmp:=aCgcDre[nPosDes][2]
		EndIF
		Aadd(aLinha,{"P58_ORIEMP",cOriEmp		})//P58_ORIEMP
		Aadd(aLinha,{"P58_DESTEMP",cDesEmp		})//P58_DESTEMP
		Aadd(aLinha,{"P58_STATUS",'1'			})//P58_STATUS
		aLinha:=oInter:ReorderBulk(aStrFld,aLinha)
		if(Len(aLinha)>0)
			oBulkTre:AddData(aLinha)
		EndIF


		if !oBulkTre:Flush()
			Alert(oBulkTre:GetError())
		EndIF
		if oBulkTre:Close()

		EndIF
		CATCHEXCEPTION USING oError
		IF ( ValType( oError ) == "O" )
			cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
			cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )
			FWMonitorMsg( "TIINTERCOMPANY ERRO: "+cMsgFault )
		EndIF

		ENDEXCEPTION
		oBulkTre:Destroy()
		oBulkTre := nil
		FREEOBJ( oBulkTre )
	EndIF
Return
/*/{Protheus.doc} getPerio
	Pega o periodo parcelando entre a quantidade total de threads da regra
	@type   Function
	<AUTHOR> Menabue Lima
	@since 09/09/2022
	@version 12.1.33
 
/*/
Static Function getPer(aParam)
	Local dDataIni:= Stod(Substr(aParam[3],3,4)+Substr(aParam[3],1,2)+"01")
	Local dDataFim:= LastDay(dDataIni,0)
	Local dDataFim2:=dDataFim
	Local nTotThre:= 1
	Local cProc   := aParam[2]
	Local cRegSai := aParam[4]
	Local cRegEnt := aParam[5]
	Local aRange  := {}
	Local nI	  := 0
	Local nPosThr := 0
	Local cChvTrd := SubStr(cThread,Len(cThread)-2,2)
	Local cBetWeen:="'"+DTos(dDataIni)+"' AND '"+Dtos(dDataFim)+"'"
	DbSelectArea("P56")
	DbSetOrder(1)//P56_FILIAL+P56_CODIGO+P56_REGRA+P56_REGENT+P56_SEQ
	IF dbSeek(xFilial("P56")+cProc+cRegSai+cRegEnt)
		nTotThre:= P56->P56_NTRHD
		//Aplica a Divisao para achar qual range e da thread Corrente
		nTotDia:= dDataFim-dDataIni
		nDPerTrd:=Int(nTotDia/nTotThre)
		For nI:= 1 to nTotThre
			dDataFim:= dDataIni+nDPerTrd
			If(dDataFim>dDataFim2 .Or. nI == nTotThre)
				dDataFim:=dDataFim2
			EndIF
			cBetw:="'"+DTos(dDataIni)+"' AND '"+Dtos(dDataFim)+"'"
			dDataIni:=dDataFim+1
			Aadd(aRange,{StrZero(nI,2),cBetw})
		Next nI
		nPosThr := aScan(aRange, {|x| Alltrim(x[1]) == Alltrim(cChvTrd) })
		If(nPosThr>0)
			cBetWeen:=aRange[nPosThr][2]
		EndIF
	EndIF
Return cBetWeen
