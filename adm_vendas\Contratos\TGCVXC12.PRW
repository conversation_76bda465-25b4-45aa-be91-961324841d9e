#Include "Totvs.ch"


User Function TGCVXC12()


/*
"M-CA<PERSON><PERSON><PERSON>"
"M-IN<PERSON>EM<PERSON>"
"M-MANUAL"
"M-REAJUSTE"
"M-OSCILA" 
"M-REATIVA"
"M-SITUAC "
"M-TRANFS" 
"M-TRANSFE"
"M-TROCA"
"M-VLNOVA  "
"M-VLTOTAL "

"C-BON<PERSON><PERSON> "
"C-CANCELA "
"C-CARENCIA"
"C-MANUAL"
"C-SITUAC "
"C-TROCA   "
*/

  

Return


Class TGCVXC12

	Data oResumo    as TGCVXC11
	Data cAmFecha   as String
	Data cAmPH5     as String
	Data nRecPh5    as Numeric
	Data nReajAnt   as Numeric
	Data nTamCli    as Numeric
	Data nTamLoj    as Numeric
	Data nTamUni    as Numeric
	Data nTamPerI   as Numeric
	Data nTamPerF   as Numeric
	Data nTamMot    as Numeric
	Data nTamChurn  as Numeric
	Data nTamTipPh3 as Numeric
	Data aPH3       as Array
	Data aPh4C      as Array
	Data aPh4B      as Array
	Data aPH5       as Array
	Data aPSV       as Array
	Data aCalcBoni  as Array
	Data aCliBonAp  as Array //Controle para calculo de bonificação para não duplicar 
	Data lTemIncr   as Boolean
	Data lProcAnt   as Boolean
    Data lIntera    as Boolean
	Data aTipos    as Array
	Data aMRR       as Array
	Data aChurn     as Array

	Data aEMRR       as Array
	Data aEChurn     as Array

	Method New(oResumo, cAmFecha, cAmPH5)
	Method Processa()
	Method ChkIntera()
	Method ProcMRR()
	Method ProcAMRR()
	Method ProcMTrans(nPosPh5, cCpoPH5, cTpMRR, nFator)
	Method TransfPH8(nPosPh5, cTipTrf, cAMPH5)
	Method ProcMVlTot()
	Method ProcMCanc()
	Method ProcReaj()
	Method ProcFlgCorp()
	Method ProcPH5Corp()

	Method ProcChurn()
	Method ProcBonif()
	Method ProcCaren()
	Method ProcCancel()
	Method ProcTroca()

	Method LoadPH3()
	Method LoadPH4()
	Method LoadPH5()
	Method LoadPSV()
	Method CroBonif()
	Method TrataEstorno(cTipo)
	Method LoadEstorno(cTipo, aAux)
	Method AtuResumo(cTipo)
	Method Calcula()
	
	Method RetPH5()
	Method RetPSV()
	Method Ret()

	Method Destroy()

EndClass

Method New(oResumo, cAmFecha, cAmPH5)  Class TGCVXC12

	::oResumo   := oResumo
	::cAmFecha  := cAmFecha
	::cAmPH5    := cAmPH5
	::nRecPh5   := 0
	::nReajAnt  := 0
	::nTamCli    := FwTamSx3("PSV_CLIENT")[1]
	::nTamLoj    := FwTamSx3("PSV_LOJA")[1]
	::nTamUni    := FwTamSx3("PSV_UNINEG")[1]
	::nTamPerI   := FwTamSx3("PSV_PERINI")[1]
	::nTamPerF   := FwTamSx3("PSV_PERFIM")[1]
	::nTamMot    := FwTamSx3("PSV_MOTIVO")[1]
	::nTamChurn  := FwTamSx3("PSV_CHURN")[1]
	::nTamTipPh3 := FwTamSx3("PSV_TIPPH3")[1]
	::aPH3      := {}
	::aPH4C     := {}
	::aPH4B     := {}
	::aPH5      := {}
	::aPSV      := {}
	::aCalcBoni := {}
	::aCliBonAp := {}
	::lTemIncr	:= .F.
	::lProcAnt := .F.

	::aTipos    := {"M-VLTOTAL" ,;
					"M-VLNOVA"  ,;
					"M-REAJUSTE",;
					"M-INCREMEN",;
					"M-CANCELA" ,;
					"M-REATIVA" ,;
					"M-TRANFS"  ,;
					"M-TRANSFE" ,;
					"M-OSCILA"  ,;
					"M-TROCA"   ,;
					"M-CORPORAT",;
					"M-MANUAL"  ,;
					"M-SITUAC"  ,;
					"C-BONIFIC" ,;
					"C-CARENCIA",;
					"C-CANCELA" ,;
					"C-TROCA"   ,;
					"C-MANUAL"  ,;
					"C-SITUAC"   }

	::aMRR      := {}
	::aChurn    := {}
	::aEMRR      := {}
	::aEChurn    := {}
    ::lIntera   := .F.		
Return

Method Processa() Class TGCVXC12
    Self:ChkIntera()	
	If !Empty(::aph5)
		Self:ProcMRR()
		Self:ProcChurn()
	EndIf
	
Return

Method ChkIntera() Class TGCVXC12

	Local cChave    := ""
	Local cProposta := ""
	Local cProduto  := ""
	Local cAgrupa   := ""
	Local cModal    := ""

	::lIntera := .F.

	cChave := FwXFilial("CNB")
	cChave += ::oResumo:cContra + ::oResumo:cRevisa
	cChave += ::oResumo:cNumero + ::oResumo:cItem

    CNB->(DbSetOrder(1))  //CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM
    IF ! CNB->(DbSeek(cChave))
		Return
	Endif

	cProduto  := CNB->CNB_PRODUT
	cProposta := CNB->CNB_PROPOS

	ADY->(dbSetOrder(1))   //ADY_FILIAL+ADY_PROPOS
	ADY->(dbSeek(FWxFilial("ADY") + cProposta ))
	
	cAgrupa := ADY->ADY_XCODAG
	cModal  := ADY->ADY_XMODAL 

	PT6->(DbSetOrder(1))
	If ! PT6->(dbSeek(xFilial("PT6") + cAgrupa + cModal)) .or. ! PT6->PT6_FOTOGR $ "2345"
		Return
	EndIf 
	
	If ! U_GCVIIILI(cProduto, cAgrupa, cModal) //verifica se produto é intera ilimitado
		Return
	EndIf 

	::lIntera := .T.
	
Return

Method ProcMRR() Class TGCVXC12
	Local nx    := 0
	Local lLast := .F.

	For nx:=1 to Len(::aPH5)
		lLast := nx == Len(::aPH5)
		Self:ProcMVlTot(nx, "PH5_VLTOT" , "M-VLTOTAL" ,  1, lLast)
        Self:ProcMVlTot(nx, "PH5_VLNOVA", "M-VLNOVA"  ,  1, lLast)
		Self:ProcMCanc( nx, "PH5_VLCANC", "M-CANCELA" , -1)
		Self:ProcAMRR(  nx, "PH5_VLREA" , "M-REATIVA" ,  1)
		Self:ProcAMRR(  nx, "PH5_VLRTRC", "M-TROCA"   , -1)
		Self:ProcAMRR(  nx, "PH5_VLINCR", "M-INCREMEN",  1)
		Self:ProcMTrans(nx, "PH5_VLTRAN", "M-TRANFS"  , 1) 
		Self:ProcMTrans(nx, "PH5_VLTRAE", "M-TRANSFE" , 1) 
		Self:ProcAMRR(  nx, "PH5_VLINTE", "M-OSCILA",  1) 
	Next

	Self:ProcFlgCorp()

	For nx:=1 to Len(::aTipos)
		If Left(::aTipos[nx], 1) == "M"
			Self:AtuResumo(::aTipos[nx])
		EndIf
	Next

Return

Method ProcChurn() Class TGCVXC12
	Local nx       := 0
	Local cCli1    := ""
	Local cCli2    := ""


	For nx:=1 to Len(::aPH5)	
		Self:ProcBonif(nx, 1)
		Self:ProcCaren(nx, 1)
		Self:ProcTroca(nx)

		cCli1 := Self:RetPH5(nx, "PH5_CLIENT")
		If nx < Len(::aPH5)
			cCli2 := Self:RetPH5(nx + 1, "PH5_CLIENT")
			If cCli1 <> cCli2
				Self:ProcCancel(nx, 1)
			EndIf
		Else
			Self:ProcCancel(nx, 1)
		EndIf	
	
	Next

	For nx:=1 to Len(::aTipos)
		If Left(::aTipos[nx], 1) == "C"
			Self:TrataEstorno(::aTipos[nx])
			Self:AtuResumo(::aTipos[nx])
		EndIf
	Next
Return



Method TrataEstorno(cTipo) Class TGCVXC12
	Local nx        := 0
	Local cAMPH5    := ""
	Local nDesc     := "" 
	Local cMotivo   := ""
	Local cChurn    := ""
	Local cMRR      := "" 
	Local cIniPer   := ""
	Local cFimPer   := ""
	Local cObs      := ""
	Local cTipoPH3  := ""
	Local cAmResu	:= ""
	Local cCliente  := ""
	Local cLoja     := ""
	Local cSeq      := ""
    Local cAmFecha  := ::cAmFecha 
	Local cCtrOri   := ""
	Local cCtrDes   := ""
	Local dDtInc    := Ctod("")
	Local cTotParc  := "" 



	For nx := 1 to Len(::aPSV)
		cObs := "Estorno do mês " + Self:RetPSV(nx, "PSV_AMFECH" ) 
		If ! cTipo == AllTrim(Self:RetPSV(nx, "PSV_TIPO")) 
			Loop
		EndIf

		cAMPH5    := Self:RetPSV(nx, "PSV_AMCTR" ) 
		nDesc     := Self:RetPSV(nx, "PSV_VALOR" ) * -1
		cMotivo   := Self:RetPSV(nx, "PSV_MOTIVO") 
		cChurn    := Self:RetPSV(nx, "PSV_CHURN" ) 
		cMRR      := Self:RetPSV(nx, "PSV_MRR"   ) 
		cTipoPH3  := Self:RetPSV(nx, "PSV_TIPPH3")
		cIniPer   := Self:RetPSV(nx, "PSV_PERINI") 
		cFimPer   := Self:RetPSV(nx, "PSV_PERFIM") 
		cAmResu   := Self:RetPSV(nx, "PSV_AMFECH")
		cCliente  := Self:RetPSV(nx, "PSV_CLIENT")
		cLoja     := Self:RetPSV(nx, "PSV_LOJA"  )
		cUniNeg   := Self:RetPSV(nx, "PSV_UNINEG")
		cNota     := Self:RetPSV(nx, "PSV_NOTA"  )
		cSerie    := Self:RetPSV(nx, "PSV_SERIE" )
		cItemNf   := Self:RetPSV(nx, "PSV_ITEMNF")
		cSeq      := Self:RetPSV(nx, "PSV_SEQ"   )
		cCtrOri   := Self:RetPSV(nx, "PSV_CTRORI")
		cCtrDes   := Self:RetPSV(nx, "PSV_CTRDES")
		dDtInc    := Self:RetPSV(nx, "PSV_DTINCP")
		cTotParc  := Self:RetPSV(nx, "PSV_TOTPAR") 




		aAux     := {}
		AADD(aAux, {"AMFECHA" , cAmFecha })    
		AADD(aAux, {"AMPH5"   , cAMPH5   })
		AADD(aAux, {"VALOR"   , nDesc    })
		AADD(aAux, {"ESTORNO" , "S"      })
		AADD(aAux, {"AMRESUMO", cAmResu  })
		AADD(aAux, {"OBSERV"  , cObs     })
		AADD(aAux, {"MOTIVO"  , cMotivo  })
		AADD(aAux, {"CHURN"   , cChurn   })
		AADD(aAux, {"MRR"     , cMRR     })
		AADD(aAux, {"TIPOPH3" , cTipoPH3 })
		AADD(aAux, {"PERINI"  , cIniPer  })
		AADD(aAux, {"PERFIM"  , cFimPer  })
		AADD(aAux, {"CLIENTE" , cCliente })
		AADD(aAux, {"LOJA"    , cLoja    })
		AADD(aAux, {"UNINEG"  , cUniNeg  })	
		AADD(aAux, {"NOTA"    , cNota    })	
		AADD(aAux, {"SERIE"   , cSerie   })	
		AADD(aAux, {"ITEMNF"  , cItemNf  })
		AADD(aAux, {"SEQ"     , cSeq     })
		AADD(aAux, {"CTRORI"  , cCtrOri  })
		AADD(aAux, {"CTRDES"  , cCtrDes  })
		AADD(aAux, {"DTINC"   , dDtInc   })
		AADD(aAux, {"TOTPAR"  , cTotParc }) 

		Self:LoadEstorno(cTipo, aAux)
	
	Next

Return

Method LoadEstorno(cTipo, aAux) Class TGCVXC12
	Local nPosAux := 0

	If Left(cTipo, 1) == "M"
		nPosAux := Ascan(::aEMRR, {|x| x[1] == cTipo})
		If nPosAux == 0
			AADD(::aEMRR, {cTipo, {}})
			nPosAux    := Len(::aEMRR)
		EndIf
		AADD(::aEMRR[nPosAux, 2], aClone(aAux) )
	Else
		nPosAux := Ascan(::aEChurn, {|x| x[1] == cTipo})
		If nPosAux == 0
			AADD(::aEChurn, {cTipo, {}})
			nPosAux    := Len(::aEChurn)
		EndIf
		AADD(::aEChurn[nPosAux, 2], aClone(aAux) )
	EndIf
Return

Method AtuResumo(cTipo) Class TGCVXC12
	Local nx       := 0
	Local lEstorna := .F.
	Local aAux     := {}
	Local aEAux    := {}
	Local aRAux    := {}
	Local nPosCalc := 0
	Local nPosEst  := 0
	Local nPosRes  := 0
	Local nPosAux  := 0
	Local cChvAux  := ""
	Local nValor   := 0 
	Local cClient  := ""
	Local cMotivo  := ""
	Local cChurn   := ""
	Local cPeriod  := ""
	Local cTipPh3  := ""
	Local cUniNeg  := ""
	Local aCalcPSV := {}
	Local aEstPSV  := {}

	If Left(cTipo, 1) == "M"
		nPosCalc := Ascan(::aMRR, {|x| x[1] == cTipo})
		If nPosCalc > 0
			aAux     := ::aMRR[nPosCalc, 2]
		EndIf

		nPosEst := Ascan(::aEMRR, {|x| x[1] == cTipo})
		If nPosEst > 0
			aEAux     := ::aEMRR[nPosEst, 2]
		EndIf

		nPosRes := Ascan(::oResumo:aRMRR, {|x| x[1] == cTipo})
		If nPosRes == 0
			AADD(::oResumo:aRMRR, {cTipo, {}})
			nPosRes := Len(::oResumo:aRMRR)
		EndIf
		aRAux     := ::oResumo:aRMRR[nPosRes, 2]
	Else
		nPosCalc := Ascan(::aChurn, {|x| x[1] == cTipo})
		If nPosCalc > 0
			aAux     := ::aChurn[nPosCalc, 2]
		EndIf

		nPosEst := Ascan(::aEChurn, {|x| x[1] == cTipo})
		If nPosEst > 0
			aEAux     := ::aEChurn[nPosEst, 2]
		EndIf

		nPosRes := Ascan(::oResumo:aRChurn, {|x| x[1] == cTipo})
		If nPosRes == 0
			AADD(::oResumo:aRChurn, {cTipo, {}})
			nPosRes := Len(::oResumo:aRChurn)
		EndIf
		aRAux     := ::oResumo:aRChurn[nPosRes, 2]
	EndIf

	If Empty(aEAux)
		If !Empty(aAux)
			For nx :=1 to Len(aAux)
				AADD(aRAux,aAux[nx]) 
			Next
			Self:Calcula(cTipo, aRAux)
		EndIf
		
		Return
	EndIf


	For nx := 1 to Len(aAux)
		nValor 	:= Round(ABS(Self:Ret(aAux, nx, "VALOR")), 2)
		cClient := Self:Ret(aAux, nx, "CLIENTE") + Self:Ret(aAux, nx, "LOJA") 
		cUniNeg := Self:Ret(aAux, nx, "UNINEG" ) 
		cMotivo := Self:Ret(aAux, nx, "MOTIVO" )
		cChurn  := Self:Ret(aAux, nx, "CHURN"  )
		cPeriod := Self:Ret(aAux, nx, "PERINI" ) + Self:Ret(aAux, nx, "PERFIM") 
		cTipPh3 := Self:Ret(aAux, nx, "TIPOPH3")
		cChvAux := cClient + cUniNeg + cMotivo + cChurn + cPeriod + cTipPh3

		nPosAux := aScan(aCalcPSV, {|x| x[1] == cChvAux })

		If nPosAux > 0
			aCalcPSV[nPosAux,2] +=  nValor	
		Else
			AADD(aCalcPSV, {cChvAux, nValor })
		EndIf

	Next

	For nx := 1 to Len(aEAux)

		nValor 	:= Round(ABS(Self:Ret(aEAux, nx, "VALOR")), 2)
		cClient := Self:Ret(aEAux, nx, "CLIENTE") + Self:Ret(aEAux, nx, "LOJA") 
		cUniNeg := Self:Ret(aEAux, nx, "UNINEG" ) 
		cMotivo := Self:Ret(aEAux, nx, "MOTIVO" )
		cChurn  := Self:Ret(aEAux, nx, "CHURN"  )
		cPeriod := Self:Ret(aEAux, nx, "PERINI" ) + Self:Ret(aEAux, nx, "PERFIM") 
		cTipPh3 := Self:Ret(aEAux, nx, "TIPOPH3")
		cChvAux := cClient + cUniNeg + cMotivo + cChurn + cPeriod + cTipPh3

		nPosAux := aScan(aEstPSV, {|x| x[1] == cChvAux })

		If nPosAux >  0
			aEstPSV[nPosAux,2] +=  nValor
		Else
			AADD(aEstPSV, {cChvAux, nValor })	
		EndIf

	Next

	If Len(aCalcPSV) == Len(aEstPSV)
		aSort(aCalcPSV,,,{|x,y| x[1] < y[1]})
		aSort(aEstPSV ,,,{|x,y| x[1] < y[1]})
		For nx := 1 to Len(aCalcPSV)
			If aCalcPSV[nx, 1] <> aEstPSV[nx, 1]
				lEstorna := .T.
				Exit
			EndIf
			If aCalcPSV[nx, 2] <> aEstPSV[nx, 2]
				lEstorna := .T.
				Exit
			EndIf
		Next
	Else
		lEstorna := .T.
	EndIf
	
	If lEstorna	
		For nx :=1 to Len(aEAux)
			AADD(aRAux,aEAux[nx]) 
		Next

		For nx :=1 to Len(aAux)
			AADD(aRAux,aAux[nx]) 
		Next

		Self:Calcula(cTipo, aRAux)
	EndIf
Return


Method Calcula(cTipo, aRAux) Class TGCVXC12
	Local cCliente := ""
	Local cLoja    := ""
	Local cAmFecha := ""
	Local cChurn   := ""
	Local nX       := 0
	Local nP       := 0


	For Nx:=1 to Len(aRAux)
		cCliente := Self:Ret(aRaux, Nx, "CLIENTE")
		cLoja    := Self:Ret(aRaux, Nx, "LOJA"   )
		cAmFecha := Self:Ret(aRaux, Nx, "AMFECHA")
		cChurn   := Self:Ret(aRaux, Nx, "CHURN"  )
		nP := Ascan(::oResumo:aResumo,{|x| x[1, 2] + x[2, 2] + x[3, 2] + x[4, 2]== cAmFecha + cCliente + cLoja + cTipo })

		If nP == 0 
			AADD(::oResumo:aResumo, {{"FECHA", cAmFecha}, {"CLIENTE",cCliente}, {"LOJA", cLoja} , {"TIPO", cTipo}, {"VALOR", 0}})
			nP := Len(::oResumo:aResumo)
		EndIf
		If nx == 1
			::oResumo:aResumo[nP, 5, 2]  := 0
		EndIf

		If Left(cTipo, 1) == "C" .and. cChurn == "N"
			Loop
		EndIf

		::oResumo:aResumo[nP, 5, 2] += Self:Ret(aRaux, Nx, "VALOR") 		
	Next

Return


Method ProcAMRR(nPosPh5, cCpoPH5, cTpMRR, nFator) Class TGCVXC12
	Local cAMPH5   := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nValor   := self:RetPH5(nPosPH5, cCpoPH5) * nFator
	Local cObs     := ""
	Local cAmResu  := ""
	Local cCliente := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja    := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg  := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota    := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie   := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf  := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq     := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local cMotivo  := Space(::nTamMot)
	Local aAux     := {}
	Local nPosMRR  := Ascan(::aMrr, {|x| x[1] == cTpMRR})
	Local nCondic  := ::oResumo:nCondic
	Local cAmFecha := cAMPH5
	Local cMrr     := "S"
	Local cChurn   := "N"
	Local nx       := 0
	Local cIniPer  := Space(::nTamPerI)
	Local cFimPer  := Space(::nTamPerF)
	Local cTipoPH3 := Space(::nTamTipPh3)


	If ::oresumo:cTipRec == "2"
		cMrr := "N"
	EndIf

	If cTpMRR == "M-INCREMEN" .and. nValor > 0
		::lTemIncr := .T.
	EndIf

	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf

	If nValor <> 0
		nValor := nValor / nCondic
		If cAmFecha < ::oResumo:cAmIniFat
			nCondic := 1
		Endif
		For nx:=1 to nCondic
			If cAmFecha >= ::cAmFecha
				aAux := {}
				cObs     := "Lançamento do mês " + AmtoCMP(cAmFecha) 

				AADD(aAux, {"AMFECHA" , cAmFecha})
				AADD(aAux, {"AMPH5"   , cAMPH5  })
				AADD(aAux, {"VALOR"   , nValor  })
				AADD(aAux, {"ESTORNO" , "N"     })
				AADD(aAux, {"AMRESUMO", cAmResu })
				AADD(aAux, {"OBSERV"  , cObs    })
				AADD(aAux, {"MOTIVO"  , cMotivo }) 
				AADD(aAux, {"CHURN"   , cChurn  })	
				AADD(aAux, {"MRR"     , cMrr    })
				AADD(aAux, {"TIPOPH3" , cTipoPH3})
				AADD(aAux, {"PERINI"  , cIniPer }) 
				AADD(aAux, {"PERFIM"  , cFimPer }) 
				AADD(aAux, {"CLIENTE" , cCliente})
				AADD(aAux, {"LOJA"    , cLoja   })
				AADD(aAux, {"UNINEG"  , cUniNeg })	
				AADD(aAux, {"NOTA"    , cNota   })	
				AADD(aAux, {"SERIE"   , cSerie  })	
				AADD(aAux, {"ITEMNF"  , cItemNf })				
				AADD(aAux, {"SEQ"     , cSeq    })
				AADD(aAux, {"CTRORI"  , ""      })	
				AADD(aAux, {"CTRDES"  , ""      })	
				AADD(aAux, {"DTINC"   , Ctod("")})	
				AADD(aAux, {"TOTPAR"  , ""      })	
				AADD(::aMRR[nPosMRR, 2], aClone(aAux) )
			EndIf

			cAmFecha := SomaAM(cAmFecha, 1) 
		Next
	EndIf		

Return


Method ProcMTrans(nPosPh5, cCpoPH5, cTpMRR, nFator) Class TGCVXC12
	Local cAMPH5    := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nValor    := self:RetPH5(nPosPH5, cCpoPH5) * nFator
	Local cObs      := ""
	Local cAmResu   := ""
	Local cCliente  := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja     := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg   := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota     := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie    := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf   := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq      := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local cMotivo   := Space(::nTamMot)
	Local aAux      := {}
	Local nPosMRR   := Ascan(::aMrr, {|x| x[1] == cTpMRR})
	Local nCondic   := ::oResumo:nCondic
	Local cAmFecha  := cAMPH5
	Local cMrr      := "S"
	Local cChurn    := "N"
	Local nx        := 0
	Local cIniPer   := Space(::nTamPerI)
	Local cFimPer   := Space(::nTamPerF)
	Local cTipoPH3  := Space(::nTamTipPh3)
	Local cTipTrf   := ""
	Local cCtrOri   := ""
	Local cCtrDes   := ""


	If ::oresumo:cTipRec == "2"
		cMrr := "N"
	EndIf

	If cCpoPH5 == "PH5_VLTRAE"
		cTipTrf := "E"
	Else
		cTipTrf := "S"
	EndIf	


	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf

	If nValor <> 0
		If cTipTrf == "S"
			cCtrDes := Self:TransfPH8(nPosPh5, cTipTrf, cAMPH5) 
		Else
			cCtrOri := Self:TransfPH8(nPosPh5, cTipTrf, cAMPH5) 
		EndIf

		nValor := nValor / nCondic
		If cAmFecha < ::oResumo:cAmIniFat
			nCondic := 1
		Endif
		For nx:=1 to nCondic
			If cAmFecha >= ::cAmFecha
				aAux := {}
				cObs     := "Lançamento do mês " + AmtoCMP(cAmFecha) 

				AADD(aAux, {"AMFECHA" , cAmFecha})
				AADD(aAux, {"AMPH5"   , cAMPH5  })
				AADD(aAux, {"VALOR"   , nValor  })
				AADD(aAux, {"ESTORNO" , "N"     })
				AADD(aAux, {"AMRESUMO", cAmResu })
				AADD(aAux, {"OBSERV"  , cObs    })
				AADD(aAux, {"MOTIVO"  , cMotivo }) 
				AADD(aAux, {"CHURN"   , cChurn  })	
				AADD(aAux, {"MRR"     , cMrr    })
				AADD(aAux, {"TIPOPH3" , cTipoPH3})
				AADD(aAux, {"PERINI"  , cIniPer }) 
				AADD(aAux, {"PERFIM"  , cFimPer }) 
				AADD(aAux, {"CLIENTE" , cCliente})
				AADD(aAux, {"LOJA"    , cLoja   })
				AADD(aAux, {"UNINEG"  , cUniNeg })	
				AADD(aAux, {"NOTA"    , cNota   })	
				AADD(aAux, {"SERIE"   , cSerie  })	
				AADD(aAux, {"ITEMNF"  , cItemNf })				
				AADD(aAux, {"SEQ"     , cSeq    })
				AADD(aAux, {"CTRORI"  , cCtrOri })	
				AADD(aAux, {"CTRDES"  , cCtrDes })
				AADD(aAux, {"DTINC"   , CtoD("")})	
				AADD(aAux, {"TOTPAR"  , ""      })	
				AADD(::aMRR[nPosMRR, 2], aClone(aAux) )
			EndIf

			cAmFecha := SomaAM(cAmFecha, 1) 
		Next
	EndIf		

Return

Method TransfPH8(nPosPh5, cTipTrf, cAMPH5) Class TGCVXC12
	Local cContrato := self:RetPH5(nPosPH5, "PH5_CONTRA")
	Local cContrRet	:= ""
	Local cNumero   := self:RetPH5(nPosPH5, "PH5_NUMERO")
	Local cItem     := self:RetPH5(nPosPH5, "PH5_ITEM"  ) 
	Local cChvPH8   := FwxFilial("PH8") + cContrato + cNumero + cItem
	Local cCmpPH5   := AmtoCMP(cAMPH5)

	PH8->(DbSetOrder(3)) // PH8_FILIAL+PH8_CONTRA+PH8_NUMERO+PH8_ITEM

	PH8->(DbSeek(cChvPH8))

	While PH8->(!Eof()) .And. PH8->(PH8_FILIAL + PH8_CONTRA + PH8_NUMERO + PH8_ITEM) == cChvPH8
		
		If PH8->PH8_TM <> cTipTrf
			PH8->(DbSkip())
			Loop
		EndIf

		If PH8->PH8_COMPET <> cCmpPH5
			PH8->(DbSkip())
			Loop
		EndIf

		If cTipTrf == "S"
			cContrRet := PH8->PH8_CTRDES
		Else
			cContrRet := PH8->PH8_CTRORI
		EndIf

		Exit
	End

Return cContrRet



Method ProcMVlTot(nPosPh5, cCpoPH5, cTpMRR, nFator, lLast) Class TGCVXC12
	Local cAMPH5   := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nVlIncr  := self:RetPH5(nPosPh5, "PH5_VLINCR") * nFator
	Local nVlReat  := self:RetPH5(nPosPh5, "PH5_VLREA" ) * nFator
	//Local nVlBoni  := self:RetPH5(nPosPh5, "PH5_VLBONI") * nFator 
	Local nValor   := self:RetPH5(nPosPH5, cCpoPH5) * nFator
	Local cObs     := ""
	Local cAmResu  := ""
	Local cCliente := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja    := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg  := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota    := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie   := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf  := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq     := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local nVlTrae  := self:RetPH5(nPosPH5, "PH5_VLTRAE") 
	Local aAux     := {}
	Local nPosMRR  := Ascan(::aMrr, {|x| x[1] == cTpMRR})
	Local nCondic  := ::oResumo:nCondic
	Local cAmFecha := cAMPH5
	Local cMrr     := "S"
	Local cChurn   := "N"
	Local nx       := 0
	Local nPH5_SLDCAN := 0
	Local nPH5_VLRFAT := 0
	Local nPerDev     := 0
	Local cMotivo  := Space(::nTamMot)
	Local cIniPer  := Space(::nTamPerI)
	Local cFimPer  := Space(::nTamPerF)
	Local cTipoPH3 := Space(::nTamTipPh3)

	If nVlTrae > 0
		Return
	EndIf
	

	If ::oresumo:cTipRec == "2"
		cMrr := "N"
	EndIf

	If nValor > 0
        nValor -= nVlIncr
        nValor -= nVlReat

	EndIf

	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf

	nPH5_SLDCAN := self:RetPH5(nPosPH5, "PH5_SLDCAN")
	nPH5_VLRFAT := self:RetPH5(nPosPH5, "PH5_VLRFAT")

	nPerDev     := nPH5_SLDCAN / nPH5_VLRFAT


	::nReajAnt := 0

	If nValor > 0 .And. lLast
		Self:ProcReaj(  nPosPh5, "PH5_VLREAJ", "M-REAJUSTE",  1)
	EndIf 

	nValor := nValor - ::nReajAnt

	If cCpoPH5 == "PH5_VLTOT" .Or. cCpoPH5 == "PH5_VLNOVA"
		
		If nPH5_SLDCAN > 0
			nPH5_SLDCAN := nValor * nPerDev
			nValor -= (nPH5_SLDCAN * nFator)
		EndIf
	EndIF

	If nValor <> 0
		nValor := nValor / nCondic
		If cAmFecha < ::oResumo:cAmIniFat
			nCondic := 1
		Endif
		For nx:=1 to nCondic
			If cAmFecha >= ::cAmFecha 
					
				aAux := {}
				cObs     := "Lançamento do mês " + AmtoCMP(cAmFecha) 
				AADD(aAux, {"AMFECHA" , cAmFecha})
				AADD(aAux, {"AMPH5"   , cAMPH5  })
				AADD(aAux, {"VALOR"   , nValor  })
				AADD(aAux, {"ESTORNO" , "N"     })
				AADD(aAux, {"AMRESUMO", cAmResu })
				AADD(aAux, {"OBSERV"  , cObs    })
				AADD(aAux, {"MOTIVO"  , cMotivo })
				AADD(aAux, {"CHURN"   , cChurn  })
				AADD(aAux, {"MRR"     , cMrr    })
				AADD(aAux, {"TIPOPH3" , cTipoPH3})	 
				AADD(aAux, {"PERINI"  , cIniPer }) 
				AADD(aAux, {"PERFIM"  , cFimPer }) 			
				AADD(aAux, {"CLIENTE" , cCliente})
				AADD(aAux, {"LOJA"    , cLoja   })
				AADD(aAux, {"UNINEG"  , cUniNeg })	
				AADD(aAux, {"NOTA"    , cNota   })	
				AADD(aAux, {"SERIE"   , cSerie  })	
				AADD(aAux, {"ITEMNF"  , cItemNf })					
				AADD(aAux, {"SEQ"     , cSeq    })
				AADD(aAux, {"CTRORI"  , ""      })
				AADD(aAux, {"CTRDES"  , ""      })	
				AADD(aAux, {"DTINC"   , Ctod("")})		
				AADD(aAux, {"TOTPAR"  , ""      })	
				AADD(::aMRR[nPosMRR, 2], aClone(aAux) )
			EndIf

			cAmFecha := SomaAM(cAmFecha, 1) 
		Next
	EndIf	
Return


Method ProcMCanc(nPosPh5, cCpoPH5, cTpMRR, nFator) Class TGCVXC12
	Local cAMPH5   := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nValor   := self:RetPH5(nPosPh5, cCpoPH5     )  * nFator
	Local nVlTranf := self:RetPH5(nPosPh5, "PH5_VLTRAN")  * nFator * -1
	Local cObs     := "" 
	Local cAmResu  := ""
	Local cCliente := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja    := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg  := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota    := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie   := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf  := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq     := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local nVlTotal := self:RetPH5(nPosPH5, "PH5_VLTOT" )	
	Local nVlCancel:= self:RetPH5(nPosPH5, "PH5_VLCANC")
	Local lTotParc := .T.	
	Local aAux     := {}
	Local nPosMRR  := Ascan(::aMrr, {|x| x[1] == cTpMRR})
	Local nCondic  := ::oResumo:nCondic
	Local cAmFecha := cAMPH5
	Local cMrr     := "S"
	Local cChurn   := "N"
	Local nx       := 0
	Local cMotivo  := Space(::nTamMot)
	Local cIniPer  := Space(::nTamPerI)
	Local cFimPer  := Space(::nTamPerF)
	Local cTipoPH3 := Space(::nTamTipPh3)
	Local dDtInc   := CtoD("")
	Local cTotParc := "" 

	If nVlTotal ==  nVlCancel
		cTotParc := "TOTAL"
		lTotParc := .F.
	EndIf

	For nx:= 1 to len(::aPH3)
		dDtInc   := ::aPH3[nx, 10]
		If lTotParc
			cTotParc := IIF(::aPH3[nx, 3] == "T","TOTAL","PARCIAL")
		EndIf	
	Next


	If ::oresumo:cTipRec == "2"
		cMrr := "N"
	EndIf


	If nVlTranf != 0  
		nValor := nValor - nVlTranf
	EndIf

	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf

	If nValor <> 0
		nValor := nValor / nCondic
		If cAmFecha < ::oResumo:cAmIniFat
			nCondic := 1
		Endif
		For nx:=1 to nCondic
			If cAmFecha >= ::cAmFecha
				
				aAux := {}
				cObs     := "Lançamento do mês " + AmtoCMP(cAmFecha) 
				AADD(aAux, {"AMFECHA" , cAmFecha})
				AADD(aAux, {"AMPH5"   , cAMPH5  })
				AADD(aAux, {"VALOR"   , nValor  })
				AADD(aAux, {"ESTORNO" , "N"     })
				AADD(aAux, {"AMRESUMO", cAmResu })
				AADD(aAux, {"OBSERV"  , cObs    })
				AADD(aAux, {"MOTIVO"  , cMotivo })
				AADD(aAux, {"CHURN"   , cChurn  })
				AADD(aAux, {"MRR"     , cMrr    })
				AADD(aAux, {"TIPOPH3" , cTipoPH3})
				AADD(aAux, {"PERINI"  , cIniPer }) 
				AADD(aAux, {"PERFIM"  , cFimPer }) 				
				AADD(aAux, {"CLIENTE" , cCliente})
				AADD(aAux, {"LOJA"    , cLoja   })
				AADD(aAux, {"UNINEG"  , cUniNeg })	
				AADD(aAux, {"NOTA"    , cNota   })	
				AADD(aAux, {"SERIE"   , cSerie  })	
				AADD(aAux, {"ITEMNF"  , cItemNf })					
				AADD(aAux, {"SEQ"     , cSeq    })
				AADD(aAux, {"CTRORI"  , ""      })
				AADD(aAux, {"CTRDES"  , ""      })			
				AADD(aAux, {"DTINC"   , dDtInc  })
				AADD(aAux, {"TOTPAR"  , cTotParc})
							
				AADD(::aMRR[nPosMRR, 2], aClone(aAux) )
			EndIf

			cAmFecha := SomaAM(cAmFecha, 1) 
		Next
	EndIf	

Return

Method ProcReaj(nPosPh5, cCpoPH5, cTpMRR, nFator) Class TGCVXC12
	Local cAMPH5   := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nQtdPH5  := self:RetPH5(nPosPh5, "PH5_QUANT" )
	Local nValor   := self:RetPH5(nPosPH5, cCpoPH5) * nFator
	Local cObs     := ""
	Local cAmResu  := ""
	Local cCliente := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja    := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg  := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota    := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie   := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf  := self:RetPH5(nPosPH5, "PH5_ITEMNF") 	
	Local cSituac  := self:RetPH5(nPosPH5, "PH5_SITUAC")
	Local cSeq     := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 	
	Local aAux     := {}
	Local nPosMRR  := Ascan(::aMrr, {|x| x[1] == cTpMRR})
	
	Local nCondic  := ::oResumo:nCondic
	Local cAmFecha := cAMPH5
	Local cMrr     := "S"
	Local cChurn   := "N"
	Local nx       := 0
	Local lIsSaas  := U_GVFUNSA2(::oResumo:cGrpSBM)
	Local cMotivo  := Space(::nTamMot)
	Local cIniPer  := Space(::nTamPerI)
	Local cFimPer  := Space(::nTamPerF)
	Local cTipoPH3 := Space(::nTamTipPh3)

	If ::oresumo:cTipRec == "2"
		cMrr := "N"
	EndIf
	
		
	If cSituac $ "CO"
		Return 0
	EndIf

	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf
	
	If nValor <> 0
		nValor := nValor / nCondic
		If cAmFecha < ::oResumo:cAmIniFat
			nCondic := 1
		Endif
		For nx:=1 to nCondic
			If cAmFecha >= ::cAmFecha
				aAux := {}
				cObs     := "Lançamento do mês " + AmtoCMP(cAmFecha) 

				AADD(aAux, {"AMFECHA" , cAmFecha})
				AADD(aAux, {"AMPH5"   , cAMPH5  })
				AADD(aAux, {"VALOR"   , nValor  })
				AADD(aAux, {"ESTORNO" , "N"     })
				AADD(aAux, {"AMRESUMO", cAmResu })
				AADD(aAux, {"OBSERV"  , cObs    })
				AADD(aAux, {"MOTIVO"  , cMotivo })
				AADD(aAux, {"CHURN"   , cChurn  }) 
				AADD(aAux, {"MRR"     , cMrr    })
				AADD(aAux, {"TIPOPH3" , cTipoPH3})
				AADD(aAux, {"PERINI"  , cIniPer }) 
				AADD(aAux, {"PERFIM"  , cFimPer }) 
				AADD(aAux, {"CLIENTE" , cCliente})
				AADD(aAux, {"LOJA"    , cLoja   })	
				AADD(aAux, {"UNINEG"  , cUniNeg })	
				AADD(aAux, {"NOTA"    , cNota   })	
				AADD(aAux, {"SERIE"   , cSerie  })	
				AADD(aAux, {"ITEMNF"  , cItemNf })									
				AADD(aAux, {"SEQ"     , cSeq    })
				AADD(aAux, {"CTRORI"  , ""      })
				AADD(aAux, {"CTRDES"  , ""      })	
				AADD(aAux, {"DTINC"   , Ctod("")})		
				AADD(aAux, {"TOTPAR"   , ""     })		
				AADD(::aMRR[nPosMRR, 2], aClone(aAux) )
			EndIf

			cAmFecha := SomaAM(cAmFecha, 1) 
		Next
	EndIf	
Return 

Static Function SubAnoMes(cAM)
	Local cMes := Right(cAm, 2) 
	Local cAno := Left( cAm, 4)

	If Empty(cAM)
		Return "      "
	EndIf

	If cMes == "01"
		cMes := "12"
		cAno := Tira1(cAno)
	else
		cMes := Tira1(cMes)
	EndIf
Return cAno + cMes


Method ProcBonif(nPosPH5, nFator) Class TGCVXC12

	Local nVlBonif  := Self:RetPH5(nPosPH5, "PH5_VLBONI")
    Local nx        := 0
	Local cChurn   := "N"
	Local cMotivo   := ""
	Local cIniBonif := ""
	Local cFimBonif := ""
	Local cAMPH5    := self:RetPH5(nPosPH5, "PH5_ANOMES")
	Local nVlrTot   := self:RetPH5(nPosPH5, "PH5_VLTOT" ) + self:RetPH5(nPosPH5, "PH5_VLNOVA") + self:RetPH5(nPosPH5, "PH5_VLREAJ")

	Local cTipo     := ""
	Local nDesc     := 0
	Local nPerc     := 0
	Local lVlrInfo  := .F.
	Local cObs      := ""
	Local cCliente  := ""
	Local cLoja     := ""
	Local cAmResu   := ""
	Local aAux      := {}
	Local nPosChurn := Ascan(::aChurn, {|x| x[1] == "C-BONIFIC"})
    Local cPH5Cli   := Self:RetPH5(nPosPH5, "PH5_CLIENT")
	Local cPH5Loja  := Self:RetPH5(nPosPH5, "PH5_LOJA"  )
	Local cUniNeg   := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota     := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie    := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf   := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq      := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
    Local cDesUN    := ""
    Local lUNChurn  := .T.
    Local np        := 0 
	Local nPosCli   := 0
	Local cTipoPH3  := Space(::nTamTipPh3)
	Local dDtInc    := CtoD("")
	Local cTotParc  := "" 

    np := ascan(::oResumo:aNoChurn, {|x| x[1] == cUniNeg })
    If ! Empty(np)
        cDesUN := ::oResumo:aNoChurn[np, 2]
        lUNChurn := .F.
    EndIf 

	If nVlBonif == 0
		Return
	EndIf

	nPosCli := Ascan(::aCliBonAp, cCliente)
	If nPosCli > 0
		Return
	EndIf

	AADD(::aCliBonAp, cCliente)

	If nPosChurn == 0
		AADD(::aChurn, {"C-BONIFIC", {}})
		nPosChurn    := Len(::aChurn)
	EndIf
	

	::aCalcBoni := {}
	::aCliBonAp := {}
	Self:CroBonif(nPosPH5)


	For nx := 1 to len(::aCalcBoni)

		cTipo     := ::aCalcBoni[nx, 7]
		nDesc     := ::aCalcBoni[nx, 8]
		nPerc     := ::aCalcBoni[nx, 9]
		lVlrInfo  := ::aCalcBoni[nx, 10]
		cMotivo   := ::aCalcBoni[nx, 5]
		cChurn    := ::aCalcBoni[nx, 6]
		cIniBonif := ::aCalcBoni[nx, 1]
		cFimBonif := ::aCalcBoni[nx, 2]
		cCliente  := ::aCalcBoni[nx, 3]
		cLoja     := ::aCalcBoni[nx, 4]
		dDtInc    := ::aCalcBoni[nx, 11]

        cObs      := "Lançamento do mês " + AmtoCMP(cAMPH5)
        If ! lUNChurn .and. cChurn == "S"
            cChurn := "N"
            cObs += " Churn não devido a filial " + cUniNeg + "-" + cDesUN
        EndIf  

		If cTipo == "P"
			nDesc := nVlrTot * nPerc / 100
		EndIf

		nDesc := nDesc * nFator 

		aAux     := {}

		AADD(aAux, {"AMFECHA" , ::cAMFecha})
		AADD(aAux, {"AMPH5"   , cAMPH5    })
		AADD(aAux, {"VALOR"   , nDesc     })
		AADD(aAux, {"ESTORNO" , "N"       })
		AADD(aAux, {"AMRESUMO", cAmResu   })
		AADD(aAux, {"OBSERV"  , cObs      })
		AADD(aAux, {"MOTIVO"  , cMotivo   })
		AADD(aAux, {"CHURN"   , cChurn    })
		AADD(aAux, {"MRR"     , "N"       })
		AADD(aAux, {"TIPOPH3" , cTipoPH3  })
		AADD(aAux, {"PERINI"  , cIniBonif })
		AADD(aAux, {"PERFIM"  , cFimBonif })
		AADD(aAux, {"CLIENTE" , cPH5Cli   })
		AADD(aAux, {"LOJA"    , cPH5Loja  })
		AADD(aAux, {"UNINEG"  , cUniNeg   })	
		AADD(aAux, {"NOTA"    , cNota     })	
		AADD(aAux, {"SERIE"   , cSerie    })	
		AADD(aAux, {"ITEMNF"  , cItemNf   })
		AADD(aAux, {"SEQ"     , cSeq      })
		AADD(aAux, {"CTRORI"  , ""        })
		AADD(aAux, {"CTRDES"  , ""        })		
		AADD(aAux, {"DTINC"   , dDtInc    })		
		AADD(aAux, {"TOTPAR"  , cTotParc  })		

		AADD(::aChurn[nPosChurn, 2], aClone(aAux) )

	Next
Return

Method CroBonif(nPosPH5) Class Tgcvxc12
	Local nx:= 0
	Local aBonifica := ::aPH4b
	Local cCliente  := ""
	Local cLoja     := ""
	Local cAMIni    := ""
	Local cAMFin    := ""
	Local cTpDesc   := ""
	Local lVlrInfo  := .F.
	Local nVlrBoni  := 0
	LOcal nPerBoni  := 0
	Local nPerRat   := Self:RetPH5(nPosPH5, "PH5_PERRAT")
	Local cPH5Cli   := Self:RetPH5(nPosPH5, "PH5_CLIENT")
	Local cPH5Loja  := Self:RetPH5(nPosPH5, "PH5_LOJA"  )
	Local cAnomes   := Self:RetPH5(nPosPH5, "PH5_ANOMES")
	Local cMotivo   := ""
	Local cChurn    := "N"
	Local dDtInc    := CtoD("")

	If nPerRat == 0
		nPerRat := 100
	EndIf

	For nx:= 1 to len(aBonifica)
		cCliente  := aBonifica[nx, 3]
		cLoja     := aBonifica[nx, 4]
		cAMIni    := CmptoAM(aBonifica[nx, 1])
		cAMFin    := CmptoAM(aBonifica[nx, 2])

		cTpDesc   := aBonifica[nx, 7]
		nVlrBoni  := aBonifica[nx, 8]
		nPerBoni  := aBonifica[nx, 9]
		cMotivo   := aBonifica[nx, 5]
		cChurn    := aBonifica[nx, 6]
		dDtInc    := aBonifica[nx, 10]
		lVlrInfo   := .F.

		If nPerRat < 100
			If !Empty(cCliente + cLoja)
				If cCliente + cLoja == cPH5Cli + cPH5Loja
					lVlrInfo := .T.
				Else
					Loop
				EndIf
			Else
				lVlrInfo := .T. //atendendo pedido aplica valor cheio para todos clientes 02/12/2019
			EndIf
		EndIf
		If cAnoMes >= cAMIni .and. cAnoMes <= cAMFin
			If cTpDesc == "P"
				lVlrInfo := .T.
			EndIf

			aadd(::aCalcBoni, {cAMIni, cAMFin, cCliente, cLoja, cMotivo, cChurn, cTpDesc, nVlrBoni, nPerBoni, lVlrInfo, dDtInc})
		EndIf
	Next
Return


Method ProcCaren(nPosPH5, nFator) Class TGCVXC12
	Local nVlCaren  := Self:RetPH5(nPosPH5, "PH5_VLCARE")
	Local nx        := 0
	Local cChurn   := "N"
	Local cMotivo   := ""
	Local cIniCaren := ""
	Local cFimCaren := ""
	Local cAMPH5    := Self:RetPH5(nPosPH5, "PH5_ANOMES")
	Local cObs      := ""
	Local cCliente  := Self:RetPH5(nPosPH5, "PH5_CLIENT")
	Local cLoja     := Self:RetPH5(nPosPH5, "PH5_LOJA"  )
	Local cUniNeg   := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota     := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie    := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf   := self:RetPH5(nPosPH5, "PH5_ITEMNF")
	Local cSeq      := self:RetPH5(nPosPH5, "PH5_SEQ"   )  
	Local cCliPH4   := ""
	Local cLojPH4   := ""
	Local cAmResu   := ""
	Local aAux      := {}
	Local nPosChurn := Ascan(::aChurn, {|x| x[1] == "C-CARENCIA"})

    Local cDesUN    := ""
    Local lUNChurn  := .T. 
    Local np        := 0 
	Local lFindPH4  := .F.
	Local cTipoPH3  := Space(::nTamTipPh3)
	Local dDtInc    := CtoD("")
	Local cTotParc  := ""

    np := ascan(::oResumo:aNoChurn, {|x| x[1] == cUniNeg })
    If ! Empty(np)
        cDesUN := ::oResumo:aNoChurn[np, 2]
        lUNChurn := .F.
    EndIf 

	If nPosChurn == 0
		AADD(::aChurn, {"C-CARENCIA", {}})
		nPosChurn    := Len(::aChurn)
	EndIf

	If nVlCaren == 0
		Return
	EndIf

	nVlCaren := nVlCaren * nFator

	For nx := 1 to Len(::aPH4C)
		If !(cAMPH5 >= CmpToAm(::aPH4C[nx, 1]) .And. cAMPH5 <= CmpToAm(::aPH4C[nx, 2]))
			loop
		EndIf
		cChurn    := ::aPH4C[nx, 6]
		cMotivo   := ::aPH4C[nx, 5]
		cIniCaren := CmpToAm(::aPH4C[nx, 1])
		cFimCaren := CmpToAm(::aPH4C[nx, 2])
		cCliPH4   := ::aPH4C[nx, 3] 
		cLojPH4   := ::aPH4C[nx, 4] 
		dDtInc    := ::aPH4C[nx, 8] 

		If !cCliente == cCliPH4
			Loop
		EndIf

		cObs      := "Lançamento do mês " + AmtoCMP(cAMPH5) 
		If ! lUNChurn .and. cChurn == "S"
			cChurn := "N"
			cObs += " Churn não devido a filial " + cUniNeg + "-" + cDesUN
		EndIf  	

		lFindPH4 := .T.
		
		aAux     := {}

		AADD(aAux, {"AMFECHA" , ::cAMFecha})
		AADD(aAux, {"AMPH5"   , cAMPH5    })
		AADD(aAux, {"VALOR"   , nVlCaren  })
		AADD(aAux, {"ESTORNO" , "N"       })
		AADD(aAux, {"AMRESUMO", cAmResu   })
		AADD(aAux, {"OBSERV"  , cObs      })
		AADD(aAux, {"MOTIVO"  , cMotivo   })
		AADD(aAux, {"CHURN"   , cChurn    })
		AADD(aAux, {"MRR"     , "N"       })
		AADD(aAux, {"TIPOPH3" , cTipoPH3  })
		AADD(aAux, {"PERINI"  , cIniCaren })
		AADD(aAux, {"PERFIM"  , cFimCaren })
		AADD(aAux, {"CLIENTE" , cCliente  })
		AADD(aAux, {"LOJA"    , cLoja     })
		AADD(aAux, {"UNINEG"  , cUniNeg   })	
		AADD(aAux, {"NOTA"    , cNota     })	
		AADD(aAux, {"SERIE"   , cSerie    })	
		AADD(aAux, {"ITEMNF"  , cItemNf   })
		AADD(aAux, {"SEQ"     , cSeq      })
		AADD(aAux, {"CTRORI"  , ""        })
		AADD(aAux, {"CTRDES"  , ""        })	
		AADD(aAux, {"DTINC"   , dDtInc    })
		AADD(aAux, {"TOTPAR"  , cTotParc  }) 
			
		AADD(::aChurn[nPosChurn, 2], aClone(aAux) )

		Exit
	Next

	If lFindPH4
		Return
	EndIF

	For nx := 1 to Len(::aPH4C)
		If !(cAMPH5 >= CmpToAm(::aPH4C[nx, 1]) .And. cAMPH5 <= CmpToAm(::aPH4C[nx, 2]))
			loop
		EndIf
		cChurn    := ::aPH4C[nx, 6]
		cMotivo   := ::aPH4C[nx, 5]
		cIniCaren := CmpToAm(::aPH4C[nx, 1])
		cFimCaren := CmpToAm(::aPH4C[nx, 2])
		cCliPH4   := ::aPH4C[nx, 3] 
		cLojPH4   := ::aPH4C[nx, 4] 
		dDtInc    := ::aPH4C[nx, 8] 

		If !Empty(cCliPH4) .And. !::oResumo:cMainCli == cCliPH4
			Loop
		EndIf

		cObs      := "Lançamento do mês " + AmtoCMP(cAMPH5) 
		If ! lUNChurn .and. cChurn == "S"
			cChurn := "N"
			cObs += " Churn não devido a filial " + cUniNeg + "-" + cDesUN
		EndIf  	
		
		aAux     := {}

		AADD(aAux, {"AMFECHA" , ::cAMFecha})
		AADD(aAux, {"AMPH5"   , cAMPH5    })
		AADD(aAux, {"VALOR"   , nVlCaren  })
		AADD(aAux, {"ESTORNO" , "N"       })
		AADD(aAux, {"AMRESUMO", cAmResu   })
		AADD(aAux, {"OBSERV"  , cObs      })
		AADD(aAux, {"MOTIVO"  , cMotivo   })
		AADD(aAux, {"CHURN"   , cChurn    })
		AADD(aAux, {"MRR"     , "N"       })
		AADD(aAux, {"TIPOPH3" , cTipoPH3  })
		AADD(aAux, {"PERINI"  , cIniCaren })
		AADD(aAux, {"PERFIM"  , cFimCaren })
		AADD(aAux, {"CLIENTE" , cCliente  })
		AADD(aAux, {"LOJA"    , cLoja     })
		AADD(aAux, {"UNINEG"  , cUniNeg   })	
		AADD(aAux, {"NOTA"    , cNota     })	
		AADD(aAux, {"SERIE"   , cSerie    })	
		AADD(aAux, {"ITEMNF"  , cItemNf   })
		AADD(aAux, {"SEQ"     , cSeq      })
		AADD(aAux, {"CTRORI"  , ""        })
		AADD(aAux, {"CTRDES"  , ""        })	
		AADD(aAux, {"DTINC"   , dDtInc    })
		AADD(aAux, {"TOTPAR"  , cTotParc  }) 
		AADD(::aChurn[nPosChurn, 2], aClone(aAux) )

		Exit
	Next

Return

Method ProcCancel(nPosPH5, nFator) Class TGCVXC12
	Local nx        := 1
	Local cAmPH5    := Self:RetPH5(nPosPH5, "PH5_ANOMES")
	Local nVlUnit   := Self:RetPH5(nPosPH5, "PH5_VLUNIT")
	Local nPerRat   := Self:RetPH5(nPosPH5, "PH5_PERRAT")
	Local cMotivo   := Space(::nTamMot)
	Local cTipoPH3  := Space(::nTamTipPh3)
	Local nValor    :=  0
	Local cChurn    := "N"
	Local cObs      := ""
	Local cObserv   := ""
	Local cAmResu   := ""
	Local cCliente  := Self:RetPH5(nPosPH5, "PH5_CLIENT")
	Local cLoja     := Self:RetPH5(nPosPH5, "PH5_LOJA"  )
	Local cUniNeg   := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota     := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie    := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf   := self:RetPH5(nPosPH5, "PH5_ITEMNF") 
	Local cSeq      := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local nVlTotal := self:RetPH5(nPosPH5, "PH5_VLTOT" )	
	Local nVlCancel:= self:RetPH5(nPosPH5, "PH5_VLCANC")
	Local lTotParc := .T.	
	Local aAux      := {}
	Local nPosChurn := Ascan(::aChurn, {|x| x[1] == "C-CANCELA"})
    Local cDesUN    := ""
    Local lUNChurn  := .T. 
    Local np        := 0 
	Local cIniPer   := Space(::nTamPerI)
	Local cFimPer   := Space(::nTamPerF)
	Local dDtInc    := CtoD("")
	Local cTotParc  := "" 

    np := ascan(::oResumo:aNoChurn, {|x| x[1] == cUniNeg })
    If ! Empty(np)
        cDesUN := ::oResumo:aNoChurn[np, 2]
        lUNChurn := .F.
    EndIf 

	If nPerRat > 0
		nVlUnit  := nVlUnit * nPerRat / 100
	EndIf

	If nPosChurn == 0
		AADD(::aChurn, {"C-CANCELA", {}})
		nPosChurn    := Len(::aChurn)
	EndIf

	If nVlTotal ==  nVlCancel
		cTotParc := "TOTAL"
		lTotParc := .F.
	EndIf

	//Destarcar a Qtd cancelada e reativação
	For nx:= 1 to len(::aPH3)
		cMotivo  := ::aPH3[nx, 5] 
		cTipoPH3 := ::aPH3[nx, 2]
		nValor   := ::aPH3[nx, 4] * nVlUnit 
		cChurn   := ::aPH3[nx, 9]
		dDtInc   := ::aPH3[nx, 10]
		
		If lTotParc
			cTotParc :=  IIF(::aPH3[nx, 3] == "T","TOTAL","PARCIAL")  
	    EndIf

        cObserv  := ""
		If cTipoPH3 == "R"
			cObserv := " Reativação"
			nValor := nValor * -1
		EndIf


        cObs     := "Lançamento do mês " + AmtoCMP(cAMPH5) + cObserv 
        If ! lUNChurn .and. cChurn == "S"
            cChurn := "N"
            cObs += " Churn não devido a filial " + cUniNeg + "-" + cDesUN
        EndIf  

		nValor := nValor * nFator

		aAux     := {}
		
		AADD(aAux, {"AMFECHA" , ::cAMFecha})
		AADD(aAux, {"AMPH5"   , cAMPH5    })
		AADD(aAux, {"VALOR"   , nValor    })
		AADD(aAux, {"ESTORNO" , "N"       })
		AADD(aAux, {"AMRESUMO", cAmResu   })
		AADD(aAux, {"OBSERV"  , cObs      })
		AADD(aAux, {"MOTIVO"  , cMotivo   })
		AADD(aAux, {"PERINI"  , cIniPer   }) 
		AADD(aAux, {"PERFIM"  , cFimPer   }) 
		AADD(aAux, {"CHURN"   , cChurn    })
		AADD(aAux, {"MRR"     , "N"       })
		AADD(aAux, {"TIPOPH3" , cTipoPH3  })
		AADD(aAux, {"CLIENTE" , cCliente  })
		AADD(aAux, {"LOJA"    , cLoja     })
		AADD(aAux, {"UNINEG"  , cUniNeg   })	
		AADD(aAux, {"NOTA"    , cNota     })	
		AADD(aAux, {"SERIE"   , cSerie    })	
		AADD(aAux, {"ITEMNF"  , cItemNf   })
		AADD(aAux, {"SEQ"     , cSeq      })
		AADD(aAux, {"CTRORI"  , ""        })
		AADD(aAux, {"CTRDES"  , ""        })		
		AADD(aAux, {"DTINC"   , dDtInc    })			
		AADD(aAux, {"TOTPAR"  , cTotParc  }) 

		AADD(::aChurn[nPosChurn, 2], aClone(aAux) )

	Next
Return

Method ProcTroca(nPosPh5) Class TGCVXC12
	Local cAMPH5   := self:RetPH5(nPosPh5, "PH5_ANOMES")
	Local nDltTrc  := self:RetPH5(nPosPH5, "PH5_DLTTRC")
	Local cObs     := "Lançamento do mês " + AmtoCMP(cAMPH5) 
	Local cAmResu  := ""
	Local cCliente := self:RetPH5(nPosPH5, "PH5_CLIENT")	
	Local cLoja    := self:RetPH5(nPosPH5, "PH5_LOJA"  ) 
	Local cUniNeg  := self:RetPH5(nPosPH5, "PH5_UNINEG")
	Local cNota    := self:RetPH5(nPosPH5, "PH5_NOTA"  ) 
	Local cSerie   := self:RetPH5(nPosPH5, "PH5_SERIE" ) 
	Local cItemNf  := self:RetPH5(nPosPH5, "PH5_ITEMNF") 
	Local cSeq     := self:RetPH5(nPosPH5, "PH5_SEQ"   ) 
	Local aAux     := {}
	Local cChurn   := "N"
	Local nPosChurn    := Ascan(::aChurn, {|x| x[1] == "C-TROCA"})
	Local cMotivo  := Space(::nTamMot)
	Local cIniPer  := Space(::nTamPerI)
	Local cFimPer  := Space(::nTamPerF)
	Local cTipoPH3 := Space(::nTamTipPh3)

	If nDltTrc == 0
		Return
	EndIf

	If nPosChurn == 0
		AADD(::aChurn, {"C-TROCA", {}})
		nPosChurn    := Len(::aChurn)
	EndIf

	nDltTrc := nDltTrc * -1

	If nDltTrc > 0
		cChurn   := "S"
	EndIf

	AADD(aAux, {"AMFECHA" , ::cAMFecha})
	AADD(aAux, {"AMPH5"   , cAMPH5    })
	AADD(aAux, {"VALOR"   , nDltTrc   })
	AADD(aAux, {"ESTORNO" , "N"       })
	AADD(aAux, {"AMRESUMO", cAmResu   })
	AADD(aAux, {"OBSERV"  , cObs      })
	AADD(aAux, {"MOTIVO"  , cMotivo   })
	AADD(aAux, {"CHURN"   , cChurn    })
	AADD(aAux, {"MRR"     , "N"       })
	AADD(aAux, {"TIPOPH3" , cTipoPH3  })	
	AADD(aAux, {"PERINI"  , cIniPer   }) 
	AADD(aAux, {"PERFIM"  , cFimPer   }) 
	AADD(aAux, {"CLIENTE" , cCliente  })
	AADD(aAux, {"LOJA"    , cLoja     })
	AADD(aAux, {"UNINEG"  , cUniNeg   })	
	AADD(aAux, {"NOTA"    , cNota     })	
	AADD(aAux, {"SERIE"   , cSerie    })	
	AADD(aAux, {"ITEMNF"  , cItemNf   })	
	AADD(aAux, {"SEQ"     , cSeq      })
	AADD(aAux, {"CTRORI"  , ""        })
	AADD(aAux, {"CTRDES"  , ""        })
	AADD(aAux, {"DTINC"   , Ctod("")  })	
	AADD(aAux, {"TOTPAR"  , ""		  })	
	

	AADD(::aChurn[nPosChurn, 2], aClone(aAux) )
	
Return


Method LoadPH5()  Class TGCVXC12
	Local cChavePH5 := ""
	Local aAreaPH5  := PH5->(GetArea("PH5"))
	Local aLinPH5   := {}
	Local nx        := 0
	Local nPosCli   := 0

	cChavePH5 := FwXFilial("PH5")
	cChavePH5 += ::oResumo:cContra + ::oResumo:cRevisa
	cChavePH5 += ::oResumo:cNumero + ::oResumo:cItem + ::cAmPH5

	PH5->(DbSetOrder(17))
	//PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM+PH5_ANOMES+PH5_SEQ+PH5_CLIENT+PH5_LOJA+PH5_CONDIC+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_GU

	PH5->(DbSeek(cChavePH5))
	While !PH5->(EOF()) .and. PH5->(PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM+PH5_ANOMES) == cChavePH5
		
		aSize(aLinPH5, 0)
		aLinPH5 := {}

		For nx:= 1 to PH5->(Fcount())
			If nPosCli == 0 .And. PH5->(FieldName(nx)) == "PH5_CLIENT"
				nPosCli := nx
			EndIf
			aadd(aLinPH5, {PH5->(FieldName(nx)), PH5->(FieldGet(nx))} )
		Next
		aadd(::aPH5, aClone(aLinPH5))

		PH5->(DbSkip())
	End

	aSort(::aPH5,,,{|x, y| x[nPosCli, 2] < y[nPosCli, 2]  } )

	RestArea(aAreaPH5)
Return

Method LoadPH3()  Class TGCVXC12
	Local aAreaPH3 := PH3->(GetArea("PH3"))
	Local cChave   := ""
	Local cOper    := ""  //P-Programado, C-Cancelado, R-Reativado
	Local cTipo    := ""  //T-Total, P-Parcial

	Local cAMPH3   := ""
	Local cAMCob   := ""
	Local nQtdCNB  := 0
	Local nQtdOpe  := 0
	Local cMotivo  := ""
	Local cSitAnt  := ""
	Local nMulta   := 0
	Local dDtInc   := Ctod("//")
	Local cAmRef   := ""

	::aPH3 := {}

	PH3->(DbSetOrder(5)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_ITEM+PH3_CMPCAN+PH3_ITSEQ

	cChave := FwXFilial("PH3")
	cChave += ::oResumo:cContra + ::oResumo:cRevisa
	cChave += ::oResumo:cNumero + ::oResumo:cItem

	PH3->(DbSeek(cChave))
	While PH3->(! Eof() .and. cChave == PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_ITEM)
		cOper    :=PH3->PH3_STATUS

		If ! cOper $ "PCR"
			PH3->(DbSkip())
			Loop
		EndIf

		If cOper == "P"
			nQtdCNB  := PH3->PH3_QUANT
			nQtdOpe  := PH3->PH3_QTDCAN
			cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
			cAMCob := ""
			cAmRef   := cAMPH3
		ElseIf cOper == "C"
 			nQtdCNB  := PH3->PH3_QUANT
			nQtdOpe  := PH3->PH3_QTDCAN
			cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
			cAMCob   := ""
			cAmRef   := cAMPH3
		ElseIf cOper == "R"
			nQtdCNB := 0
			nQtdOpe := PH3->PH3_QUANT
			cAMCob  := CmptoAM(PH3->PH3_CMPCOB)
			cAMPH3  := CmptoAM(PH3->PH3_CMPREA)
			If ! Empty(PH3->PH3_CMPCAN)
				PH3->(DbSkip())
				Loop
			EndIf
			If Empty(cAMCob)
				cAMCob := cAMPH3
			EndIF
			cAmRef   := cAMCob
		EndIf
		dDtInc := PH3->PH3_DATA
		cTipo   := If(nQtdCNB == nQtdOpe, "T", "P")
		cMotivo := PH3->PH3_MOTBC
		nMulta  := PH3->PH3_VLMULT
		cSitAnt := PH3->PH3_SITUAC

		cChurn := "N"
		PU4->(DbSetOrder(1)) //PU4_FILIAL+PU4_MOTBC+PU4_TIPO
		If PU4->(DbSeek(xFilial("PU4") + cMotivo + "X")) .And. PU4->PU4_CHURN == "1"
			cChurn := "S"
		EndIf
		If cAmRef == ::cAmPH5
			aadd(::aPH3, {cAMPH3, cOper, cTipo,  nQtdOpe, cMotivo, nMulta, cAMCob, cSitAnt, cChurn, dDtInc })
		EndIf
		PH3->(DbSkip())
	End
	RestArea(aAreaPH3)

	aSort(::aPH3,,, {|x,y| x[1] + x[3] < y[1] + y[3] })

Return


Method LoadPH4() Class TGCVXC12

	Local aAreaPH4  := PH4->(GetArea("PH4"))
	Local cChave    := ""
	Local cCarIni   := ""
	Local cCarFin   := ""
	Local cCliente  := ""
	Local cLoja     := ""
	Local lPrincipal:= .F.
	Local cTpDesc   := ""
	Local nVlrDes   := ""
	Local nPerDes   := ""
	Local cChurn    := "N"
	Local cMotivo   := ""
	Local dDtOper   := Ctod("")

	CNC->(DbSetOrder(5))
	If ! CNC->(DbSeek(xFilial("CNC") + ::oResumo:cContra  + ::oResumo:cRevisa  + "01"))
		::oResumo:cMsgErro := "Cliente não encontrado CNC!"
		Return
	EndIf

	PH4->(DbSetOrder(5))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ

	cChave := FwXFilial("PH4")
	cChave += ::oResumo:cContra + ::oResumo:cRevisa
	cChave += ::oResumo:cNumero + ::oResumo:cItem
	cChave += "C"  //B=BonificOper;C=Carencia

	PH4->(DbSeek(cChave))
	// necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
	While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_TIPO == cChave)
		If ! PH4->PH4_STATUS $ "AE"
			PH4->(DbSkip())
			Loop
		EndIf
		cCarIni  := PH4->PH4_CMPINI
		cCarFin  := PH4->PH4_CMPFIM
		cCliente := PH4->PH4_CLIENT
		cLoja    := PH4->PH4_LOJA
		cMotivo  := PH4->PH4_MOTBC
		dDtOper  := PH4->PH4_DTOPER 

		lPrincipal := CNC->(CNC_CLIENT + CNC_LOJACL) == cCliente + cLoja
		cChurn := "N"
		PU4->(DbSetOrder(1)) //PU4_FILIAL+PU4_MOTBC+PU4_TIPO
		If PU4->(DbSeek(xFilial("PU4") + cMotivo + "C")) .And. PU4->PU4_CHURN == "1"
			cChurn := "S"
		EndIf
		aadd(::aPH4C, {cCarIni, cCarFin, cCliente, cLoja, cMotivo, cChurn, lPrincipal, dDtOper})

		PH4->(DbSkip())
	End

	cChave := FwXFilial("PH4")
	cChave += ::oResumo:cContra + ::oResumo:cRevisa
	cChave += ::oResumo:cNumero + ::oResumo:cItem
	cChave += "B"  //B=BonificOper;C=Carencia

	PH4->(DbSeek(cChave))
	// necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
	While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_TIPO == cChave)
		If ! PH4->PH4_STATUS $ "AE"
			PH4->(DbSkip())
			Loop
		EndIf

		cCarIni  := PH4->PH4_CMPINI
		cCarFin  := PH4->PH4_CMPFIM
		cCliente := PH4->PH4_CLIENT
		cLoja    := PH4->PH4_LOJA
		cTpDesc  := PH4->PH4_TPDESC
		nVlrDes  := PH4->PH4_VLRDES
		nPerDes  := PH4->PH4_PERDES
		cMotivo  := PH4->PH4_MOTBC
		dDtOper  := PH4->PH4_DTOPER 

		cChurn := "N"
		PU4->(DbSetOrder(1)) //PU4_FILIAL+PU4_MOTBC+PU4_TIPO
		If PU4->(DbSeek(xFilial("PU4") + cMotivo + "B")) .And. PU4->PU4_CHURN == "1"
			cChurn := "S"
		EndIf
		aadd(::aPH4b, {cCarIni, cCarFin, cCliente, cLoja, cMotivo, cChurn, cTpDesc, nVlrDes, nPerDes, dDtOper})

		PH4->(DbSkip())
	End

	RestArea(aAreaPH4)
Return

Method LoadPSV() Class TGCVXC12
	Local aAreaPSV := PSV->(GetArea())
	Local cChvPSV  := ""
	Local aLinPSV  := {}
	Local nx       := 0
	Local cAmPSVF  := ""

	PSV->(DbSetOrder(2))
	//PSV_FILIAL+PSV_CONTRA+PSV_NUMERO+PSV_ITEM+PSV_AMCTR+PSV_AMFECH+PSV_AMRESU+PSV_CLIENT+PSV_LOJA+PSV_CHVPH5+PSV_SEQ+PSV_TIPO+PSV_OPERA                             

	cChvPSV := FwXFilial("PSV")
	cChvPSV += ::oResumo:cContra 
	cChvPSV += ::oResumo:cNumero + ::oResumo:cItem
	cChvPSV += ::cAmPH5

	PSV->(DbSeek(cChvPSV))
	While !PSV->(EOF()) .and. PSV->(PSV_FILIAL+PSV_CONTRA+PSV_NUMERO+PSV_ITEM+PSV_AMCTR) == cChvPSV
		If PSV->PSV_AMFECH >= ::cAmFecha
			PSV->(DbSkip())
			Loop
		EndIf

		If PSV->PSV_ESTORN == "S"
			PSV->(DbSkip())
			Loop
		EndIf

		If PSV->PSV_MANUAL == "S"
			PSV->(DbSkip())
			Loop
		EndIf		

		If cAmPSVF < PSV->PSV_AMFECH 
			cAmPSVF := PSV->PSV_AMFECH
			aSize(::aPSV, 0)
			::aPSV := {}
		EndIf
		
		aSize(aLinPSV, 0)
		aLinPSV := {}

		For nx:= 1 to PSV->(Fcount())
			aadd(aLinPSV, {PSV->(FieldName(nx)), PSV->(FieldGet(nx))} )
		Next
		aadd(::aPSV, aClone(aLinPSV))

		PSV->(DbSkip())
	End

	PSV->(RestArea(aAreaPSV))
Return


Method RetPH5(nLinha, cNameCpo) Class TGCVXC12
    Local uRet := NIL    
    Local np   := 0
    Local aFinanceiro := ::aPH5

    np:= Ascan(aFinanceiro[nLinha], {|x| x[1] == cNameCpo})
    uRet := aFinanceiro[nLinha, np, 2]

Return uRet


Method RetPSV(nLinha, cNameCpo) Class TGCVXC12
    Local uRet := NIL    
    Local np   := 0
    Local aPSV := ::aPSV

    np:= Ascan(aPSV[nLinha], {|x| x[1] == cNameCpo})
    uRet := aPSV[nLinha, np, 2]

Return uRet

Method Ret(aAux, nLinha, cNameCpo) Class TGCVXC12
    Local uRet := NIL    
    Local np   := 0

    np:= Ascan(aAux[nLinha], {|x| x[1] == cNameCpo})

	If np == 0
		Return ""
	EndIf

    uRet := aAux[nLinha, np, 2]

Return uRet


Method Destroy() Class TGCVXC12


	aSize(::aPH3     , 0)
	aSize(::aPh4C    , 0)
	aSize(::aPh4B    , 0)
	aSize(::aPH5     , 0)
	aSize(::aPSV     , 0)
	aSize(::aCalcBoni, 0)
	aSize(::aCliBonAp, 0)
	aSize(::aTipos   , 0)
	aSize(::aMRR     , 0)
	aSize(::aChurn   , 0)
	aSize(::aEMRR    , 0)
	aSize(::aEChurn  , 0)


	::aPH3      := Nil
	::aPh4C     := Nil
	::aPh4B     := Nil
	::aPH5      := Nil
	::aPSV      := Nil
	::aCalcBoni := Nil
	::aTipos    := Nil
	::aMRR      := Nil
	::aChurn    := Nil
	::aEMRR     := Nil
	::aEChurn   := Nil


Return

Static Function AMtoCmp(cAm)   
Return Right(cAm, 2) + "/" + Left(cAm, 4) 

Static Function CmptoAM(cCmp)   
Return Right(cCmp, 4) + Left(cCmp, 2) 

Static Function SomaAM(cAm, nMes) 
    Local cMes := ""
    Local cAno := ""
    Local ni   := 0

    For ni:= 1 to nMes
        cMes := Right(cAM, 2)
        cAno := Left(cAM, 4)

        If cMes == "12"
            cAM := soma1(cAno) + "01"
        Else 
            cAM := soma1(cAM)
        EndIf
    Next
Return cAM

//--> Antonio Nunes 
// 12/01/2021
Method ProcFlgCorp() Class TGCVXC12 
	Local cAmIni 	:= Left(::cAmFecha, 4)+"01"
	Local cChavePH5 := '' 

	If ::lProcAnt
		Return .T.
	EndIf

	If ! ::lTemIncr 
		Return .T.
	EndIf

	PH5->(DbSetOrder(17))
	//PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM+PH5_ANOMES+PH5_SEQ+PH5_CLIENT+PH5_LOJA+PH5_CONDIC+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_GU 

	While cAmIni < ::cAmFecha
		cChavePH5 := fwxFilial("PH5")+::oResumo:cContra+::oResumo:cRevisa+::oResumo:cNumero+::oResumo:cItem+cAmIni
	
		PH5->(dbSeek(cChavePH5))
		
		While cChavePH5 == PH5->(PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM+PH5_ANOMES)

			::ProcPH5Corp()

			PH5->(dbSkip())
		End
		cAmIni := SomaAM(cAmIni, 1)
	End

Return


Method ProcPH5Corp() Class TGCVXC12 //nPosPh5, cCpoPH5, cTpMRR, nFator
	Local cTpMRR	:= "M-CORPORAT"
	Local nVlTot    := 0
	Local nPosMRR   := 0
	Local cAMPH5 	:= PH5->PH5_ANOMES
	Local cUniNeg   := PH5->PH5_UNINEG
	Local cNota     := PH5->PH5_NOTA
	Local cSerie    := PH5->PH5_SERIE
	Local cItemNf   := PH5->PH5_ITEMNF 
	Local cSeq      := PH5->PH5_SEQ
	Local cMotivo   := Space(::nTamMot)
	Local cMRR      := "S"
	Local cChurn    := "N"
	Local cIniPer   := Space(::nTamPerI)
	Local cFimPer   := Space(::nTamPerF)
	Local cTipoPH3  := Space(::nTamTipPh3)

	
	If !(PH5->PH5_CMPCTB == ::cAmFecha .and. PH5->PH5_FLCORP = '1')
		Return
	EndIf

	nVlTot := PH5->PH5_VLTOT
	If nVlTot <= 0
		Return
	EndIf

	nPosMRR   := Ascan(::aMrr, {|x| x[1] == cTpMRR })

	If nPosMRR == 0
		AADD(::aMrr, {cTpMRR, {}})
		nPosMRR    := Len(::aMrr)
	EndIf

	aAux := {}
	cObs     := "Referente Corp mês " + AmtoCMP(cAMPH5) 

	AADD(aAux, {"AMFECHA" , ::cAmFecha     })
	AADD(aAux, {"AMPH5"   , cAMPH5         })
	AADD(aAux, {"VALOR"   , nVlTot         })
	AADD(aAux, {"ESTORNO" , "N"            })
	AADD(aAux, {"AMRESUMO", "" 	           })	
	AADD(aAux, {"OBSERV"  , cObs           })
	AADD(aAux, {"MOTIVO"  , cMotivo        }) 
	AADD(aAux, {"CHURN"   , cChurn         })
	AADD(aAux, {"MRR"     , cMRR           })
	AADD(aAux, {"TIPOPH3" , cTipoPH3       })
	AADD(aAux, {"PERINI"  , cIniPer        }) 
	AADD(aAux, {"PERFIM"  , cFimPer        }) 
	AADD(aAux, {"CLIENTE" , PH5->PH5_CLIENT})
	AADD(aAux, {"LOJA"    , PH5->PH5_LOJA  })  	
	AADD(aAux, {"UNINEG"  , cUniNeg        })	
	AADD(aAux, {"NOTA"    , cNota          })	
	AADD(aAux, {"SERIE"   , cSerie         })	
	AADD(aAux, {"ITEMNF"  , cItemNf        })	
	AADD(aAux, {"SEQ"     , cSeq           })
	AADD(aAux, {"CTRORI"  , ""             })
	AADD(aAux, {"CTRDES"  , ""             })		
	AADD(aAux, {"DTINC"   , Ctod("")       })			
	AADD(aAux, {"TOTPAR"  , ""             })			
	AADD(::aMRR[nPosMRR, 2], aClone(aAux)   )

Return 
