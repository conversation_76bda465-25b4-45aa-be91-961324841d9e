#INCLUDE "PROTHEUS.CH"               

/*
TIMJob  										// Classe Pricipal do motor Job 
TIMJBlock 	              				// Classe Bloco do motor job

MJAuto2Str(nOption,aCab,aItens)	// Funcao que retorna um String o conteudo a opcao, aCab e itens utilizados em rotina automatica
MJStr2HAuto(cString) 					// Funcao que retorna um array com o cabeçalho para a rotina automatica
MJStr2HAuto(cString)              // Funcao que retorna um array com os itens para a rotina automatica
MJStr2OpAuto(cString)				   // Funcao que retorna a opcao 3-inclusao;4-alteracao;5-exclusao para a rotina automatica

TIMJobM()             					// Monitor Job
TIMJobI(cCodJob)		  					//	Impresso do Job
TIMJobS(cCodEmp,cFilEmp)  				// Funcao do tipo Job para gerenciar os jobmotor, pode ser utilizado no StarJob no ini do protheus           
TIMJExe(cCodJob,lWait)    				// funcao para execuar um motor job manualmente
TIMJOpen(cArq,cAlias)					// cria e/o abre a tabela de controle do motor job
*/
     

#DEFINE JOB_PENDENTE  "1"   
#DEFINE JOB_ANDAMENTO "2"
#DEFINE JOB_OK        "3"
#DEFINE JOB_ERRO      "4"
#DEFINE JOB_BLQ       "5"


Static cErroA	:= ""
Static __DtRef
Static __nSem


/*
========================================================================
Classe ACD Motor Job
========================================================================
*/

/*
{Protheus.doc} TIMJob
Classe para gerenciamento de rotina  automatica
@owner Alex Sandro 
@version 1.0
@since	30/01/2012
@attrib cDesc 
@herenca
*/   
 
Class TIMJob
    data cDescr
    data cCodJob
    data cOrigem
    data cChave
    data aBlock
    data aSeqBlock
    data cStatus
    data cLogJob   
    data lSave
    data dDtCri
    data cHrCri
    data dDtIni
    data cHrIni
    
    data dDtTExe
    data cHrTExe
    Method New(cCodJob,cDescr) 
    Method Execute()   
    Method SaveDB()
    Method SvStatus()
    Method DeleteDB() 
    Method LoadDB()
EndClass

/*
{Protheus.doc} New
Metodo construtor
@param cCodJob
@param cDescr
<AUTHOR> Sandro 
@version 1.0
@since	30/01/2012
@return	NIL
*/   

Method New(cCodJob, cDescr, cOrigem, cChave) Class TIMJob
    ::aBlock:={}    
    ::aSeqBlock:={} 
    ::cLogJob:=""
    ::cOrigem:= ""
    ::cChave := ""

    If cOrigem == NIL 
        cOrigem := Padr(ProcName(1), 20)
    EndIf 
    If cChave == NIL
        cChave := ""
    EndIf 

    If Select("TIMJ0") == 0 
        U_TIMJOpen("TIMJ0")
    EndIf
    If Select("TIMJ1") == 0 
        U_TIMJOpen("TIMJ1")
    EndIf
    If Select("TIMJ2") == 0
        U_TIMJOpen("TIMJ2")
    EndIf

    If cCodJob==NIL
        ::cCodJob	:= NextJob()
        ::cDescr	:= cDescr
        ::cStatus	:= JOB_PENDENTE
        ::cOrigem   := cOrigem
        ::cChave    := cChave
        ::lSave		:= .F.                 	
        ::dDtCri	:= Date()
        ::cHrCri	:= Time()
        ::dDtIni    := ctod("")
        ::cHrIni    := ""
        ::dDtTExe	:= cTod("")
        ::cHrTExe	:= ""
    Else  
        ::cCodJob	:= cCodJob  
        Self:LoadDB()
    EndIf

    
Return

Method Execute() Class TIMJob
    Local nX   

    For nX:= 1 to len(::aBlock)
        If ::aBlock[nX]:cStBlock =="4"
            ::cStatus := JOB_OK
            Loop
        EndIf
        If ::aBlock[nX]:cStBlock <> "2" 
            ::cStatus := JOB_ANDAMENTO
            Self:SvStatus()
            If ! ::aBlock[nX]:Execute() 
                Exit
            EndIf 
        EndIf
    Next                

    If ::cStatus $ JOB_OK + JOB_PENDENTE
        ::dDtTExe	:= Date()
        ::cHrTExe	:= Time()
    EndIf	

    If ::lSave
        Self:SaveDB()
    EndIf
Return            

Static Function NextJob()
    Local cCodJob := "0000000000"
    Local aTIMJ0  := TIMJ0->(GetArea())

    While ! LockByName("tictrlmjob", .F., .F., LS_GetTotal(1) < 0)
        Sleep(500)
    EndDo
                                
    If ! file("tictrlmjob.seq")
        TIMJ0->(DbSetOrder(1))
        TIMJ0->(DbGoBottom())
        cCodJob:= TIMJ0->CODIGO
        If Empty(cCodJob)
            cCodJob :="0000000000"
        EndIf	
    Else
        cCodJob:= Memoread("tictrlmjob.seq")
    EndIf	
    cCodJob:= Soma1(cCodJob)
    MemoWrit("tictrlmjob.seq",cCodJob)

    UnlockByName("tictrlmjob", .F., .F., LS_GetTotal(1) < 0)

    RestArea(aTIMJ0)
Return cCodJob

Method SaveDB() Class TIMJob  
    Local nX
    ::lSave:= .T.

    Begin Transaction                                                            	
        For nX:= 1 to len(::aBlock)
            ::aBlock[nX]:SaveDB(StrZero(nX,2))
        Next
        TIMJ0->(DbSetOrder(1))
        If ! TIMJ0->(DbSeek(::cCodJob))
            TIMJ0->(RecLock("TIMJ0",.T.))
            TIMJ0->PRIORI := Subs(Dtos(Date()),3)+::cCodJob
            TIMJ0->CODIGO := ::cCodJob
            TIMJ0->DESCR  := ::cDescr
            TIMJ0->ORIGEM := ::cOrigem
            TIMJ0->(FieldPut(FieldPos("CHAVE"), ::cChave))
            TIMJ0->USERM  := __cUserId
            TIMJ0->EMPORI := cEmpAnt
            TIMJ0->FILORI := cFilAnt	
        Else
            TIMJ0->(RecLock("TIMJ0",.F.))
        EndIf
        
        TIMJ0->DTCRI := ::dDtCri
        TIMJ0->HRCRI := ::cHrCri
        TIMJ0->DTINI := ::dDtIni 
        TIMJ0->HRINI := ::cHrIni
        TIMJ0->DTFIN := ::dDtTExe
        TIMJ0->HRFIN := ::cHrTExe

        TIMJ0->STJOB := ::cStatus
        TIMJ0->(MsUnLock())
    End Transaction
                    
Return  

Method SvStatus() Class TIMJob 

    If ! ::lSave
        Return 
    EndIf 
    If Empty(::dDtIni)
        ::dDtIni := Date()
        ::cHrIni := Time()
    EndIf 
    Begin Transaction
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->STJOB := ::cStatus
        TIMJ0->DTINI := ::dDtIni 
        TIMJ0->HRINI := ::cHrIni
        TIMJ0->(MsUnLock())
    End Transaction

Return 

Method DeleteDB() Class TIMJob 
    Local nX
    TIMJ0->(DbSetOrder(1))
    If TIMJ0->(DbSeek(::cCodJob))
        For nX:= 1 to len(::aBlock)
            ::aBlock[nX]:DeleteDB(StrZero(nX,2))
        Next            
        ::aBlock:={}
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->(DbDelete())
        TIMJ0->(MsUnLock())
    EndIf
Return              

Method LoadDB() Class TIMJob
    TIMJ0->(DbSetOrder(1))
    If ! TIMJ0->(DbSeek(::cCodJob))
        Return
    EndIf
    ::lSave  := .T.
    ::cDescr := TIMJ0->DESCR
    ::cStatus:= TIMJ0->STJOB
    ::cOrigem:= TIMJ0->ORIGEM
    ::cChave := TIMJ0->(FieldGet(Fieldpos("CHAVE")))
    ::cLogJob:= ""
    ::dDtCri := TIMJ0->DTCRI
    ::cHrCri := TIMJ0->HRCRI
    ::dDtIni := TIMJ0->DTINI
    ::cHrIni := TIMJ0->HRINI
    ::dDtTExe:= TIMJ0->DTFIN
    ::cHrTExe:= TIMJ0->HRFIN
        
    ::aSeqBlock:={}
    TIMJ1->(DbSetOrder(1))
    TIMJ1->(DbSeek(::cCodJob))
    While TIMJ1->(! Eof() .and. Left(CODIGO, 10) == ::cCodJob)
        aadd(::aSeqBlock,Right(TIMJ1->CODIGO, 2)) 
        ::cLogJob += TIMJ1->LOGBLOCK +CRLF 
    TIMJ1->(DbSkip())
    End 

Return
/*
====================================================================
*/                     


Class TIMJBlock
   data oJob
   data cSeqBlock
   data cEmpExe
   data cFilExe
   data cDescr
   data aRotinas
   data cLog	
   data cStBlock	
   Method New(oJob,cEmpExe,cFilExe,cDescr)
   Method Add(cDescr,cExpression,cParam,cResult,nModulo)
   Method Execute()
   Method SaveDB()
   Method DeleteDB()
   Method LoadDB()
EndClass

Method New(cSeqBlock,oJob,cEmpExe,cFilExe,cDescr) Class TIMJBlock
    ::oJob		:= oJob
    ::cSeqBlock := cSeqBlock
    ::aRotinas	:= {}
    aadd(::oJob:aBlock,self)
    ::cLog		:=""   
    If cSeqBlock==NIL
        ::cSeqBlock := StrZero(len(::oJob:aBlock),2)	                     
        ::cEmpExe	:= cEmpExe
        ::cFilExe	:= cFilExe
        ::cDescr 	:= cDescr
        ::cStBlock	:= "1"  
        aadd(::oJob:aSeqBlock,::cSeqBlock)
    Else 
        ::cSeqBlock := cSeqBlock
        TIMJ1->(DbSetOrder(1))
        If TIMJ1->(DbSeek(::oJob:cCodJob+cSeqBlock))
            ::cEmpExe	:= TIMJ1->EMPEXE
            ::cFilExe	:= TIMJ1->FILEXE
            ::cDescr 	:= TIMJ1->DESCR 
            ::cLog		:= TIMJ1->LOGBLOCK   
            ::cStBlock	:= TIMJ1->STBLOCK
            Self:LoadDB() 
        EndIf	
    EndIf
Return NIL    

Method Add(cDescr, cExpression, cParam, cResult, nModulo) Class TIMJBlock
    Local aAux 
    Local cResult:= ""
    Default nModulo:=4                                      
    aAux := {cDescr,cExpression,cParam,cResult,nModulo}
    aadd(::aRotinas,aClone(aAux))  
Return 

Method Execute() Class TIMJBlock
    Local nX
    Local nOpcao		
    Local aCab			:={}
    Local aItens		:={}
    Local cExpression	
    Local cParam
    Local nModOld                   
    Local cRet
    Local lErro         := .F.               
    Local lBlq          := .F.
    Local bErroA	
    Local nExec         := 1

    Private __cParam := ""

    If Type("cEmpAnt") == "U"
        cEmpAnt := ""
    EndIf

    If Type("cFilAnt") == "U"
        cFilAnt := ""
    EndIf

    If !(cEmpAnt == ::cEmpExe) .OR. !(cFilAnt == ::cFilExe)

        RpcClearEnv()//ISTO RESOLVE TUDO!!
        RPCSetType(3)
        cEmpAnt:= ::cEmpExe
        cFilAnt:= ::cFilExe
        RpcSetEnv(::cEmpExe,::cFilExe, , ,'FAT',,)

    Endif

	If Select("TIMJ0") == 0 
		U_TIMJOpen("TIMJ0")
	EndIf
	If Select("TIMJ1") == 0 
		U_TIMJOpen("TIMJ1")
	EndIf
	If Select("TIMJ2") == 0
		U_TIMJOpen("TIMJ2")
	EndIf
	

	FWMonitorMsg("Motor ("+Alltrim(::cDescr)+") "+"Emp:"+::cEmpExe+"/"+::cFilExe) 
	SaveInter()

	Private lMsHelpAuto := .T.
	Private lMsErroAuto := .F.                         

	AutoGrlog("Bloco: "+::cDescr+" "+Dtoc(dDataBase)+" "+Time())
	//AutoGrlog("Empresa:"+::cEmpExe+" Filial:"+::cFilExe) 
  	AutoGrlog("Empresa:"+cEmpAnt+" Filial:"+cFilAnt) 
	
	bErroA	:= ErrorBlock( { |oErro| ChkErr( oErro ) } ) 
	
	Begin Sequence
		For nX:= 1 to len(::aRotinas)
			nModOld		:= nModulo   
			cTitulo		:= ::aRotinas[nX,1]
			cExpression	:=	::aRotinas[nX,2]
			cParam		:=	::aRotinas[nX,3]
            nModulo		:=	::aRotinas[nX,5]          
            nExec       := nX

            __cParam    :=  cParam

			AutoGrlog("")
			AutoGrlog("   Sequencia:"+Alltrim(Str(nX)))         
			AutoGrlog("")
			AutoGrlog("      Titulo:"+cTitulo)
			AutoGrlog("      Rotina:"+cExpression)
			AutoGrlog("      Modulo:"+Alltrim(Str(nModulo)))
	        

            lMsErroAuto	:= .F.  

            cRet := &cExpression
            
            If Upper(Left(cRet, 4)) == "ERRO" .or. ! Empty(cErroA)
                lErro       := .T.
                ::aRotinas[nX,4] := cRet
                Break	
            EndIf 
            If Upper(Left(cRet, 8)) == "BLOQUEIO"
                lBlq        := .T.
                ::aRotinas[nX,4] := cRet
                Break	
            EndIf 
            AutoGrLog("")
                
			nModulo := nModOld 
			AutoGrLog("   Sequencia finalizada OK em "+Time() )
			AutoGrLog("")
		Next
	End Sequence 
	If ! Empty(cErroA) 
		::aRotinas[nExec,4] := "Erro Prg"
		lErro:=.T.
		AutoGrLog("======================================")
		AutoGrLog("ERRO PRG"+CRLF+cErroA)   
		AutoGrLog("======================================")
		cErroA:=""
	EndIf	

    
	::cLog := MemoRead(NomeAutoLog()) 
	If ::oJob:cLogJob == Nil
        ::oJob:cLogJob := ""
    EndIf
    ::oJob:cLogJob += ::cLog 
	FErase(NomeAutoLog())    	
	If lErro
	   ::cStBlock := "3"
       ::oJob:cStatus := JOB_ERRO
    ElseIf lBlq
        ::cStBlock := "1"
        ::oJob:cStatus := JOB_BLQ
        lErro := .t.
	Else
	   ::cStBlock := "2"
       ::oJob:cStatus := JOB_OK
	EndIf               
	
	RestInter()
//RpcClearEnv()
Return ! lErro

Static Function ChkErr( oErroArq)
    Local ni:= 0
    If oErroArq:GenCode > 0
        cErroA := '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description )+CRLF
    EndIf  
    ni := 2
    While ( !Empty(ProcName(ni)) )
        cErroA +=	Trim(ProcName(ni)) +"(" + Alltrim(Str(ProcLine(ni))) + ") "+CRLF
        ni++
    End                
    If Intransact()
        cErroA +="Transacao aberta desarmada"
        DisarmTransaction()
    EndIf
    Break
Return 

Method SaveDB() Class TIMJBlock   
    Local cCodJob   := ::oJob:cCodJob
    Local cSeqBlock := ::cSeqBlock
    Local nX

	If Select("TIMJ0") == 0 
		U_TIMJOpen("TIMJ0")
	EndIf
	If Select("TIMJ1") == 0 
		U_TIMJOpen("TIMJ1")
	EndIf
	If Select("TIMJ2") == 0
		U_TIMJOpen("TIMJ2")
	EndIf
    
    TIMJ1->(DbSetOrder(1))
    If ! TIMJ1->(DbSeek(cCodJob + cSeqBlock))
        TIMJ1->(RecLock("TIMJ1",.T.))
        TIMJ1->STBLOCK := "0"
        TIMJ1->CODIGO  := cCodJob + cSeqBlock
        TIMJ1->DESCR   := ::cDescr
        TIMJ1->EMPEXE  := ::cEmpExe
        TIMJ1->FILEXE  := ::cFilExe	
    Else
        TIMJ1->(RecLock("TIMJ1",.F.))
    EndIf                     	
    TIMJ1->STBLOCK	:=	::cStBlock
    TIMJ1->LOGBLOCK :=	 Alltrim(TIMJ1->LOGBLOCK) + CRLF + ::cLog
    TIMJ1->(MsUnLock())

    For nX:= 1 to len(::aRotinas)
        TIMJ2->(DbSetOrder(1))
        If ! TIMJ2->(DbSeek(cCodJob + cSeqBlock + StrZero(nX, 2)))
            TIMJ2->(RecLock("TIMJ2",.T.))
            TIMJ2->CODIGO := cCodJob + cSeqBlock + StrZero(nX, 2)
            TIMJ2->DESCR  := ::aRotinas[nX,1]
            TIMJ2->ROTINA := ::aRotinas[nX,2]	
            TIMJ2->CPAR   := ::aRotinas[nX,3]
            TIMJ2->MODULO := ::aRotinas[nX,5]
        Else
            TIMJ2->(RecLock("TIMJ2",.F.))
        EndIf
        TIMJ2->RESULT := ::aRotinas[nX,4]
        TIMJ2->(MsUnLock()) 	  
    Next
Return

Method DeleteDB() Class TIMJBlock   
    Local cCodJob:= ::oJob:cCodJob                           	
    Local cSeqBlock := ::cSeqBlock
    TIMJ1->(DbSetOrder(1))
    If TIMJ1->(DbSeek(cCodJob + cSeqBlock))
        While TIMJ2->(DbSeek(cCodJob + cSeqBlock))
            TIMJ2->(RecLock("TIMJ2", .F.))
            TIMJ2->(DbDelete()) 
            TIMJ2->(MsUnLock()) 
        End
        TIMJ1->(RecLock("TIMJ1", .F.))
        TIMJ1->(DbDelete())
        TIMJ1->(MsUnLock()) 
        TIMJ2->(DbSetOrder(1))    
    EndIf                       
    ::aRotinas:={}
Return

Method LoadDB() Class TIMJBlock
    Local cCodJob		:= ::oJob:cCodJob
    Local cSeqBlock 	:= ::cSeqBlock               
    Local cDescr
    Local cExpression
    Local cParam                                 
    Local cResult
    Local nModulo
    TIMJ2->(DbSetOrder(1))
    TIMJ2->(DbSeek(cCodJob + cSeqBlock))
    While TIMJ2->(! Eof() .and. Left(CODIGO, 12) == cCodJob + cSeqBlock)
        cDescr		:= Alltrim(TIMJ2->DESCR)
        cExpression	:= Alltrim(TIMJ2->ROTINA)
        cParam		:= Alltrim(TIMJ2->CPAR)  
        cResult		:= Alltrim(TIMJ2->RESULT)
        nModulo		:= TIMJ2->MODULO
        Self:Add(cDescr, cExpression, cParam, cResult, nModulo)
        TIMJ2->(DbSkip()) 
    End
Return

/*
 funcao de apoio
*/               
// Funcao que retorna um String o conteudo a opcao, aCab e itens utilizados em rotina automatica
User Function MJAuto2Str(nOption, aCab, aItens) 
    Local cParam    := ""
    Local nX           
    Local nY
    Local aItem     := {}
    Default nOption := 3 
    Default aCab    := {}
    Default aItens  := {}

    cParam += "[NOPTION]" + CRLF
    cParam += Alltrim(Str(nOption)) + CRLF
    cParam += "[HEADER]" + CRLF

    For nX := 1 to Len(aCab)     
        If Valtype(aCab[nX,2]) == "C" 
            If Left(aCab[nX,2],3) == '{||'  
                cParam += Alltrim(aCab[nX,1]) + ':=' + aCab[nX, 2] + CRLF 
            Else
                cParam += Alltrim(aCab[nX,1]) + ':="' + aCab[nX, 2] + '"' + CRLF 
            EndIf
        ElseIf Valtype(aCab[nX,2]) == "N"
            cParam += Alltrim(aCab[nX,1]) + ':=' + Alltrim(Str(aCab[nX, 2])) + CRLF 
        ElseIf Valtype(aCab[nX,2]) == "D"
            cParam += Alltrim(aCab[nX,1]) + ':=CTOD("' + dtoc(aCab[nX,2]) + '")' + CRLF  
        EndIf 	
    Next 
    For nY := 1 to Len(aItens)
        aItem  := aClone(aItens[nY])
        cParam += "[ITEMS]" + CRLF
        For nX := 1 to Len(aItem)     
            If Valtype(aItem[nX,2]) == "C" 
                If Left(aItem[nX,2],3) == '{||'  
                    cParam += Alltrim(aItem[nX,1]) + ':=' + aItem[nX,2] + CRLF
                Else
                    cParam += Alltrim(aItem[nX,1]) + ':="' + aItem[nX,2] + '"' + CRLF 
                EndIf
            ElseIf Valtype(aItem[nX,2]) == "N"
                cParam += Alltrim(aItem[nX,1]) + ':=' + Alltrim(Str(aItem[nX,2])) + CRLF 
            ElseIf Valtype(aItem[nX,2])=="D"
                cParam += Alltrim(aItem[nX,1]) + ':=CTOD("' + dtoc(aItem[nX,2]) + '")' + CRLF 
            EndIf 	
        Next 
    Next 
Return cParam

User Function MJStr2HAuto(cString) 					// Funcao que retorna um array com o cabeçalho para a rotina automatica
    Local nX
    Local NY
    Local aArray :={}
    Local cLinha :=""
    Local cCampo
    Local uConteudo                   

    For nY := 1 to MLCOUNT(cString)
        cLinha := MemoLine(cString, , nY)
        If Alltrim(cLinha) == "[HEADER]"
            Exit
        EndIf
    Next
    nY++
    For nX := nY to MLCOUNT(cString)
        cLinha := MemoLine(cString,, nX)
        If Alltrim(cLinha) == "[HEADER]"
            Loop                    
        EndIf
        If Alltrim(cLinha) == "[ITEMS]"
            Exit
        EndIf
        cCampo := left(cLinha, at(":=", cLinha) - 1)
        uConteudo := Alltrim(Subs(cLinha, at(":=", cLinha) + 2))
        If  Left(uConteudo, 1) # "'" .and.  Left(uConteudo, 1) # '"'
            uConteudo := &uConteudo 
            If ValType(uConteudo) == "B"  	
                uConteudo := Eval(uConteudo)
            EndIf
        Else
            uConteudo := StrTran(uConteudo, "'", "")
            uConteudo := StrTran(uConteudo, '"', '')
        EndIf
        If !Empty(cCampo) //.and. !Empty(uConteudo)
            aadd(aArray, {cCampo, uConteudo, NIL})
        EndIf
    Next
Return aClone(aArray)

User Function MJStr2IAuto(cString)                // Funcao que retorna um array com os itens para a rotina automatica
    Local nX,NY
    Local aArray :={}
    Local aTemp  := {}
    Local cLinha :=""
    Local cCampo
    Local uConteudo

    For nY := 1 to MLCOUNT(cString)
        cLinha := MemoLine(cString,, nY)
        If Alltrim(cLinha) == "[ITEMS]"
            Exit
        EndIf
    Next
    nY++
    For nX := nY to MLCOUNT(cString)
        cLinha := MemoLine(cString,, nX)
        If Alltrim(cLinha) == "[ITEMS]"
            aadd(aArray, aClone(aTemp))
            aTemp := {}
            Loop
        EndIf
        cCampo    := left(cLinha, at(":=", cLinha) - 1)
        uConteudo := Alltrim(Subs(cLinha, at(":=", cLinha) + 2))
        If  Left(uConteudo, 1) # "'" .and.  Left(uConteudo, 1) # '"'
            uConteudo := &uConteudo     
            If  ValType(uConteudo) == "B"   	
                uConteudo := Eval(uConteudo)
            EndIf		
        Else
            uConteudo := StrTran(uConteudo,"'","")
            uConteudo := StrTran(uConteudo,'"','')
        EndIf
        If ! empty(cCampo)
            aadd(aTemp, {cCampo, uConteudo, NIL})
        EndIf
    Next                    
    If ! Empty(aTemp)
        aadd(aArray, aClone(aTemp)) 
    EndIf
Return aClone(aArray)

User Function MJStr2OpAuto(cString)				   // Funcao que retorna a opcao 3-inclusao;4-alteracao;5-exclusao para a rotina automatica
    Local nX
    Local cLinha :=""
    Local nOpcao := 3  

    For nX := 1 to MLCOUNT(cString)
        cLinha :=MemoLine(cString, ,nX)
        If Alltrim(cLinha) == "[NOPTION]" 
            nOpcao := Val(MemoLine(cString, , nX + 1))
            Exit
        EndIf
    Next                    
Return nOpcao

/*
Monitor Job
*/

User Function TIMJobM()
    Local aArea    := GetArea()
    Local oDlg            
    Local oFolder       
    Local aoBrw		:={}

    Local oTimer 
    Local aCoors	:= FWGetDialogSize( oMainWnd )
    
    Local lServico := !GetMv("TI_INCRTON", , .F.)  

    __DtRef := dDataBase             


    Private lJobOnLine 	:= .F.      
    Private cStatusJob	:= ""	
    Private oStatusJob  

    If Select("TIMJ0") == 0 
        U_TIMJOpen("TIMJ0")
    EndIf
    If Select("TIMJ1") == 0 
        U_TIMJOpen("TIMJ1")
    EndIf
    If Select("TIMJ2") == 0
        U_TIMJOpen("TIMJ2")
    EndIf

    // um alias para cada aba
    U_TIMJOpen("TIMJ0","TIMJ01")  
    U_TIMJOpen("TIMJ0","TIMJ02")
    U_TIMJOpen("TIMJ0","TIMJ03")
    U_TIMJOpen("TIMJ0","TIMJ04")
    U_TIMJOpen("TIMJ0","TIMJ05")

    FErase("timjobs.on")
    FErase("timjobs.off")

            
    DEFINE MSDIALOG oDlg FROM aCoors[1], aCoors[2] TO aCoors[3] * 0.9, aCoors[4]  * 0.9 TITLE "Monitor Motor Job" OF oMainWnd COLOR "W+/W" PIXEL //STYLE nOR(WS_VISIBLE,WS_POPUP)  

        oFolder := TFolder():New(1,0,,,oDlg,,,,.F.,.F.,340,140,) 

        oFolder :AddItem("Fila")
        oFolder :SetOption(1)                     
        aadd(aoBrw,Monitor(oFolder, oFolder:aDialogs[len(ofolder:aDialogs)], oDlg, lServico))

        oFolder:AddItem("Execução")                     
        oFolder :SetOption(2)
        aadd(aoBrw,Monitor(oFolder, oFolder:aDialogs[len(ofolder:aDialogs)], oDlg, lServico) )

        oFolder :AddItem("Finalizado")
        oFolder :SetOption(3)                     
        aadd(aoBrw,Monitor(oFolder, oFolder:aDialogs[len(ofolder:aDialogs)], oDlg, lServico))

        oFolder:AddItem("Divergencia")                     
        oFolder :SetOption(4)
        aadd(aoBrw,Monitor(oFolder, oFolder:aDialogs[len(ofolder:aDialogs)], oDlg, lServico) )

        oFolder:AddItem("Bloqueado")                     
        oFolder :SetOption(5)
        aadd(aoBrw,Monitor(oFolder, oFolder:aDialogs[len(ofolder:aDialogs)], oDlg, lServico) )



        DEFINE TIMER oTimer INTERVAL 1000 ACTION AtuTela(oFolder, aoBrw, oTimer, oDlg) OF oDlg
        oFolder :Align := CONTROL_ALIGN_ALLCLIENT
        oFolder :SetOption(1)
        oFolder :bSetOption := {|nFolder| MudaFolder(aoBrw,nFolder)}

    ACTIVATE MSDIALOG oDlg ON INIT (AtuTela(oFolder, aoBrw, oTimer, oDlg), oTimer:Activate()) CENTERED

    RestArea(aArea)                                           
Return

Static Function MudaFolder(aoBrw,nFolder)
    FErase("timjobs.on")
    lJobOnLine := File("timjobs.on")     
    If oStatusjob <> NIL 
        If lJobOnLine
            cStatusJob := "On Line "
        Else 
            cStatusJob := "Off Line "
        EndIf               
        oStatusjob:Refresh()
    EndIf	
    If nFolder==1
        TIMJ01->(DbSeek('1', .t.))
        aoBrw[1]:Refresh()   
        Eval(aoBrw[1]:bChange)
    ElseIf  nFolder==2
        TIMJ02->(DbSeek('2' + Subs(Dtos(__DtRef), 3), .t.))
        aoBrw[2]:Refresh()   
        Eval(aoBrw[2]:bChange)
    ElseIf  nFolder==3
        TIMJ03->(DbSeek('3', .t.))
        aoBrw[3]:Refresh()   
        Eval(aoBrw[3]:bChange)
    ElseIf  nFolder==4
        TIMJ04->(DbSeek('4', .t.))
        aoBrw[4]:Refresh()   
        Eval(aoBrw[4]:bChange)
    ElseIf  nFolder==5
        TIMJ05->(DbSeek('5', .t.))
        aoBrw[5]:Refresh()   
        Eval(aoBrw[5]:bChange)
    EndIf
Return                          
                    

Static Function AtuTela(oFolder, aoBrw, oTimer, oDlg)
    Local nRecno	:= 0      
  
    FErase("timjobs.on")
    lJobOnLine := File("timjobs.on")     

    If oTimer == NIL
        Return
    EndIf 
  
    oTimer:Deactivate()             
    If oFolder:nOption == 1
        nRecno	:= TIMJ01->(Recno())
        TIMJ01->(DbSeek('1'))
        TIMJ01->(DbGoto(nRecno)) 
    ElseIf  oFolder:nOption == 2
        nRecno	:= TIMJ02->(Recno())
        TIMJ02->(DbSeek('2'))
        TIMJ02->(DbGoto(nRecno)) 
    ElseIf  oFolder:nOption == 3
        nRecno	:= TIMJ03->(Recno())
        TIMJ03->(DbSeek('3'))
        TIMJ03->(DbGoto(nRecno)) 
    ElseIf  oFolder:nOption == 4
        nRecno	:= TIMJ04->(Recno())
        TIMJ04->(DbSeek('4'))
        TIMJ04->(DbGoto(nRecno)) 
    ElseIf  oFolder:nOption == 5
        nRecno	:= TIMJ05->(Recno())
        TIMJ05->(DbSeek('5'))
        TIMJ05->(DbGoto(nRecno)) 
    EndIf
    
    aoBrw[oFolder:nOption]:Refresh()   
    Eval(aoBrw[oFolder:nOption]:bChange)
    oTimer:Activate()

 Return


Static Function Legenda()     
    Local aLegenda	:={	{"LBTICK"	, "Processado"			},; 
                        {"LBNO"		, "Não Processado"		},; 
                        {"PMSMENOS"	, "Ignorado no processamento"	},; 
                        {"lightblu"	, "Processado com alteração pelo Monitor"			},;
                        {"xclose"	, "Erro no processamento"}} 

    BrwLegenda("Legenda bloco", "Motor Job", aLegenda)

Return

Static Function MoveUP(oFolder, oBrw)
    Local aArea 	:= GetArea()
    Local aAreaX
    Local cPriori1	  
    Local cPriori2 
    Local nAtuRec 
        
    If oFolder:nOption ==1
        aAreaX	:= TIMJ01->(GetArea())
        cCampo	:= "PRIORI" 
        cPriori1	:= TIMJ01->(FieldGet(FieldPos(cCampo)))  
        nAtuRec 	:= TIMJ01->(Recno())
        TIMJ01->(DbSkip(-1))
        If TIMJ01->(Bof()) .or. TIMJ01->STJOB <> "1"  
            TIMJ01->(DbGoto(nAtuRec))
        Else      
        cPriori2	:= TIMJ01->(FieldGet(FieldPos(cCampo))) 
            TIMJ01->(Reclock('TIMJ01',.f.))
            TIMJ01->(FieldPut(FieldPos(cCampo),cPriori1))
            TIMJ01->(MsUnlock())                

            TIMJ01->(DbGoto(nAtuRec)) 
            TIMJ01->(Reclock('TIMJ01',.f.))
            TIMJ01->(FieldPut(FieldPos(cCampo),cPriori2))		
            TIMJ01->(MsUnlock())
        EndIf
        oBrw:Refresh()

    EndIf
    RestArea(aAreaX)
    RestArea(aArea)
Return


Static Function MoveDown(oFolder,oBrw)
    Local aArea 	:= GetArea()
    Local aAreaX
    Local cPriori1	  
    Local cPriori2 
    Local nAtuRec 	               

    If oFolder:nOption ==1 // Tela a executar
        aAreaX	 := TIMJ01->(GetArea()) 
        cCampo	 := "PRIORI"  
        cPriori1 := TIMJ01->(FieldGet(FieldPos(cCampo)))  
        nAtuRec  := TIMJ01->(Recno())
        TIMJ01->(DbSkip())
        If TIMJ01->(Eof()) .OR. TIMJ01->STJOB <> "1" 
            TIMJ01->(DbGoto(nAtuRec))
        Else                
            cPriori2	:= TIMJ01->(FieldGet(FieldPos(cCampo))) 
            TIMJ01->(Reclock('TIMJ01',.f.))
            TIMJ01->(FieldPut(FieldPos(cCampo), cPriori1))
            TIMJ01->(MsUnlock())

            TIMJ01->(DbGoto(nAtuRec)) 
            TIMJ01->(Reclock('TIMJ01',.f.))
            TIMJ01->(FieldPut(FieldPos(cCampo), cPriori2))
            TIMJ01->(MsUnlock())
        EndIf 
        oBrw:Refresh()	                          	
    EndIf

    RestArea(aAreaX)
RestArea(aArea)
Return
                                                 

Static Function Monitor(oFolder, oDialog, oDlg, lServico)
    Local aBrowse	:= {"CODIGO","DESCR","USERM","EMPORI","FILORI","ORIGEM","CHAVE","DTCRI","HRCRI","DTINI","HRINI","DTFIN","HRFIN"}
    Local aCab1		:= {"Codigo","Descricao","Usuario","Emp. Ori.","Filial Ori","Origem", "Chave","Data criação","Hora criação","Inicio execução","Hora Execução","Data termino ","Hora termino"}  
    Local aCab2		:= {" ","Bloco","Empresa","Filial","Alterado por:" }  
    Local aCab3		:= {"Descricao","Rotina","Resultado"}  
    Local aCoors	
    Local aItens2  := {}
    Local oLbx

    Local aItens3  := {}
    Local oLbx2

    Local oCol
    Local oBrowse
    Local i       
    Local cStatus := Str(oFolder:nOption, 1)

    Local cCpoFil := "TIMJ0"+cStatus+"->STJOB"
    Local cTopFun := cStatus
    Local cBotFun := cStatus
    Local oP           
    Local oPanel1  
    Local oPanel2 
    Local oPanel3     
    Local oPStatus   
    Local oPTitulo  
    Local oCodJob
    Local cCodJob  :=Space(10)
    Local oDescJob
    Local cDescjob :=Space(100)

    Local nX                            
    Local nAux						
                            
    Local aBtnLat:={}  
    Local oStFont
    Default lServico := .T.
                        
    If cStatus == JOB_PENDENTE   
        aAdd(aBtnLat,	{"pmssetaup"  		,{|| MoveUP(oFolder  ,oBrowse)}	    ,"Move Job para cima" 				,{|| .T. }})
        aAdd(aBtnLat,	{"pmssetadown"		,{|| MoveDown(oFolder,oBrowse)}		,"Move Job para baixo"				,{|| .T. }})
        If lServico
           aAdd(aBtnLat, {"sducreateidx" ,{|| AtivaServico()   }	    ,"Ativar Serviço"     ,{|| ! lJobOnLine }})
           aAdd(aBtnLat, {"sdudrptbl"    ,{|| DesativaServico()}	    ,"Desativar Serviço"  ,{|| lJobOnLine   }})
        EndIf 
        aAdd(aBtnLat,	{"autom"			,{|| Executa(oBrowse) }			,"Executar Job Manualmente"		,{|| ! lJobOnLine }})
        aAdd(aBtnLat,	{"sduappend"		,{|| Divergencia(oBrowse)}		,"Transferir Job para Divergente",{|| .T. }})
    Elseif cStatus ==JOB_ANDAMENTO
        aAdd(aBtnLat,	{"devolnf"  		,{|| Fila(oBrowse, "2")}		,"Transferir Job para Fila"		    ,{|| .T. }})
    Elseif cStatus ==JOB_OK
        aAdd(aBtnLat,	{"btcalend"  		,{|| MudaData(oBrowse)}			,"Busca por Data do Job"  			,{|| .T. }})
        aAdd(aBtnLat,	{"pesquisa"  		,{|| PesqJob(oBrowse)}			,"Busca por Código Job"				,{|| .T. }})
        aAdd(aBtnLat,	{"impressao" 		,{|| ImprimeJob("3")}			,"Imprimir Job"						,{|| .T. }})
    Elseif cStatus ==JOB_ERRO
        aAdd(aBtnLat,	{"devolnf"  		,{|| Fila(oBrowse, "4")}		,"Transferir Job para Fila"		,{|| .T. }})
        aAdd(aBtnLat,	{"SduRepl"  		,{|| IgnoraBloco(oBrowse)}		,"Ignorar Bloco"						,{|| .T. }})
        aAdd(aBtnLat,	{"excluir"  		,{|| Finaliza(oBrowse) }		,"Finalizar Job sem processar"	,{|| .T. }})
        aAdd(aBtnLat,	{"impressao" 		,{|| ImprimeJob("4")}			,"Imprimir Job"						,{|| .T. }})
    EndIf						
    aAdd(aBtnLat,		{"svm"    			,{|| Legenda()}			,"Legenda Bloco"						,{|| .T. }})            
    aAdd(aBtnLat,		{"final_mdi"		,{|| oDlg:End()}				,"Sair"									,{|| .T. }})   


   
    oP:= TPanelCss():New(,,, oDialog)
    oP:SetCoors(TRect():New(0, 0, 40, 60))
    oP:Align :=CONTROL_ALIGN_ALLCLIENT

    @00,00 MSPANEL oPanel1 PROMPT "" SIZE 200, 25 of oP
    oPanel1:Align :=CONTROL_ALIGN_TOP

        nAux:= 2
        For nX := 1 To Len(aBtnLat)
            TBtnBmp2():New(2,nAux, 40, 40 ,aBtnLat[nX,1], NIL, NIL,NIL,aBtnLat[nX,2], oPanel1, aBtnLat[nX,3], aBtnLat[nX,4], NIL,NIL ) 
            nAux += 42
        Next

        @00,00 MSPANEL oPStatus PROMPT "" SIZE 50, 20 of oPanel1
        oPStatus:Align :=CONTROL_ALIGN_RIGHT

                    
        oStFont := TFont():New('Consolas',, 16,, .T.,,,,, .F., .F.)
        @ 008, 002 SAY oStatusJob VAR cStatusJob SIZE 60,010 OF oPStatus PIXEL FONT oStFont
        
        Ferase("timjobs.on")
        lJobOnLine := File("timjobs.on")     
        If oStatusjob <> NIL 
            If lJobOnLine
                cStatusJob := "On Line "
            Else 
                cStatusJob := "Off Line "
            EndIf              
            oStatusjob:Refresh()
        EndIf	   	

        
        @00,00 MSPANEL oPTitulo PROMPT "" SIZE 200, 20 of oPanel1
        oPTitulo:Align := CONTROL_ALIGN_RIGHT

        @ 008, 002 SAY oCodJob  VAR cCodJob SIZE 35,010 OF oPTitulo PIXEL FONT oStFont
        @ 008, 050 SAY oDescJob VAR cDescJob SIZE 130,010 OF oPTitulo PIXEL FONT oStFont

                
    @00,00 MSPANEL oPanel2 PROMPT "" SIZE 20,20 of oP
    oPanel2:Align := CONTROL_ALIGN_ALLCLIENT

    oBrowse := VCBrowse():New( 0, 0, 266,175,,,,oPanel2 ,cCpoFil,cTopFun,cBotFun,/*bLDblClick*/,,,,,,,, .F.,"TIMJ0"+cStatus, .T.,, .F., ,)
    oBrowse := oBrowse:GetBrowse()  
    oBrowse:bChange := {|| oBrowse :Refresh(),MtaTIMJ1(oLbx,aItens2,("TIMJ0"+cStatus)->CODIGO,cStatus,oCodJob,oDescJob),Eval(oLbx:bChange)   }
    oBrowse:Align := CONTROL_ALIGN_ALLCLIENT

            
    For i:=1 To Len(aBrowse)                                                       
        oCol := TCColumn():New( Alltrim(aCab1[i]), &("{ || TIMJ0"+cStatus+"->"+aBrowse[i]+"}"),,,, "LEFT",, .F., .F.,,,, .F., )
        oBrowse:AddColumn(oCol)
    Next i

    CalcTemp(.t.)
    
    oCol := TCColumn():New( "Duração", &("{ || TIMJ0"+cStatus+"->(CalcTemp()) }"),,,, "LEFT",, .F., .F.,,,, .F., )
    oBrowse:AddColumn(oCol)   

    ("TIMJ0"+cStatus)->(DbSetOrder(2))
    ("TIMJ0"+cStatus)->(DbSeek(cStatus))
    oBrowse :Refresh()                                       


                                    
    @00,00 MSPANEL oPanel3 PROMPT "" SIZE 140,140 of oPanel2
    oPanel3:Align := CONTROL_ALIGN_BOTTOM  
        aCoors	:= FWGetDialogSize( oPanel3 )
    
        oLbx := TWBrowse():New(aCoors[1],aCoors[2],aCoors[4] * 0.9 / 4, aCoors[3],,aCab2,,oPanel3,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        MtaTIMJ1(oLbx,aItens2,("TIMJ0"+cStatus)->CODIGO,cStatus,oCodJob,oDescJob)
        oLbx:Align := CONTROL_ALIGN_LEFT
        oLbx:blDBlClick := {|| MostraLog(aItens2[oLbx:nAt,len(aItens2[oLbx:nAt])])}
        oLbx:bChange := {|| oBrowse :Refresh(),MtaTIMJ2(oLbx2,aItens3,aItens2[oLbx:nAt,len(aItens2[oLbx:nAt])],cStatus) }


        oLbx2 := TWBrowse():New(aCoors[1],aCoors[2],aCoors[4] * 0.9 / 4, aCoors[3],,aCab3,,oPanel3,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        MtaTIMJ2(oLbx2,aItens3,aItens2[oLbx:nAt,len(aItens2[oLbx:nAt])],cStatus)
        oLbx2:Align := CONTROL_ALIGN_ALLCLIENT
        oLbx2:blDBlClick := {|| MostraParam(aItens3[oLbx2:nAt,len(aItens3[oLbx2:nAt])],oFolder)}

        

    @ 000,000 BUTTON oSplit PROMPT "*" SIZE 5,5 OF oPanel2 PIXEL 
    oSplit:nClrText := 0   
    oSplit:cToolTip := "Habilita e desabilita os itens."
    oSplit:bLClicked := {|| oPanel3:lVisibleControl 	:= !oPanel3:lVisibleControl}
    oSplit:Align := CONTROL_ALIGN_BOTTOM       

Return oBrowse  

Static Function CalcTemp(lInit)
    Local cTempo := ""
    Local cAlias  := Alias()

    If lInit <> NIL
        Return
    EndIf 

    If ! Empty((cAlias)->HRFIN)
        cTempo   := ElapTime((cAlias)->HRINI, (cAlias)->HRFIN)
    EndIf 

Return cTempo 

Static Function PesqJob(oBrowse) 
    Local cCodJob:= Space(10)
    Local aRet := {}
    Local aParamBox :={{1,"Codigo job"				,cCodJob	,"","",""	,".T.",0,.F.}}

    If ! ParamBox(aParamBox,"Parametros",@aRet,,,,,,,,.f.) 
        Return .f.
    EndIf    
    cCodJob:=MV_PAR01
    TIMJ0->(DbSetOrder(1))
    If ! TIMJ0->(DbSeek(cCodJob))        
        MsgAlert('Job '+cCodJob+'não encontrado!')
        Return .f.
    EndIf
    If TIMJ0->STJOB =='1'
        MsgAlert('Job '+cCodJob+' localizado na pasta FILA para ser processado.')
        Return .f.
    EndIf
    If TIMJ0->STJOB =='3'
        MsgAlert('Job '+cCodJob+' localizado na pasta DIVERGENCIA.')
        Return .f.
    EndIf	
    TIMJ03->(DbGoto(TIMJ0->(Recno())))
    eval(oBrowse:bChange)

Return .t.

Static Function MudaData(oBrowse)
    Local dData:= __DtRef
    Local aRet := {}
    Local aParamBox :={{1,"Data"				,dData	,"","",""	,".T.",0,.F.}}

    If ParamBox(aParamBox,"Parametros",@aRet,,,,,,,,.f.)  
        __DtRef := MV_PAR01    
        TIMJ03->(DbSeek('3'+Subs(Dtos(__DtRef),3),.t.))
        oBrowse:Refresh()   
        Eval(oBrowse:bChange)	
    EndIf	
Return                           

Static Function AtivaServico()           

    StartJob("U_TIMJobS",GetEnvServer(),.F.,cEmpAnt,cFilAnt)
    While ! File("timjobs.on")
        Sleep(100) 
    End
    lJobOnLine := .T.     
    If lJobOnLine
        cStatusJob := "On Line "
    Else 
        cStatusJob := "Off Line "
    EndIf              
    oStatusjob:Refresh()

Return                                       

Static Function DesativaServico() 
    MemoWrit("timjobs.off","F") 
    While File("timjobs.on")
        Sleep(100) 
    End
    lJobOnLine := .F.     
    If lJobOnLine
        cStatusJob := "On Line "
    Else 
        cStatusJob := "Off Line "
    EndIf             
    oStatusjob:Refresh()
Return                                   

Static Function Divergencia(oBrowse, nFolder)  
    Local nRecNew:=0
    If ! MsgYesNo("Confirma a transferencia do Job ["+TIMJ01->CODIGO+"-"+Alltrim(TIMJ01->DESCR) + "] para pasta de divergencia?")
        Return 
    EndIf     
    TIMJ0->(DbGoto(TIMJ01->(Recno())))
    Begin Transaction   
        nRecNew := TIMJ0->(CBCopyRec())
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->(DbDelete())
        TIMJ0->(MsUnlock())    
        TIMJ0->(DbGoto(nRecNew))	       
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->STJOB	:= JOB_ERRO
        TIMJ0->DTFIN	:= Date()
        TIMJ0->HRFIN	:= Time()
        TIMJ0->(MsUnlock())    
    End Transaction              
    
    TIMJ01->(DbSeek("1"))  
    TIMJ03->(DbSeek("3"))  
    oBrowse:Refresh()          
Return          

Static Function Fila(oBrowse, cOrigem)
    Local nRecNew := 0
    Local nRecAtu := 0

    If cOrigem == "2"
        If ! MsgYesNo("Confirma a transferencia do Job ["+TIMJ02->CODIGO+"-"+Alltrim(TIMJ02->DESCR) + "] para ser reprocessado?")
            Return 
        EndIf     
        nRecAtu := TIMJ02->(Recno())
    Else    
        If ! MsgYesNo("Confirma a transferencia do Job ["+TIMJ04->CODIGO+"-"+Alltrim(TIMJ04->DESCR) + "] para ser reprocessado?")
            Return 
        EndIf     
        nRecAtu := TIMJ04->(Recno())
    EndIf

    TIMJ0->(DbGoto(nRecAtu))
    Begin Transaction 
        nRecNew := TIMJ0->(CBCopyRec())
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->(DbDelete())
        TIMJ0->(MsUnlock())    
        TIMJ0->(DbGoto(nRecNew))	       
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->STJOB	:= JOB_PENDENTE 
        TIMJ0->DTFIN		:= Date()
        TIMJ0->HRFIN		:= Time()
        TIMJ0->(MsUnlock())    
    End Transaction                

    TIMJ02->(DbSeek("2")) 
    TIMJ04->(DbSeek("4")) 
    TIMJ01->(DbSeek("1")) 
    oBrowse:Refresh()          
Return              

Static Function IgnoraBloco(oBrowse)
    Local aCab			:= {"DESCR","EMPEXE","FILEXE"}
    Local aBlocks 		:= {} 
    Local lMark			:= .f.  
    Local nX

    TIMJ0->(DbGoto(TIMJ03->(Recno())))
    TIMJ1->(DbSetOrder(1))
    TIMJ1->(DbSeek(TIMJ0->CODIGO))
    While TIMJ1->(! Eof() .and. Left(CODIGO,6) == TIMJ0->CODIGO) 
        TIMJ1->(aadd(aBlocks,{lMark,DESCR,EMPEXE,FILEXE,Recno()}))	
    TIMJ1->(DbSkip())                                                                          
    End

    SelDoc(aCab,aBlocks,"Job ["+TIMJ03->CODIGO+"-"+Alltrim(TIMJ03->DESCR) + "]- Selecione os itens a serem ignorados:")
    If Empty(aBlocks)
        Return
    EndIf   
    For nX:= 1 to len(aBlocks)  
        TIMJ1->(DbGoto(aBlocks[nX,len(aBlocks[nX])]))
        TIMJ1->(RecLock("TIMJ1",.F.))  
        If TIMJ1->STBLOCK=='2'
            Loop
        EndIf
        If aBlocks[nX,1] 
            TIMJ1->STBLOCK:= '4'
        Else 
            If TIMJ1->STBLOCK == '4'
                TIMJ1->STBLOCK:= '1'
            EndIf
        EndIf
        TIMJ1->(MsUnlock()) 
    Next
    
    oBrowse:Refresh()
Return

Static Function SelDoc(aCabDoc,aDocs,cTitulo)
    Local oPanel
    Local oPanel1
    Local oChk
    Local lChk 		:= .f.
    Local oDlg                 
    Local oLbx
    Local aCab		:={}
    Local nX       
    Local aButtons	:= {}                                    
    Local lOk		:= .F.     

    aadd(aCab," ")     
    For nX := 1 to len(aCabDoc)
        aadd(aCab,Alltrim(RetTitle(aCabDoc[nX])))
    Next                                                                 

    DEFINE MSDIALOG oDlg TITLE cTitulo FROM 0,0 TO 470,600 PIXEL OF oMainWnd
    
        EnchoiceBar( oDlg, {||  lOk:= .t., oDlg:End()} , {|| oDlg:End() },,aButtons )   
        oPanel :=TPanel():New( 010, 010, ,oDlg, , , , , , 14, 14, .F.,.T. )
        oPanel :align := CONTROL_ALIGN_TOP
        
        oPanel1 :=TPanel():New( 010, 010, ,oDlg, , , , , , 70, 70, .F.,.T. )
        oPanel1 :align := CONTROL_ALIGN_ALLCLIENT

        @ 002 ,002 CHECKBOX oChk VAR lChk PROMPT "Inverte seleção" 	SIZE 70,7 	PIXEL OF oPanel ON CLICK( aEval( aDocs, {|x| x[1] := !x[1] } ), oLbx:Refresh() )

        oLbx:= TwBrowse():New(01,01,490,490,,aCab,, oPanel1,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        oLbx:bLDblClick 	:= { || aDocs[oLbx:nAt,1]:= ! aDocs[oLbx:nAt,1]}
        oLbx:align := CONTROL_ALIGN_ALLCLIENT     
        oLbx:SetArray( aDocs )
        oLbx:bLine := {|| Retbline(oLbx,aDocs) }	
        oLbx:Refresh()	

    ACTIVATE MSDIALOG oDlg CENTERED    
    If ! lOk
        aDocs:={}
    EndIf

Return          
                            
Static Function RetbLine(oLbx,aDocs) 
    Local nx           
    Local oOk	:= LoadBitmap( GetResources(), "LBOK" )
    Local oNo	:= LoadBitmap( GetResources(), "LBNO" )
    Local aRet	:= {}
    For nX := 1 to len(aDocs[oLbx:nAt])
        If nX==1                       
            If aDocs[oLbx:nAt,1]         
                aadd(aRet,oOk)
            Else
                aadd(aRet,oNo)		
            EndIf
        Else 
            aadd(aRet,aDocs[oLbx:nAt,nX])
        EndIf
    Next
Return aclone(aRet)




Static Function Finaliza(oBrowse)          
    Local nRecNew:=0

    If ! MsgYesNo("Confirma a finalização do Job ["+TIMJ04->CODIGO+"-"+Alltrim(TIMJ04->DESCR) + "] sem ser processado?")
        Return 
    EndIf     

    TIMJ0->(DbGoto(TIMJ04->(Recno())))
    Begin Transaction 
        nRecNew := TIMJ0->(CBCopyRec())
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->(DbDelete())
        TIMJ0->(MsUnlock())    
        TIMJ0->(DbGoto(nRecNew))	       
        TIMJ0->(RecLock("TIMJ0",.F.))
        TIMJ0->STJOB	:= JOB_OK 
        TIMJ0->DTFIN		:= Date()
        TIMJ0->HRFIN		:= Time()
        TIMJ0->(MsUnlock())    
        TIMJ1->(DbSetOrder(1))
        TIMJ1->(DbSeek(TIMJ0->CODIGO))
        While TIMJ1->(! Eof() .and. Left(CODIGO,6) == TIMJ0->CODIGO)  
            TIMJ1->(RecLock("TIMJ1",.F.))
            TIMJ1->STBLOCK:= '4'
            TIMJ1->(MsUnlock())
            TIMJ1->(DbSkip())
        End 
    End Transaction
    TIMJ04->(DbSeek("4")) 
    TIMJ03->(DbSeek("3")) 

    oBrowse:Refresh()                 
Return                               

Static Function Executa(oBrowse)
    If ! MsgYesNo("Confirma a execução manual do Job ["+TIMJ01->CODIGO+"-"+Alltrim(TIMJ01->DESCR) + "]?")
        Return 
    EndIf 
    MsgRun( "Executando o Job... "+CRLF+"["+TIMJ01->CODIGO+"-"+Alltrim(TIMJ01->DESCR) + "]",, {|| U_TIMJExe(TIMJ01->CODIGO, .T.)} )                                       
    
    //Sleep(1000)
    TIMJ01->(DbSeek('1',.T.))
    oBrowse:Refresh()
Return



Static Function MtaTIMJ1(oLbx,aItens2,cCodigo,cStatus,oCodJob,oDescJob)                         
    Local oOK	 	:= LoadBitmap( GetResources(), "LBTICK" 	)  
    Local oNo	 	:= LoadBitmap( GetResources(), "LBNO" 	)
    Local oNote		:= LoadBitmap( GetResources(), "lightblu" 	)
    Local oMenos	:= LoadBitmap( GetResources(), "pmsmenos" 	)
    Local oVermelho := LoadBitmap( GetResources(), "XCLOSE"	)  
    Local oCor              

    oCodJob:SetText(Space(10))
    oDescJob:SetText(Space(100))

    aItens2:={}
    TIMJ0->(DbSetOrder(1))
    TIMJ0->(DbSeek(cCodigo))
    If TIMJ0->STJOB == cStatus         
        oCodJob:SetText(TIMJ0->CODIGO)
        oDescJob:SetText(TIMJ0->DESCR)
        oCodJob:Refresh()
        oDescJob:Refresh()

        TIMJ1->(DbSetOrder(1))
        TIMJ1->(DbSeek(cCodigo))
        While TIMJ1->(! Eof() .and. Left(CODIGO, 10) == cCodigo) 
            If TIMJ1->STBLOCK=='1'
                oCor:=oNo        
            ElseIf TIMJ1->STBLOCK =='2' 	 
                oCor:=oOk 
                If ! Empty(TIMJ1->USERM)
                    oCor:=oNote 
                EndIf         
            ElseIf TIMJ1->STBLOCK =='3' 	 
                oCor:=oVermelho
            ElseIf TIMJ1->STBLOCK =='4' 	 
                oCor:=oMenos
            EndIf
            TIMJ1->(aadd(aItens2,{oCor,DESCR,EMPEXE,FILEXE,USERM,Recno()}))	
            TIMJ1->(DbSkip())                                                                          
        End
    EndIf	
    If Empty(aItens2)
        TIMJ1->(aadd(aItens2,{oNo,Space(100),Space(20),Space(20),Space(20),0}))	
    EndIf
    If oLbx<>NIL
        oLbx:SetArray( aItens2 )
        oLbx:bLine := {|| aEval(aItens2[oLbx:nAt],{|z,w| aItens2[oLbx:nAt,w] } ) }
        oLbx:Refresh()	
    EndIf	
    oCodJob:Refresh()
    oDescJob:Refresh()

Return                    

Static Function MostraLog(nTIMJ1)
    Local oDlg
    Local cMemo    :='' 
    Local oFont 
                        
    TIMJ1->(DbGoto(nTIMJ1))
    cMemo :=TIMJ1->LOGBLOCK

    DEFINE FONT oFont NAME "Mono AS" SIZE 7,14
    DEFINE MSDIALOG oDlg TITLE "Log" From 3,0 to 340,417 PIXEL
        @ 5,5 GET oMemo  VAR cMemo MEMO SIZE 200,145 OF oDlg PIXEL
        oMemo:bRClicked := {||AllwaysTrue()}
        oMemo:oFont:=oFont
        //DEFINE SBUTTON  FROM 153,145 TYPE 1 ACTION (GrvLOG(,cMemo),oDlg:End()) ENABLE OF oDlg PIXEL
        DEFINE SBUTTON  FROM 153,175 TYPE 2 ACTION oDlg:End() ENABLE OF oDlg PIXEL
    ACTIVATE MSDIALOG oDlg CENTER
Return

                 
Static Function MtaTIMJ2(oLbx2,aItens3,nTIMJ1,cStatus)   

    TIMJ1->(DbGoto(nTIMJ1))
    aItens3:={}
    TIMJ0->(DbSetOrder(1))
    TIMJ0->(DbSeek(Left(TIMJ1->CODIGO, 10)))
    If TIMJ0->STJOB == cStatus
        TIMJ2->(DbSetOrder(1))
        TIMJ2->(DbSeek(TIMJ1->CODIGO))
        While TIMJ2->(! Eof() .and. Left(CODIGO, 12) == TIMJ1->CODIGO )
            TIMJ2->(aadd(aItens3,{DESCR,ROTINA,RESULT,Recno()}))	
            TIMJ2->(DbSkip())                                                                          
        End
    EndIf
    If Empty(aItens3)
        TIMJ2->(aadd(aItens3,{Space(80),Space(100),Space(100),0}))	
    EndIf
    If oLbx2<>NIL
        oLbx2:SetArray( aItens3 )
        oLbx2:bLine := {|| aEval(aItens3[oLbx2:nAt],{|z,w| aItens3[oLbx2:nAt,w] } ) }
        oLbx2:Refresh()	
    EndIf	
Return           


Static Function MostraParam(nTIMJ2,oFolder)
    Local oDlg
    Local cMemo    :='' 
    Local oFont 
                        
    TIMJ2->(DbGoto(nTIMJ2))
    cMemo :=TIMJ2->CPAR

    DEFINE FONT oFont NAME "Mono AS" SIZE 7,14
    DEFINE MSDIALOG oDlg TITLE "Parametros" From 3,0 to 340,417 PIXEL
    @ 5,5 GET oMemo  VAR cMemo MEMO SIZE 200,145 OF oDlg PIXEL
    oMemo:bRClicked := {||AllwaysTrue()}
    oMemo:oFont:=oFont   	   
    If oFolder:nOption== 3
        DEFINE SBUTTON  FROM 153,145 TYPE 1 ACTION (GrvParam(cMemo),oDlg:End()) ENABLE OF oDlg PIXEL
    EndIf
    DEFINE SBUTTON  FROM 153,175 TYPE 2 ACTION oDlg:End() ENABLE OF oDlg PIXEL
    ACTIVATE MSDIALOG oDlg CENTER
Return

Static Function GrvParam(cMemo)
    TIMJ1->(RecLock("TIMJ1",.F.))
    TIMJ1->USERM:= __cUSERID
    TIMJ1->(MsUnlock())

    TIMJ2->(RecLock("TIMJ2",.F.))
    TIMJ2->CPAR:= cMemo
    TIMJ2->(MsUnlock())
Return .t.                         
                       
Static Function ImprimeJob(cStatus)    
    Local cCodJob := If(cStatus=="3",TIMJ03->CODIGO,TIMJ04->CODIGO)
    U_TIMJobI(cCodJob)
Return


User Function TIMJobI(cCodJob)
    Local cDesc1		:= "Este programa tem como objetivo imprimir relatorio "
    Local cDesc2		:= "com o conteudo do Job "+cCodJob
    Local cDesc3		:= "Motor Job"
    Local titulo		:= "Motor Job"
    Local nLin			:= 80
    Local Cabec1		:= ""
    Local Cabec2		:= ""
    Local aOrd			:= {}         
    Local cperg			:= ""
    Private lEnd		:= .F.
    Private lAbortPrint	:= .F.
    Private limite		:= 132
    Private tamanho	:= "P"
    Private nTipo		:= 18
    Private aReturn	:= { "Zebrado", 1, "Administracao", 2, 2, 1, "", 1}
    Private nLastKey	:= 0
    Private cbtxt		:= Space(10)
    Private cbcont		:= 00
    Private CONTFL		:= 01
    Private m_pag		:= 01
    Private wnrel		:= "TIMJOBI"
    Private cString 	:= ""

    If Select("TIMJ1") == 0
        U_TIMJOpen("TIMJ1")
    EndIf
    If Select("TIMJ0") == 0
        U_TIMJOpen("TIMJ0")
    EndIf
    If Select("TIMJ2") == 0
        U_TIMJOpen("TIMJ2")
    EndIf

    wnrel := SetPrint(cString,wnrel,cperg,@titulo,cDesc1,cDesc2,cDesc3,.F.,aOrd,.F.,Tamanho,,.F.)

    If nLastKey == 27
        Return
    Endif

    SetDefault(aReturn,cString)

    If nLastKey == 27
    Return
    Endif

    nTipo := If(aReturn[4]==1,15,18)

    RptStatus({|| RunReport(Cabec1,Cabec2,Titulo,nLin,cCodJob) },Titulo)
Return


Static Function RunReport(Cabec1,Cabec2,Titulo,nLin,cCodJob)
    Local aArea		:= GetArea()
    Local aAreaTIMJ0	:= TIMJ0->(GetArea())
    Local aAreaTIMJ1	:= TIMJ1->(GetArea())
    Local aAreaTIMJ2	:= TIMJ2->(GetArea())

    Local nX
    Local cParam
    Local cLog 

    Local cLinha
    Local nPos
    Local cTitCpo
    Local cCampo



    TIMJ0->(DbSetOrder(1))
    If ! TIMJ0->(DbSeek(cCodJob)) 
        MSGAlert("Job:"+cCodJob+" não encontrado")
        Return .f.
    EndIf

    Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
    nLin := 05 

    nLin++
    @ nLin ,000 PSAY "Job: "+TIMJ0->CODIGO+" "+TIMJ0->DESCR
    //@ nLin ,040 PSAY "Status: "+{"Não processado","Processado","Divergente"}[val(TIMJ0->STJOB)]
    nLin++   
    @ nLin ,000 PSAY "Status: "+{"Não processado","Processado","Divergente"}[val(TIMJ0->STJOB)]
    nLin++

    @ nLin ,000 PSAY "Criado em: "+DTOC(TIMJ0->DTCRI)+' '+TIMJ0->HRCRI
    nLin++

    @ nLin ,000 PSAY "Processado em: "+DTOC(TIMJ0->DTFIN)+' '+TIMJ0->HRFIN
    nLin++
    
    TIMJ1->(DbSetOrder(1))
    TIMJ1->(DbSeek(TIMJ0->CODIGO))
    While TIMJ1->(! Eof() .and. Left(CODIGO, 10) == TIMJ0->CODIGO)
    If nLin > 65
        Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
        nLin := 05
        EndIf
                

    nLin++                    
    @ nLin,000 PSAY "Bloco"
    nLin++
    @ nLin ,000 PSAY TIMJ1->CODIGO+"-"+Alltrim(TIMJ1->DESCR)+" ("+{"Não Processado","Processado","Divergencia","Ignorado"}[val(TIMJ1->STBLOCK)]+")"
    //   @ nLin,070 PSAY "Empresa"
    nLin++
        
    //   @ nLin ,000 PSAY TIMJ1->CODIGO+"-"+Alltrim(TIMJ1->DESCR)+" ("+{"Não Processado","Processado","Divergencia","Ignorado"}[val(TIMJ1->STBLOCK)]+")"
    @ nLin,000 PSAY "Empresa"
    //nLin++
        @ nLin ,020 PSAY TIMJ1->(EMPEXE+"/"+FILEXE)
        nLin++

        TIMJ2->(DbSetOrder(1))
        TIMJ2->(DbSeek(TIMJ1->CODIGO))
        While TIMJ2->(! Eof() .and. Left(CODIGO, 12) == TIMJ1->CODIGO)
        If nLin > 65
            Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
            nLin := 05
        Endif      
            nLin++   
            @ nLin,000 PSAY "***  PARAMETROS  *** Cod.Rot: "+TIMJ2->CODIGO 
            nLin++   
            @ nLin,00 PSAY "Rotina: "+Alltrim(TIMJ2->ROTINA)+" "+Alltrim(TIMJ2->RESULT)
        nLin++   
        If ! Empty(TIMJ1->USERM)
            nLin++   
                @ nLin,00 PSAY "Retificado no monitor pelo usuario codigo: "+TIMJ1->USERM
                nLin++   
            EndIf	   
        cParam:= TIMJ2->CPAR
            For nx:= 1 to mlcount(cParam)
            If nLin > 65
                Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
                nLin := 05
            Endif                    
            cLinha := MemoLine(cParam,,nx)
            nPos := At(":=",cLinha)
            cTitCpo:=''
            If nPos > 0  
                SX3->(dbSetOrder(2))
                    cCampo:= Left(cLinha,nPos-1)
                    If SX3->(DbSeek(cCampo))                                  
                    cTitCpo:= Padr(X3TITULO(),20)+'- '   
                EndIf  	
            EndIf
                @ ++nLin, 000 PSAY cTitCpo+cLinha 
            Next 
            ++nLin         
            TIMJ2->(DbSkip())
        End 
        nLin:= 80
        TIMJ1->(DbSkip())
    End

    TIMJ1->(DbSetOrder(1))
    TIMJ1->(DbSeek(TIMJ0->CODIGO))
    While TIMJ1->(! Eof() .and. Left(CODIGO, 106) == TIMJ0->CODIGO)
    cLog :=TIMJ1->LOGBLOCK
    If Empty(cLog)      
        TIMJ1->(DbSkip())
        Loop
    EndIf

    Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
    nLin := 05
    nLin++                     

    @ nLin,000 PSAY "***  L O G   ***" 
    nLin++                     
    @ nLin,000 PSAY "Codigo:"+TIMJ1->CODIGO+" ("+{"Não Processado","Processado","Divergencia","Ignorado"}[val(TIMJ1->STBLOCK)]+")"
    nLin++

        For nx:= 1 to mlcount(cLog)
        If nLin > 65
            Cabec(Titulo,Cabec1,Cabec2,wnrel,Tamanho,nTipo)
            nLin := 05
        Endif  			
            @ ++nLin, 000 PSAY MemoLine(cLog,,nx) 
        Next
    TIMJ1->(DbSkip())
    End
    

    SET DEVICE TO SCREEN

    If aReturn[5]==1
    dbCommitAll()
    SET PRINTER TO
    OurSpool(wnrel)
    Endif

    MS_FLUSH()

    RestArea(aAreaTIMJ2)    
    RestArea(aAreaTIMJ1)
    RestArea(aAreaTIMJ0)
    RestArea(aArea)
Return




User Function TIMJobS(cCodEmp,cFilEmp)
    Default cCodEmp	:= '99'
    Default cFilEmp	:='01'

    Private __cInternet := 'AUTOMATICO'

    RpcSetEnv (cCodEmp, cFilEmp,,,'FAT',,)
    FWMonitorMsg("Motor Job (Serviço) " + cCodEmp + "/" + cFilEmp) 

    If Select("TIMJ0") == 0 
        U_TIMJOpen("TIMJ0")
    EndIf
    If Select("TIMJ1") == 0 
        U_TIMJOpen("TIMJ1")
    EndIf
    If Select("TIMJ2") == 0
        U_TIMJOpen("TIMJ2")
    EndIf


    JobOnLine()
    While ! KillApp() 
        If File('timjobs.off') 
            Exit
        EndIf
        TIMJ0->(DBSetOrder(2))
        If TIMJ0->(DBSeek('1')) 
            U_TIMJExe(TIMJ0->CODIGO,.T.)    
        EndIf
        Sleep(100)
    EndDo  
    JobOffLine()

    RpcClearEnv()
Return     


Static Function JobOnLine()
    Local nC:= 0
    __nSem := -1
    While __nSem  < 0
    __nSem  := MSFCreate("timjobs.on") 
    IF  __nSem  < 0                  
        SLeep(50)             
        nC++
        If nC == 60
            nC := 0
            conout('Semaforo fechado ')
        EndIf                      
    Endif
    End              
    FWrite(__nSem,"") 
    conout('Motor Job On Line ' + time())
Return

Static Function JobOffLine()
	Fclose(__nSem)
	FErase("timjobs.on")
	FErase("timjobs.off") 
	conout('Motor Job Off Line ' + time())
Return         

          

/*
========================================================================
Funcao que executa job em outra thread
============================================================================
*/

User Function TIMJExe(cCodJob, lWait)
    Default lWait:= .F.   // se .t. o programa espera o termino.

    CheckBloq()

    StartJob("U_TIMJEx2", GetEnvServer(), lWait, cCodJob, cEmpAnt, cFilAnt)
    //TIMJEx2(cCodJob,cEmpAnt,cFilAnt)    // UTILIZAR PARA DEBUG
Return

Static Function CheckBloq()
    Local aRecJob := {}
    Local aCodJob := {}
    Local nx 

    If Select("TIMJ0") == 0 
        U_TIMJOpen("TIMJ0")
    EndIf

    TIMJ0->(DBSetOrder(2))
    If ! TIMJ0->(DBSeek('5'))
        TIMJ0->(DBSetOrder(1))
        Return 
    EndIf 

    While ! LockByName("tiblqctrlmjob", .F., .F., LS_GetTotal(1) < 0)
        Sleep(500)
    EndDo

 
    While TIMJ0->(! Eof() .and. STJOB == JOB_BLQ)
        aadd(aRecJob, TIMJ0->(Recno()))
        TIMJ0->(DbSkip())
    End 

    For nx:= 1 to len(aRecJob)
        TIMJ0->(DbGoto(aRecJob[nx]))    
        TIMJ0->(RecLock("TIMJ0", .F.))
        TIMJ0->STJOB := JOB_PENDENTE
        TIMJ0->(MsUnLock())
        aadd(aCodJob, TIMJ0->CODIGO)
    Next 

    For nx:= 1 to len(aCodJob)
        cCodJob := aCodJob[nx]
        StartJob("U_TIMJEx2", GetEnvServer(), .f., cCodJob, cEmpAnt, cFilAnt)
    Next 
 
    UnlockByName("tiblqctrlmjob", .F., .F., LS_GetTotal(1) < 0)
Return 

/*
========================================================================
Funcao que executa job na mesma thread
============================================================================
*/

User Function TIMJEx2(cCodJob, cEmpx,cFilx) 
    Local nx
    Private oJob

    If cEmpx <> NIL
        SetsDefault()
        CriaPublica()
        RpcClearEnv()

        RPCSetType(3) 
        RpcSetEnv(cEmpx,cFilx, , ,'FAT',,)
        FWMonitorMsg("Execução Motor Job: " + cCodJob)
    EndIf 

    If Select("TIMJ0") == 0 
        U_TIMJOpen("TIMJ0")
    EndIf
    If Select("TIMJ1") == 0 
        U_TIMJOpen("TIMJ1")
    EndIf
    If Select("TIMJ2") == 0
        U_TIMJOpen("TIMJ2")
    EndIf

    oJob:= TIMJob():New(cCodJob)
    For nX:= 1 to len(oJob:aSeqBlock)
        TIMJBlock():New(oJob:aSeqBlock[nX],oJob) 
    Next   
    oJob:Execute()  
Return  
                     
User Function TIMJOpen(cArq,cAlias)
    Local nI
    Local nJ
    Local cArquivo := ""
    Local cDriver  := "TOPCONN" //"CTREECDX"
    Local cIndex   := ""
    Local aStruct  := {}
    Local aTables  := {}
    DEFAULT cAlias := cArq
    

    If cArq == "TIMJ0" //-- JOB
        aStruct := {}
        Aadd(aStruct,{"STJOB" ,"C",	1	,0})     //Status 1=Pendente, 2=Execução, 3=Ok, 4=Erro == antes era 1=Pendente, 2=Ok, 3=Divergente
        Aadd(aStruct,{"PRIORI","C",	16	,0})
        Aadd(aStruct,{"CODIGO","C",	10	,0})
        Aadd(aStruct,{"DESCR" ,"C",	60	,0})
        Aadd(aStruct,{"ORIGEM","C",	20	,0})
        Aadd(aStruct,{"CHAVE" ,"C",	100 ,0})
        Aadd(aStruct,{"USERM" ,"C",	6	,0})
        Aadd(aStruct,{"EMPORI","C",	2	,0})
        Aadd(aStruct,{"FILORI","C",	FWSizeFilial()	,0})
        Aadd(aStruct,{"DTCRI" ,"D",	8	,0})
        Aadd(aStruct,{"HRCRI" ,"C",	8	,0})
        Aadd(aStruct,{"DTINI" ,"D",	8	,0})
        Aadd(aStruct,{"HRINI" ,"C",	8	,0})
        Aadd(aStruct,{"DTFIN" ,"D",	8	,0})
        Aadd(aStruct,{"HRFIN" ,"C",	8	,0})
        Aadd(aStruct,{"TMPEXE","N",	8	,0})
        //Aadd(aStruct,{"LOG"   ,"M",	80	,0})
        Aadd( aTables, {cArq, cAlias, {"CODIGO","STJOB+PRIORI","ORIGEM+CHAVE"}, aClone(aStruct) } )

    ElseIf cArq == "TIMJ1" //-- BLOCO
        aStruct := {}
        Aadd(aStruct,{"STBLOCK" ,"C",	1	,0})  
        Aadd(aStruct,{"CODIGO","C",	12	,0})
        Aadd(aStruct,{"DESCR"  ,"C",	60	,0})
        Aadd(aStruct,{"EMPEXE","C",	2	,0})
        Aadd(aStruct,{"FILEXE","C",	FWSizeFilial()	,0})
        Aadd(aStruct,{"LOGBLOCK"   ,"M",	80	,0})
        Aadd(aStruct,{"USERM"  ,"C",	6	,0})
        Aadd( aTables, {cArq, cAlias, {"CODIGO"}, aClone(aStruct) } )

    ElseIf cArq == "TIMJ2" //-- ROTINAS
        aStruct := {}    
        Aadd(aStruct,{"CODIGO","C",	14	,0})
        Aadd(aStruct,{"DESCR"  ,"C",	60	,0})
        Aadd(aStruct,{"ROTINA","C",	250,0})
        Aadd(aStruct,{"CPAR"	 ,"M",	80	,0})
        Aadd(aStruct,{"MODULO","N",	2	,0})
        Aadd(aStruct,{"RESULT","C",	80	,0})
        Aadd( aTables, { cArq, cAlias, {"CODIGO"}, aClone(aStruct) } ) 

    EndIf	

    For nI := 1 To Len(aTables)
        cAlias  := aTables[nI,2]
        aStruct := aClone(aTables[nI,4])
        cArquivo := RetArq(cDriver,cArq,.T.)

        If cEmpAnt <> "00"
            cArquivo += cEmpAnt
        EndIf 

        If !MsFile(cArquivo,,cDriver)
            DbCreate(cArquivo,aStruct,cDriver)
        EndIf

        If MsOpenDbf(.T.,cDriver,cArquivo,cAlias,.T.,.F.,.F.,.F.)
            DbSelectArea(cAlias)
            lReOpen := !MsFile(cArquivo,cArquivo+"1",cDriver)
            For nJ := 1 To Len(aTables[nI,3])
                cIndex := cArquivo+Str(nJ,1)
                MsOpenIdx(cIndex,aTables[nI,3,nJ],.T.,.F.,,cArquivo)
            Next nJ

            If lReOpen
                DbCloseArea()
                MsOpenDbf(.T.,cDriver,cArquivo,cAlias,.T.,.F.,.F.,.F.)
                DbSelectArea(cAlias)
                For nJ := 1 To Len(aTables[nI,3])
                    cIndex := cArquivo+Str(nJ,1)
                    MsOpenIdx(cIndex,aTables[nI,3,nJ],.T.,.F.,,cArquivo)
                Next nJ
            EndIf
            DbSetOrder(1)
        EndIf
    Next nI

Return .t. 



User Function TstMJOb1()                                                                            
    Local aMATA030 := {}  
    Local aMata010 := {}                                                               
    Local oJob		
    Local oBloco1	
    Local cParam	:= ""                         
    Local nNumModu	:= 5 		// numero do modulo que sera executado
    Local xcEmp  := cEmpAnt
    Local xFil   := cFilAnt

    //RpcSetEnv("99","01", , ,'FAT',,)   

    aMATA030:={     {"A1_COD"       ,"XXX001"           ,Nil},; // Codigo       C 06
                    {"A1_LOJA"      ,"01"               ,Nil},; // Loja         C 02
                    {"A1_PESSOA"    ,"F"                ,Nil},; // Pessoa       C 02
                    {"A1_NOME"      ,"INC. AUTOMATICO"  ,Nil},; // Nome         C 40
                    {"A1_NREDUZ"    ,"AUTOMATICO"		,Nil},; // Nome reduz.  C 20
                    {"A1_TIPO"      ,"R"				,Nil},; // Tipo         C 01 //R Revendedor
                    {"A1_END"       ,"RUA AUTOMATICA"	,Nil},; // Endereco     C 40
                    {"A1_MUN"       ,"SAO AUTOMATICO"	,Nil},; // Cidade       C 15
                    {"A1_EST"       ,"SP"				,Nil}}  // Estado       C 02   

    aMata010:={  {"B1_COD"     ,"999999999999999",Nil},;
 				 {"B1_DESC"    ,"Teste"        ,Nil},;
				 {"B1_TIPO"    ,"PA"           ,Nil},; 
				 {"B1_UM"      ,"UN"           ,Nil},; 
				 {"B1_LOCPAD"  ,"01"           ,Nil},; 
				 {"B1_PICM"    ,0              ,Nil},; 
				 {"B1_IPI"     ,0              ,Nil},; 
				 {"B1_PRV1"    ,100            ,Nil},; 
				 {"B1_TIPOCQ"  ,"M"            ,Nil},; 
				 {"B1_CONTRAT" ,"N"            ,Nil},; 
				 {"B1_LOCALIZ" ,"N"            ,Nil},; 
				 {"B1_CODBAR"  ,'123456'       ,Nil},; 
				 {"B1_IRRF"    ,"N"            ,Nil},; 
				 {"B1_CONTSOC" ,"N"            ,Nil},; 
				 {"B1_MRP"     ,"N"            ,Nil}} 
    
    
    
    oJob		:=  TIMJob():New(,"Teste Motor Job ")
    oBloco1	    :=  TIMJBlock():New(,oJob,xcEmp,xFil, "Bloco 1 ")
    
    //cParam 	    :=	U_MJAuto2Str(3,aMATA030)
    //oBloco1:Add("Cadastro de Cliente","MATA030",cParam,,nNumModu) 
        
    cParam 	    :=	U_MJAuto2Str(3,aMATA010)
    oBloco1:Add("Cadastro de produto","MATA010",cParam,,nNumModu) 

    /*oBloco2	:= TIMJBlock():New(,oJob,xcEmp,cFilAnt,"Bloco 2 ")
    cParam 	:= U_MJAuto2Str(3,aMATA030)
    oBloco2:Add("Cadastro de Cliente","MATA030",cParam,,nNumModu) 

    oBloco3	:= TIMJBlock():New(,oJob,"99","03","Bloco 3 ")
    cParam 	:= U_MJAuto2Str(3,aMATA030)
    oBloco3:Add("Cadastro de Cliente","MATA030",cParam,,nNumModu) 
    */    

    oJob:SaveDB()   

    U_TIMJEXE(oJob:cCodJob, .T.)
    oJob:LoadDB()  

    //oJob:Execute()

    If oJob:cStatus <> "2"
        cErro:=oJob:cLogJob 
        Return .f.
    EndIf

Return .t.


User Function TstMJOb2()                                                                            
    Local oJob		
    Local oBloco1	
    Local cParam	:= ""                         
    Local nNumModu	:= 5 		// numero do modulo que sera executado
    Local xcEmp     := cEmpAnt
    Local xFil      := cFilAnt
    Local aNovoItem := {}
    Local aCab      := {}
    
    
    oJob		:=  TIMJob():New(, "Teste Motor Job ")
    oBloco1	    :=  TIMJBlock():New(, oJob, xcEmp, xFil, "Bloco 1 ")


        aProposta := {  {"CNB_PROPOS","0001010"           ,Nil},; 
                        {"CNB_PRODUT","CODPRODUTO"        ,Nil}}
        cParam 	    :=	U_MJAuto2Str(3, aProposta)
        oBloco1:Add("Cadastro de proposta" , "U_CADPROP()", cParam,, nNumModu) 


        aNovoItem := {  {"CNB_CONTRA","0001010"       ,Nil},;
                        {"CNB_REVISA","000001"        ,Nil},; 
                        {"CNB_NUMERO","000001"        ,Nil},;
                        {"CNB_ITEM"  ,"000002"        ,Nil}}
        cParam 	    :=	U_MJAuto2Str(3, aNovoItem)
        oBloco1:Add("Novo item de contrato", "U_NOVOITEM()", cParam,, nNumModu) 
        
        
    
    oBloco2	:= TIMJBlock():New(,oJob, xcEmp, xFil, "Bloco 2 ")


        aCab:={{"C5_NUM"    ,"000111"    ,Nil},; // Numero do pedido
                {"C5_CLIENTE","999999"    ,Nil},; // Codigo do cliente
                {"C5_LOJAENT","00"        ,Nil},; // Loja para entrada
                {"C5_LOJACLI","00"        ,Nil},; // Loja do cliente
                {"C5_EMISSAO",dDatabase   ,Nil},; // Data de emissao
                {"C5_TIPO"   ,"N"         ,Nil},; // Tipo de pedido
                {"C5_TABELA" ,"7"        ,Nil},; // Codigo da Tabela de Preco
                {"C5_CONDPAG","001"       ,Nil},; // Codigo da condicao de pagamanto*
                {"C5_DESC1"  ,0           ,Nil},; // Percentual de Desconto
                {"C5_INCISS" ,"N"         ,Nil},; // ISS Incluso
                {"C5_TIPLIB" ,"1"         ,Nil},; // Tipo de Liberacao
                {"C5_MOEDA"  ,1           ,Nil},; // Moeda
                {"C5_LIBEROK","S"         ,Nil}} // Liberacao Total
        //Items
        aItens:={{"C6_NUM"    ,"000111"           ,Nil},; // Numero do Pedido
                    {"C6_ITEM"   ,"01"              ,Nil},; // Numero do Item no Pedido
                    {"C6_PRODUTO","999999999999999" ,Nil},; // Codigo do Produto
                    {"C6_QTDVEN" ,1                 ,Nil},; // Quantidade Vendida
                    {"C6_PRUNIT"  ,0                ,Nil},; // PRECO DE LISTA
                    {"C6_PRCVEN" ,100               ,Nil},; // Preco Unitario Liquido
                    {"C6_VALOR"  ,100               ,Nil},; // Valor Total do Item
                    {"C6_ENTREG" ,dDataBase         ,Nil},; // Data da Entrega
                    {"C6_UM"     ,"UN"              ,Nil},; // Unidade de Medida Primar.
                    {"C6_TES"    ,"510"             ,Nil},; // Tipo de Entrada/Saida do Item
                    {"C6_LOCAL"  ,"01"              ,Nil},; // Almoxarifado
                    {"C6_DESCONT",1                 ,Nil},; // Percentual de Desconto
                    {"C6_COMIS1" ,0                 ,Nil},; // Comissao Vendedor
                    {"C6_CLI"    ,"000001"          ,Nil},; // Cliente
                    {"C6_LOJA"   ,"00"              ,Nil},; // Loja do Cliente
                    {"C6_QTDEMP" ,1                 ,Nil},; // Quantidade Empenhada
                    {"C6_QTDLIB" ,1                 ,Nil}}  // Quantidade Liberada
        
        cParam 	:= U_MJAuto2Str(3, aCab, {aItens})

        oBloco2:Add("Geração pedido","U_xMATA410()",cParam,, nNumModu) 

    oJob:SaveDB()   

    /*U_TIMJEXE(oJob:cCodJob, .T.)
    oJob:LoadDB()  

    //oJob:Execute()

    If oJob:cStatus <> "2"
        cErro:=oJob:cLogJob 
        Return .f.
    EndIf
    */

    
Return .t.


User Function CADPROP()
    Local cParam := __cParam
    Local aProposta := {}

    aProposta := U_MJStr2HAuto(cParam)

    Sleep(1000 * 20)

Return ""


User Function NOVOITEM()
    Local cParam := __cParam
    Local aItem  :=  {}
    
    aItem := U_MJStr2HAuto(cParam)

Return ""

User Function xMATA410()
    Local cParam := __cParam
    Local nOpcao := 0
    Local aCab   := {}
    Local aItens := {}

    nOpcao	:= U_MJStr2OpAuto(cParam)
	aCab	:= U_MJStr2HAuto(cParam)
	aItens	:= U_MJStr2IAuto(cParam)



Return ""
