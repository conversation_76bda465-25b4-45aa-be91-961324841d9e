#Include "totvs.ch"
#include 'tlpp-core.th'

using namespace ti.empresa.manager

namespace ti.empresa.manager.teste

/*/{Protheus.doc} ExemploUsoTIEmpresaManager
Exemplos de uso da classe TIEmpresaManager
@type function
@version P12
<AUTHOR>
@since 01/08/2025
/*/
User Function ExemploUsoTIEmpresaManager()

    Local oEmpManager := Nil
    Local aEmpInfo    := {}
    Local aFilInfo    := {}
    Local uResult     := Nil
    Local cCNPJ       := "12345678000195"
    Local cCodEmp     := "01"
    Local cFilialEmpresa     := "0001"

    // Exemplo 1: Inicialização da classe
    ConOut("=== Exemplo 1: Inicialização ===")
    oEmpManager := TIEmpresaManager():New()
    
    If oEmpManager != Nil
        ConOut("Classe inicializada com sucesso!")
        ConOut("Empresa original: " + oEmpManager:GetCurrentEmpresa())
        ConOut("Filial original: " + oEmpManager:GetCurrentFilial())
    EndIf

    // Exemplo 2: Buscar informações de empresa por CNPJ
    ConOut("=== Exemplo 2: Buscar por CNPJ ===")
    aEmpInfo := oEmpManager:GetEmpresaInfo(cCNPJ, "CNPJ")
    
    If Len(aEmpInfo) > 0
        ConOut("Empresa encontrada:")
        For nI := 1 To Len(aEmpInfo)
            ConOut("  " + aEmpInfo[nI][1] + ": " + aEmpInfo[nI][2])
        Next nI
    Else
        ConOut("Empresa não encontrada para CNPJ: " + cCNPJ)
    EndIf

    // Exemplo 3: Buscar informações de empresa por código
    ConOut("=== Exemplo 3: Buscar por Código ===")
    aEmpInfo := oEmpManager:GetEmpresaInfo(cCodEmp, "CODIGO")
    
    If Len(aEmpInfo) > 0
        ConOut("Empresa encontrada por código:")
        For nI := 1 To Len(aEmpInfo)
            ConOut("  " + aEmpInfo[nI][1] + ": " + aEmpInfo[nI][2])
        Next nI
    EndIf

    // Exemplo 4: Buscar informações de filial específica
    ConOut("=== Exemplo 4: Buscar Filial ===")
    aFilInfo := oEmpManager:GetFilialInfo(cCodEmp, cFilialEmpresa)
    
    If Len(aFilInfo) > 0
        ConOut("Filial encontrada:")
        For nI := 1 To Len(aFilInfo)
            ConOut("  " + aFilInfo[nI][1] + ": " + aFilInfo[nI][2])
        Next nI
    EndIf

    // Exemplo 5: Conectar a uma empresa específica
    ConOut("=== Exemplo 5: Conectar a Empresa ===")
    If oEmpManager:ConnectToEmpresa(cCodEmp, "CODIGO", cFilialEmpresa)
        ConOut("Conectado com sucesso!")
        ConOut("Empresa atual: " + oEmpManager:GetCurrentEmpresa())
        ConOut("Filial atual: " + oEmpManager:GetCurrentFilial())
        ConOut("Status conectado: " + IIf(oEmpManager:IsConnected(), "SIM", "NÃO"))
        
        // Restaurar conexão original
        oEmpManager:RestoreOriginalConnection()
        ConOut("Conexão original restaurada")
    Else
        ConOut("Erro ao conectar à empresa")
    EndIf

    // Exemplo 6: Executar código em empresa específica
    ConOut("=== Exemplo 6: Executar em Empresa ===")
    uResult := oEmpManager:ExecuteInEmpresa(cCodEmp, "CODIGO", cFilialEmpresa, {|| ExemploConsultaEmpresa()})
    
    If uResult != Nil
        ConOut("Resultado da execução: " + cValToChar(uResult))
    Else
        ConOut("Erro na execução ou empresa não encontrada")
    EndIf

    // Exemplo 7: Listar todas as empresas
    ConOut("=== Exemplo 7: Listar Empresas ===")
    aEmpresas := oEmpManager:GetAllEmpresas()
    ConOut("Total de empresas: " + cValToChar(Len(aEmpresas)))
    
    For nI := 1 To Min(3, Len(aEmpresas)) // Mostra apenas as 3 primeiras
        ConOut("Empresa " + cValToChar(nI) + ": " + aEmpresas[nI][1] + "/" + aEmpresas[nI][2] + " - " + AllTrim(aEmpresas[nI][6]))
    Next nI

    // Exemplo 8: Destruir a instância
    ConOut("=== Exemplo 8: Finalização ===")
    oEmpManager:Destroy()
    oEmpManager := Nil
    ConOut("Classe finalizada")

Return

/*/{Protheus.doc} ExemploConsultaEmpresa
Exemplo de função que será executada em contexto de empresa específica
@type function
@return character, Resultado da consulta
/*/
Static Function ExemploConsultaEmpresa()

    Local cResult := ""
    Local cQuery  := ""
    Local cAlias  := GetNextAlias()

    // Exemplo: Consultar quantidade de clientes na empresa atual
    cQuery := "SELECT COUNT(*) AS TOTAL FROM " + RetSqlName("SA1") + " "
    cQuery += "WHERE A1_FILIAL = '" + xFilial("SA1") + "' "
    cQuery += "AND D_E_L_E_T_ = ' '"

    cQuery := ChangeQuery(cQuery)
    DbUseArea(.T., "TOPCONN", TCGenQry(,, cQuery), cAlias, .F., .T.)

    If (cAlias)->(!Eof())
        cResult := "Total de clientes: " + cValToChar((cAlias)->TOTAL)
    Else
        cResult := "Nenhum cliente encontrado"
    EndIf

    (cAlias)->(DbCloseArea())

Return cResult

/*/{Protheus.doc} ExemploWebServiceComEmpresa
Exemplo de como usar a classe em um WebService
@type function
@param cCNPJEmpresa, character, CNPJ da empresa
@return logical, Sucesso na operação
/*/
User Function ExemploWebServiceComEmpresa(cCNPJEmpresa)

    Local oEmpManager := TIEmpresaManager():New()
    Local lSuccess    := .F.
    Local aResult     := {}

    // Executa operação na empresa específica sem alterar cEmpAnt
    aResult := oEmpManager:ExecuteInEmpresa(cCNPJEmpresa, "CNPJ", "", {|| ConsultarDadosEmpresa()})

    If aResult != Nil .And. Len(aResult) > 0
        lSuccess := .T.
        // Processa resultado...
    EndIf

    // Limpa recursos
    oEmpManager:Destroy()

Return lSuccess

/*/{Protheus.doc} ConsultarDadosEmpresa
Consulta dados específicos da empresa
@type function
@return array, Dados da empresa
/*/
Static Function ConsultarDadosEmpresa()

    Local aResult := {}
    
    // Exemplo: buscar dados da empresa atual
    DbSelectArea("SM0")
    SM0->(DbSetOrder(1))
    
    If SM0->(DbSeek(cEmpAnt + cFilAnt))
        aResult := {;
            {"EMPRESA", cEmpAnt},;
            {"FILIAL",  cFilAnt},;
            {"NOME",    AllTrim(SM0->M0_NOMECOM)},;
            {"CNPJ",    AllTrim(SM0->M0_CGC)};
        }
    EndIf

Return aResult

/*/{Protheus.doc} ExemploOtimizacaoTCRMS078
Exemplo de como refatorar o TCRMS078 usando a classe
@type function
@param oWS, object, Objeto do WebService
@return logical, Sucesso na operação
/*/
User Function ExemploOtimizacaoTCRMS078(oWS)

    Local oEmpManager := TIEmpresaManager():New()
    Local cCNPJURL    := Upper(AllTrim(oWS:aURLParms[1]))
    Local lSuccess    := .F.
    Local aResult     := {}

    // Substitui todo o código duplicado por uma única chamada
    aResult := oEmpManager:ExecuteInEmpresa(cCNPJURL, "CNPJ", "", {|| ProcessarDadosWS(oWS)})

    If aResult != Nil
        lSuccess := .T.
        // Define resposta do WebService
        oWS:SetResponse(aResult)
    Else
        SetRestFault(400, "Empresa não encontrada ou erro no processamento")
    EndIf

    // Limpa recursos
    oEmpManager:Destroy()

Return lSuccess

/*/{Protheus.doc} ProcessarDadosWS
Processa dados do WebService no contexto da empresa
@type function
@param oWS, object, Objeto do WebService
@return variant, Resultado do processamento
/*/
Static Function ProcessarDadosWS(oWS)

    Local oResponse := JsonObject():New()
    
    // Aqui o código já está executando no contexto da empresa correta
    // Não precisa alterar cEmpAnt nem fazer FwLoadSM0()
    
    oResponse["empresa"] := cEmpAnt
    oResponse["filial"]  := cFilAnt
    oResponse["dados"]   := "Processamento realizado com sucesso"
    
Return oResponse:ToJson()

/*/{Protheus.doc} ExemploTratamentoErro
Exemplo de tratamento de erro com a classe
@type function
@return logical, Sucesso na operação
/*/
User Function ExemploTratamentoErro()

    Local oEmpManager := TIEmpresaManager():New()
    Local lSuccess    := .F.

    // Tenta conectar a uma empresa inexistente
    If !oEmpManager:ConnectToEmpresa("99999999000199", "CNPJ")
        ConOut("Erro esperado: Empresa não encontrada")
    EndIf

    // Tenta executar em empresa inexistente
    uResult := oEmpManager:ExecuteInEmpresa("99999999000199", "CNPJ", "", {|| "Teste"})
    
    If uResult == Nil
        ConOut("Erro esperado: Execução falhou")
    EndIf

    // Sempre restaura e limpa
    oEmpManager:Destroy()

Return lSuccess
